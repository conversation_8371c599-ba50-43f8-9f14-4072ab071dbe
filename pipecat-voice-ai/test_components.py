#!/usr/bin/env python3

"""
组件测试脚本 - 验证各个服务是否正常工作

测试STT、LLM、TTS服务的基本功能
"""

import asyncio
import os
import sys

from dotenv import load_dotenv
from loguru import logger

from pipecat.services.openai.stt import OpenAISTTService
from pipecat.services.openai.llm import OpenAILLMService
from pipecat.services.groq.tts import GroqTTSService
from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext
from pipecat.transcriptions.language import Language

load_dotenv(override=True)


async def test_llm():
    """测试LLM服务"""
    logger.info("🧠 测试Groq LLM服务...")
    
    try:
        llm = OpenAILLMService(
            api_key=os.getenv("GROQ_API_KEY"),
            base_url="https://api.groq.com/openai/v1",
            model="llama-3.1-8b-instant",
        )
        
        # 创建上下文
        context = OpenAILLMContext(
            messages=[
                {
                    "role": "system",
                    "content": "你是一个友好的中文AI助手。请用简洁的中文回答问题。"
                },
                {
                    "role": "user", 
                    "content": "你好，请简单介绍一下自己。"
                }
            ]
        )
        
        logger.info("发送测试消息到Groq LLM...")
        # 注意：这里只是测试服务初始化，实际的LLM调用需要在Pipeline中进行
        logger.success("✅ Groq LLM服务初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ Groq LLM服务测试失败: {e}")
        return False


async def test_stt():
    """测试STT服务"""
    logger.info("🎤 测试OpenAI STT服务...")
    
    try:
        stt = OpenAISTTService(
            api_key=os.getenv("GROQ_API_KEY"),
            base_url="https://api.groq.com/openai/v1",
            model="whisper-large-v3",
            language=Language.ZH,
        )
        
        logger.success("✅ OpenAI STT服务初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ OpenAI STT服务测试失败: {e}")
        return False


async def test_tts():
    """测试TTS服务"""
    logger.info("🔊 测试Groq TTS服务...")
    
    try:
        tts = GroqTTSService(
            api_key=os.getenv("GROQ_API_KEY"),
            voice="alloy",
        )
        
        logger.success("✅ Groq TTS服务初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ Groq TTS服务测试失败: {e}")
        return False


async def test_api_connection():
    """测试API连接"""
    logger.info("🌐 测试Groq API连接...")
    
    try:
        from openai import AsyncOpenAI
        
        client = AsyncOpenAI(
            api_key=os.getenv("GROQ_API_KEY"),
            base_url="https://api.groq.com/openai/v1"
        )
        
        # 测试简单的聊天完成
        response = await client.chat.completions.create(
            model="llama3-8b-8192",  # 使用Groq支持的模型名称
            messages=[
                {"role": "user", "content": "Hello, just say 'API test successful' in Chinese."}
            ],
            max_tokens=50
        )
        
        reply = response.choices[0].message.content.strip()
        logger.info(f"API回复: {reply}")
        logger.success("✅ Groq API连接测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ Groq API连接测试失败: {e}")
        return False


async def test_environment():
    """测试环境配置"""
    logger.info("⚙️ 检查环境配置...")
    
    # 检查API密钥
    groq_key = os.getenv("GROQ_API_KEY")
    if not groq_key:
        logger.error("❌ 缺少GROQ_API_KEY环境变量")
        return False
    
    if groq_key.startswith("gsk_") and len(groq_key) > 50:
        logger.success("✅ Groq API密钥格式正确")
    else:
        logger.warning("⚠️ Groq API密钥格式可能不正确")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version >= (3, 8):
        logger.success(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        logger.error(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        return False
    
    return True


async def main():
    """主测试函数"""
    logger.info("🚀 开始组件测试...")
    logger.info("=" * 50)
    
    results = []
    
    # 测试环境
    results.append(await test_environment())
    
    # 测试API连接
    results.append(await test_api_connection())
    
    # 测试各个服务
    results.append(await test_llm())
    results.append(await test_stt())
    results.append(await test_tts())
    
    logger.info("=" * 50)
    
    # 汇总结果
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        logger.success(f"🎉 所有测试通过! ({passed}/{total})")
        logger.info("✅ 语音机器人组件准备就绪，可以启动服务")
        logger.info("💡 运行命令: python 01-say-one-thing.py --transport webrtc --host 0.0.0.0 --port 7860")
    else:
        logger.error(f"❌ 部分测试失败 ({passed}/{total})")
        logger.info("🔧 请检查失败的组件配置")
    
    return passed == total


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        sys.exit(1)
