#!/usr/bin/env python3

"""
自定义Edge TTS服务测试

创建自定义的Edge TTS服务集成到Pipecat
"""

import asyncio
import io
import os
import sys
import wave

import edge_tts
from dotenv import load_dotenv
from loguru import logger

from pipecat.frames.frames import EndFrame, TTSSpeakFrame, TTSAudioRawFrame, Frame
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineTask
from pipecat.services.tts_service import TTSService
from pipecat.transports.local.audio import LocalAudioTransport, LocalAudioTransportParams

load_dotenv(override=True)


class CustomEdgeTTSService(TTSService):
    """自定义Edge TTS服务"""

    def __init__(self, voice: str = "zh-CN-XiaoxiaoNeural", **kwargs):
        super().__init__(**kwargs)
        self.voice = voice

    async def run_tts(self, text: str) -> bytes:
        """运行TTS合成"""
        try:
            logger.info(f"正在合成语音: {text}")

            # 使用Edge TTS生成语音
            communicate = edge_tts.Communicate(text, self.voice)
            audio_data = b""

            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_data += chunk["data"]

            if audio_data:
                logger.info(f"语音合成完成，数据长度: {len(audio_data)} 字节")

                # 转换MP3到PCM
                try:
                    from pydub import AudioSegment

                    # 从MP3数据创建AudioSegment
                    audio = AudioSegment.from_mp3(io.BytesIO(audio_data))

                    # 转换为16kHz单声道PCM
                    audio = audio.set_frame_rate(16000).set_channels(1)

                    # 获取原始PCM数据
                    pcm_data = audio.raw_data

                    logger.info(f"PCM转换完成，数据长度: {len(pcm_data)} 字节")
                    return pcm_data

                except Exception as e:
                    logger.error(f"音频转换错误: {e}")
                    # 如果转换失败，返回原始数据
                    return audio_data

            else:
                logger.warning("没有生成音频数据")
                return b""

        except Exception as e:
            logger.error(f"Edge TTS错误: {e}")
            return b""


async def main():
    """主函数"""
    logger.info("启动自定义Edge TTS测试")

    # 配置本地音频传输
    transport = LocalAudioTransport(
        params=LocalAudioTransportParams(
            audio_out_enabled=True,
            audio_in_enabled=False
        )
    )

    # 配置自定义Edge TTS服务
    tts = CustomEdgeTTSService(voice="zh-CN-XiaoxiaoNeural")

    # 创建管道
    pipeline = Pipeline([tts, transport.output()])
    task = PipelineTask(pipeline)

    # 运行机器人
    runner = PipelineRunner()
    
    logger.info("开始播放测试音频...")
    
    # 直接播放测试消息
    await task.queue_frames([
        TTSSpeakFrame("你好！这是自定义Edge TTS语音合成测试。如果你能听到这段中文语音，说明我们的自定义服务工作正常！"), 
        EndFrame()
    ])
    
    await runner.run(task)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试已停止")
    except Exception as e:
        logger.error(f"测试错误: {e}")
        sys.exit(1)
