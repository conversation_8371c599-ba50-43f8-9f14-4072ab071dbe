2025-07-31 15:17:34.715 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
INFO:     Started server process [58463]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     <PERSON>vicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/9hJ15erLSSJfyuPCcyAo
INFO:     ***************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 15:17:50.932 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:17:59.769 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-07-31 15:17:59.934 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
Invoking rtc_set_log_level, log enabled:1, level:4
Configuring the webrtc log level.
2025-07-31 15:18:00.089 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> DailyInputTransport#0
2025-07-31 15:18:00.089 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyInputTransport#0 -> RTVIProcessor#0
2025-07-31 15:18:00.089 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking RTVIProcessor#0 -> OpenAIUserContextAggregator#0
2025-07-31 15:18:00.089 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> OpenAILLMService#0
2025-07-31 15:18:00.089 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAILLMService#0 -> ElevenLabsTTSService#0
2025-07-31 15:18:00.089 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking ElevenLabsTTSService#0 -> TalkingAnimation#0
2025-07-31 15:18:00.089 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking TalkingAnimation#0 -> DailyOutputTransport#0
2025-07-31 15:18:00.089 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-07-31 15:18:00.089 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-07-31 15:18:00.090 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-07-31 15:18:00.090 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-07-31 15:18:00.090 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-07-31 15:18:00.092 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:18:00.388 | INFO     | pipecat.transports.services.daily:join:597 - Joining https://heweijie.daily.co/9hJ15erLSSJfyuPCcyAo
2025-07-31 15:18:00.389 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
{"timestamp":"2025-07-31T15:18:01.008536Z","level":"ERROR","fields":{"message":"account-missing-payment-method"},"target":"daily_core::call_client"}
{"timestamp":"2025-07-31T15:18:01.008661Z","level":"ERROR","fields":{"message":"account-missing-payment-method"},"target":"daily_core::event"}
{"timestamp":"2025-07-31T15:18:01.011090Z","level":"ERROR","fields":{"message":"join (request 2) encountered an error: Connection(Api(RoomLookup(RoomInfoError(Unhandled(\"account-missing-payment-method\")))))"},"target":"daily_core::native::ffi::call_client"}
2025-07-31 15:18:01.011 | ERROR    | pipecat.transports.services.daily:join:627 - Error joining https://heweijie.daily.co/9hJ15erLSSJfyuPCcyAo: Connection(Api(RoomLookup(RoomInfoError(Unhandled("account-missing-payment-method")))))
2025-07-31 15:18:01.012 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-07-31 15:18:01.013 | DEBUG    | pipecat.transports.services.daily:start_audio_in_streaming:1396 - Start receiving audio
2025-07-31 15:18:01.018 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#0(error: Error joining https://heweijie.daily.co/9hJ15erLSSJfyuPCcyAo: Connection(Api(RoomLookup(RoomInfoError(Unhandled("account-missing-payment-method"))))), fatal: False)
2025-07-31 15:18:02.410 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:02.410 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:02.411 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:18:02.412 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:18:02.412 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:18:02.414 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#1(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:18:06.693 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:18:06.694 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:06.694 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:06.695 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:18:06.695 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:18:06.695 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:18:06.697 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#2(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:18:10.970 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:18:10.970 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:10.970 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:10.971 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:18:10.971 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:18:10.971 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:18:10.973 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#3(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:18:15.247 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:18:15.248 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:15.248 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:15.249 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:18:15.249 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:18:15.249 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:18:15.252 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#4(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:18:19.524 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:18:19.524 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:19.524 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:19.525 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:18:19.525 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:18:19.525 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:18:19.526 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#5(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:18:23.796 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:18:23.797 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:23.797 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:23.798 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:18:23.798 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:18:23.798 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:18:23.800 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#6(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:18:28.076 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:18:28.077 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:28.077 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:28.078 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:18:28.078 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:18:28.078 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:18:28.080 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#7(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:18:32.371 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:18:32.371 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:32.371 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:32.372 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:18:32.372 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:18:32.372 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:18:32.374 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#8(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:18:36.640 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:18:36.641 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:36.641 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:36.642 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:18:36.642 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:18:36.642 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:18:36.644 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#9(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:18:40.917 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:18:40.918 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:40.918 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:40.919 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:18:40.919 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:18:40.919 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:18:40.921 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#10(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:18:45.187 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:18:45.188 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:45.188 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:45.188 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:18:45.189 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:18:45.189 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:18:45.190 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#11(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:18:49.568 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:18:49.568 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:49.568 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:49.569 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:18:49.569 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:18:49.569 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:18:49.571 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#12(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:18:53.844 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:18:53.845 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:53.845 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:53.846 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:18:53.846 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:18:53.846 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:18:53.848 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#13(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:18:58.116 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:18:58.116 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:58.116 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:18:58.117 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:18:58.117 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:18:58.117 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:18:58.119 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#14(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:19:02.388 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:19:02.389 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:02.389 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:02.390 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:19:02.390 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:19:02.390 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:19:02.393 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#15(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:19:06.686 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:19:06.687 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:06.687 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:06.687 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:19:06.687 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:19:06.688 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:19:06.692 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#16(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:19:10.997 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:19:10.997 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:10.997 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:10.998 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:19:10.998 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:19:10.999 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:19:11.000 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#17(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:19:15.308 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:19:15.309 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:15.309 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:15.310 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:19:15.310 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:19:15.310 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:19:15.313 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#18(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:19:19.601 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:19:19.601 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:19.602 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:19.602 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:19:19.602 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:19:19.603 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:19:19.606 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#19(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:19:23.919 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:19:23.919 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:23.919 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:23.920 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:19:23.920 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:19:23.920 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:19:23.922 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#20(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:19:28.186 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:19:28.186 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:28.186 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:28.187 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:19:28.187 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:19:28.187 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:19:28.189 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#21(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:19:32.479 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:19:32.479 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:32.479 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:32.480 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:19:32.480 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:19:32.480 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:19:32.482 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#22(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:19:36.758 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:19:36.758 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:36.759 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:36.759 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:19:36.759 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:19:36.759 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:19:36.760 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#23(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [58463]
2025-07-31 15:19:41.046 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:19:41.047 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:41.047 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:41.047 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:19:41.047 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:19:41.048 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:19:41.049 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#24(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:19:45.324 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:19:45.325 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:45.325 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:45.326 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:19:45.326 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:19:45.326 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:19:45.328 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#25(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:19:49.594 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:19:49.595 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:49.595 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:49.596 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:19:49.596 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:19:49.596 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:19:49.598 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#26(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:19:53.877 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:19:53.878 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:53.878 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:53.878 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:19:53.879 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:19:53.879 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:19:53.880 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#27(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:19:58.154 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:19:58.155 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:58.155 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:19:58.155 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:19:58.155 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:19:58.156 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:19:58.157 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#28(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:20:02.423 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:20:02.423 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:02.424 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:02.425 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:20:02.425 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:20:02.425 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:20:02.427 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#29(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:20:06.748 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:20:06.749 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:06.749 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:06.750 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:20:06.750 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:20:06.750 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:20:06.752 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#30(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:20:11.042 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:20:11.042 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:11.042 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:11.043 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:20:11.043 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:20:11.043 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:20:11.046 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#31(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:20:15.323 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:20:15.323 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:15.323 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:15.324 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:20:15.324 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:20:15.324 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:20:15.325 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#32(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:20:19.603 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:20:19.603 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:19.603 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:19.604 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:20:19.604 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:20:19.604 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:20:19.606 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#33(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:20:23.883 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:20:23.884 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:23.884 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:23.885 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:20:23.885 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:20:23.885 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:20:23.887 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#34(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:20:28.171 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:20:28.172 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:28.172 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:28.172 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:20:28.172 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:20:28.173 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:20:28.174 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#35(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:20:32.443 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:20:32.443 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:32.443 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:32.462 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:20:32.462 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:20:32.463 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:20:32.464 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#36(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:20:36.736 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:20:36.736 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:36.737 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:36.737 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:20:36.738 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:20:36.738 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:20:36.740 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#37(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:20:41.010 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:20:41.011 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:41.011 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:41.011 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:20:41.011 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:20:41.011 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:20:41.012 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#38(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:20:45.338 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:20:45.339 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:45.339 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:45.340 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:20:45.340 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:20:45.340 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:20:45.341 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#39(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:20:49.621 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:20:49.621 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:49.621 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:49.622 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:20:49.622 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:20:49.622 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:20:49.624 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#40(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:20:53.904 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:20:53.904 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:53.904 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:53.905 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:20:53.905 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:20:53.905 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:20:53.907 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#41(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:20:58.172 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:20:58.173 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:58.173 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:20:58.173 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:20:58.174 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:20:58.174 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:20:58.176 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#42(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:21:02.465 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:21:02.466 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:02.466 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:02.467 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:21:02.467 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:21:02.467 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:21:02.468 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#43(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:21:06.739 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:21:06.740 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:06.740 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:06.741 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:21:06.741 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:21:06.741 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:21:06.743 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#44(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:21:11.021 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:21:11.022 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:11.022 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:11.023 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:21:11.023 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:21:11.023 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:21:11.025 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#45(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:21:15.293 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:21:15.293 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:15.293 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:15.294 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:21:15.294 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:21:15.294 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:21:15.295 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#46(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:21:19.588 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:21:19.588 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:19.588 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:19.589 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:21:19.589 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:21:19.589 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:21:19.591 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#47(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:21:23.886 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:21:23.887 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:23.887 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:23.888 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:21:23.888 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:21:23.888 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:21:23.890 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#48(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:21:28.163 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:21:28.163 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:28.164 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:28.164 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:21:28.164 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:21:28.165 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:21:28.166 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#49(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:21:32.438 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:21:32.439 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:32.439 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:32.440 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:21:32.440 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:21:32.440 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:21:32.442 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#50(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:21:36.715 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:21:36.715 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:36.716 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:36.717 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:21:36.717 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:21:36.717 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:21:36.719 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#51(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:21:40.977 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:21:40.978 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:40.978 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:40.978 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:21:40.979 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:21:40.979 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:21:40.980 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#52(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:21:45.261 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:21:45.261 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:45.261 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:45.262 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:21:45.262 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:21:45.263 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:21:45.265 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#53(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:21:49.536 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:21:49.537 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:49.537 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:49.538 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:21:49.538 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:21:49.538 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:21:49.539 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#54(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:21:53.818 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:21:53.818 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:53.818 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:53.819 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:21:53.819 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:21:53.819 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:21:53.820 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#55(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:21:58.097 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:21:58.098 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:58.098 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:21:58.099 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:21:58.099 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:21:58.099 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:21:58.100 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#56(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:22:02.380 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:22:02.381 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:02.381 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:02.382 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:22:02.382 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:22:02.382 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:22:02.384 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#57(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:22:06.703 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:22:06.704 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:06.704 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:06.705 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:22:06.705 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:22:06.705 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:22:06.706 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#58(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:22:10.985 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:22:10.985 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:10.985 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:10.986 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:22:10.986 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:22:10.986 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:22:10.988 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#59(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:22:15.253 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:22:15.253 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:15.253 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:15.254 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:22:15.254 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:22:15.254 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:22:15.256 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#60(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:22:19.615 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:22:19.616 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:19.616 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:19.616 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:22:19.616 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:22:19.617 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:22:19.618 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#61(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:22:23.908 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:22:23.908 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:23.908 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:23.909 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:22:23.909 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:22:23.909 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:22:23.911 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#62(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:22:28.197 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:22:28.197 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:28.197 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:28.198 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:22:28.198 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:22:28.198 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:22:28.200 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#63(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:22:32.470 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:22:32.471 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:32.471 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:32.471 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:22:32.472 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:22:32.472 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:22:32.473 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#64(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:22:36.744 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:22:36.745 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:36.745 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:36.745 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:22:36.746 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:22:36.746 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:22:36.747 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#65(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:22:41.016 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:22:41.017 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:41.017 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:41.018 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:22:41.018 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:22:41.018 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:22:41.019 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#66(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:22:45.287 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:22:45.287 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:45.287 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:45.288 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:22:45.288 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:22:45.289 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:22:45.290 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#67(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:22:49.563 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:22:49.564 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:49.565 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:49.565 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:22:49.566 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:22:49.566 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:22:49.567 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#68(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:22:53.834 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:22:53.835 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:53.835 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:53.836 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:22:53.836 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:22:53.837 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:22:53.839 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#69(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:22:58.124 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:22:58.124 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:58.124 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:22:58.125 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:22:58.125 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:22:58.125 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:22:58.127 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#70(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:23:01.020 | WARNING  | pipecat.pipeline.task:_idle_timeout_detected:814 - Idle timeout detected. Last 10 frames received:
2025-07-31 15:23:01.020 | WARNING  | pipecat.pipeline.task:_idle_timeout_detected:816 - Frame 1: StartFrame#0
2025-07-31 15:23:01.020 | WARNING  | pipecat.pipeline.task:_idle_timeout_detected:816 - Frame 2: SpeechControlParamsFrame#0
2025-07-31 15:23:01.020 | WARNING  | pipecat.pipeline.task:_idle_timeout_detected:816 - Frame 3: MetricsFrame#0
2025-07-31 15:23:01.020 | WARNING  | pipecat.pipeline.task:_idle_timeout_detected:820 - Idle pipeline detected, cancelling pipeline task...
2025-07-31 15:23:01.021 | DEBUG    | pipecat.pipeline.task:_cancel:497 - Canceling pipeline task PipelineTask#0
2025-07-31 15:23:01.029 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:23:01.037 | DEBUG    | pipecat.pipeline.runner:run:83 - Runner PipelineRunner#0 finished running PipelineTask#0
