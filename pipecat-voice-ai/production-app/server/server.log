2025-07-31 15:47:45.243 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
INFO:     Started server process [62225]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     <PERSON>vicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/uQgzGdtlWQLAaznOzNJB
INFO:     ***************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 15:48:02.339 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:48:06.111 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-07-31 15:48:06.322 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
Invoking rtc_set_log_level, log enabled:1, level:4
Configuring the webrtc log level.
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-07-31 15:48:06.484 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> DailyInputTransport#0
2025-07-31 15:48:06.484 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyInputTransport#0 -> RTVIProcessor#0
2025-07-31 15:48:06.484 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking RTVIProcessor#0 -> OpenAIUserContextAggregator#0
2025-07-31 15:48:06.484 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> GroqLLMService#0
2025-07-31 15:48:06.484 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking GroqLLMService#0 -> EdgeTTSProcessor#0
2025-07-31 15:48:06.484 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking EdgeTTSProcessor#0 -> TalkingAnimationProcessor#0
2025-07-31 15:48:06.485 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking TalkingAnimationProcessor#0 -> DailyOutputTransport#0
2025-07-31 15:48:06.485 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-07-31 15:48:06.485 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-07-31 15:48:06.485 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-07-31 15:48:06.485 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-07-31 15:48:06.486 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-07-31 15:48:06.487 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-07-31 15:48:06.488 | INFO     | pipecat.transports.services.daily:join:597 - Joining https://heweijie.daily.co/uQgzGdtlWQLAaznOzNJB
2025-07-31 15:48:08.749 | INFO     | pipecat.transports.services.daily:join:617 - Joined https://heweijie.daily.co/uQgzGdtlWQLAaznOzNJB
2025-07-31 15:48:08.749 | DEBUG    | pipecat.transports.services.daily:start_transcription:856 - Starting transcription: settings=language='en' model='nova-2-general' profanity_filter=True redact=False endpointing=True punctuate=True includeRawResponse=True extra={'interim_results': True}
2025-07-31 15:48:09.667 | DEBUG    | pipecat.transports.services.daily:on_transcription_started:1221 - Transcription started: {'language': 'en', 'model': 'nova-2-general', 'transcriptId': '784dba78-a2ce-4204-9528-48693618af1b', 'instanceId': 'a1f2f6b7-b1ac-4202-85e5-d446cb6c3d3f', 'startedBy': '5d52e5d3-d81e-4de6-a5a9-1859dec9481c'}
2025-07-31 15:48:09.668 | DEBUG    | pipecat.transports.services.daily:start_audio_in_streaming:1396 - Start receiving audio
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/TM6PlNXqSt1xCEeT9SBy
INFO:     *************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 15:50:26.599 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:50:30.399 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-07-31 15:50:30.591 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
Invoking rtc_set_log_level, log enabled:1, level:4
Configuring the webrtc log level.
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-07-31 15:50:30.705 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> DailyInputTransport#0
2025-07-31 15:50:30.705 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyInputTransport#0 -> RTVIProcessor#0
2025-07-31 15:50:30.705 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking RTVIProcessor#0 -> OpenAIUserContextAggregator#0
2025-07-31 15:50:30.705 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> GroqLLMService#0
2025-07-31 15:50:30.706 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking GroqLLMService#0 -> EdgeTTSProcessor#0
2025-07-31 15:50:30.706 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking EdgeTTSProcessor#0 -> TalkingAnimationProcessor#0
2025-07-31 15:50:30.706 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking TalkingAnimationProcessor#0 -> DailyOutputTransport#0
2025-07-31 15:50:30.706 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-07-31 15:50:30.706 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-07-31 15:50:30.706 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-07-31 15:50:30.706 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-07-31 15:50:30.707 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-07-31 15:50:30.708 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-07-31 15:50:30.709 | INFO     | pipecat.transports.services.daily:join:597 - Joining https://heweijie.daily.co/TM6PlNXqSt1xCEeT9SBy
2025-07-31 15:50:32.227 | INFO     | pipecat.transports.services.daily:join:617 - Joined https://heweijie.daily.co/TM6PlNXqSt1xCEeT9SBy
2025-07-31 15:50:32.228 | DEBUG    | pipecat.transports.services.daily:start_transcription:856 - Starting transcription: settings=language='en' model='nova-2-general' profanity_filter=True redact=False endpointing=True punctuate=True includeRawResponse=True extra={'interim_results': True}
2025-07-31 15:50:33.296 | DEBUG    | pipecat.transports.services.daily:on_transcription_started:1221 - Transcription started: {'instanceId': 'a1f2f6b7-b1ac-4202-85e5-d446cb6c3d3f', 'transcriptId': '6346811f-83b0-4ed5-af03-86c989ed6317', 'language': 'en', 'startedBy': '338da51d-32a7-49a5-9760-3f4d589f81dd', 'model': 'nova-2-general'}
2025-07-31 15:50:33.297 | DEBUG    | pipecat.transports.services.daily:start_audio_in_streaming:1396 - Start receiving audio
2025-07-31 15:50:33.298 | INFO     | pipecat.transports.services.daily:_on_participant_joined:2246 - Participant joined 5a473c88-7020-449c-9920-ea0ad32eec2b
2025-07-31 15:50:33.299 | DEBUG    | pipecat.transports.services.daily:capture_participant_audio:936 - Starting to capture [microphone] audio from participant 5a473c88-7020-449c-9920-ea0ad32eec2b
2025-07-31 15:50:33.300 | INFO     | __main__:on_first_participant_joined:291 - Participant joined: {'info': {'isLocal': False, 'joinedAt': 1753977031, 'isOwner': True, 'permissions': {'canAdmin': ['streaming', 'transcription', 'participants'], 'hasPresence': True, 'canReceive': {'base': {'screenVideo': True, 'customVideo': {'*': True}, 'screenAudio': True, 'camera': True, 'customAudio': {'*': True}, 'microphone': True}}, 'canSend': ['screenAudio', 'microphone', 'camera', 'customAudio', 'screenVideo', 'customVideo']}}, 'id': '5a473c88-7020-449c-9920-ea0ad32eec2b', 'media': {'camera': {'offReasons': ['user'], 'state': 'off', 'subscribed': 'unsubscribed'}, 'screenAudio': {'state': 'off', 'offReasons': ['user'], 'subscribed': 'subscribed'}, 'microphone': {'state': 'loading', 'subscribed': 'subscribed'}, 'customAudio': {}, 'customVideo': {}, 'screenVideo': {'offReasons': ['user'], 'state': 'off', 'subscribed': 'unsubscribed'}}}
2025-07-31 15:50:33.300 | DEBUG    | pipecat.processors.frameworks.rtvi:_handle_client_ready:1431 - Received client-ready: version 1.0.0
2025-07-31 15:50:33.300 | DEBUG    | pipecat.processors.frameworks.rtvi:_handle_client_ready:1441 - Client Details: library='@pipecat-ai/client-react' library_version='1.0.0' platform='Windows' platform_version='NT 10.0' platform_details={'browser': 'Chrome', 'platform_type': 'desktop', 'engine': 'Blink', 'browser_version': '*********'}
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frameworks/rtvi.py:1549: DeprecationWarning: Configuration helpers are deprecated. If your application needs this behavior, use custom server and client messages.
  warnings.warn(
2025-07-31 15:50:33.417 | ERROR    | __main__:process_frame:143 - Groq API error: 404 - {"error":{"message":"Not Found"}}
2025-07-31 15:50:33.417 | ERROR    | pipecat.processors.frame_processor:__internal_push_frame:608 - Uncaught exception in GroqLLMService#0: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue'
Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-groq.py", line 304, in <module>
    asyncio.run(main())
    │       │   └ <function main at 0x77125afeb100>
    │       └ <function run at 0x77127b4e0680>
    └ <module 'asyncio' from '/usr/lib/python3.12/asyncio/__init__.py'>

  File "/usr/lib/python3.12/asyncio/runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object main at 0x77125b0766c0>
           │      └ <function Runner.run at 0x77127a337ec0>
           └ <asyncio.runners.Runner object at 0x77125ae89790>
  File "/usr/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<main() running at /www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-g...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x77127a335b20>
           │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x77125ae89790>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x77127a335a80>
    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x77127a337880>
    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 1987, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x77127aa35da0>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "/usr/lib/python3.12/asyncio/events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/utils/asyncio/task_manager.py", line 236, in run_coroutine
    await coroutine
          └ <coroutine object FrameProcessor.__input_frame_task_handler at 0x7712566e0480>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 654, in __input_frame_task_handler
    await self.process_frame(frame, direction)
          │    │             │      └ <FrameDirection.DOWNSTREAM: 1>
          │    │             └ TextFrame(id=38, name='TextFrame#0', pts=None, metadata={}, transport_source=None, transport_destination=None, text='你好！我是Gro...
          │    └ <function GroqLLMService.process_frame at 0x77125aee2700>
          └ <__main__.GroqLLMService object at 0x77125ae895b0>

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-groq.py", line 144, in process_frame
    await self.push_frame(
          │    └ <function FrameProcessor.push_frame at 0x77125c1cf880>
          └ <__main__.GroqLLMService object at 0x77125ae895b0>

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 509, in push_frame
    await self.__internal_push_frame(frame, direction)
          │                          │      └ <FrameDirection.DOWNSTREAM: 1>
          │                          └ TextFrame(id=45, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无法...
          └ <__main__.GroqLLMService object at 0x77125ae895b0>
> File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 594, in __internal_push_frame
    await self._next.queue_frame(frame, direction)
          │    │     │           │      └ <FrameDirection.DOWNSTREAM: 1>
          │    │     │           └ TextFrame(id=45, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无法...
          │    │     └ <function FrameProcessor.queue_frame at 0x77125c1cf560>
          │    └ <__main__.EdgeTTSProcessor object at 0x77125aee4d40>
          └ <__main__.GroqLLMService object at 0x77125ae895b0>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 457, in queue_frame
    await self.__input_queue.put((frame, direction, callback))
          │                       │      │          └ None
          │                       │      └ <FrameDirection.DOWNSTREAM: 1>
          │                       └ TextFrame(id=45, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无法...
          └ <__main__.EdgeTTSProcessor object at 0x77125aee4d40>

AttributeError: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue'. Did you mean: '_FrameProcessor__input_event'?
2025-07-31 15:50:33.465 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#0(error: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue', fatal: False)
2025-07-31 15:50:35.054 | DEBUG    | pipecat.transports.base_input:_handle_user_interruption:348 - User started speaking
2025-07-31 15:50:35.501 | DEBUG    | pipecat.transports.services.daily:_on_transcription_message:2293 - Transcription (from: 5a473c88-7020-449c-9920-ea0ad32eec2b): [Neehaw.]
2025-07-31 15:50:37.015 | DEBUG    | pipecat.transports.base_input:_handle_user_interruption:372 - User stopped speaking
2025-07-31 15:51:07.895 | DEBUG    | pipecat.transports.base_input:_handle_user_interruption:348 - User started speaking
2025-07-31 15:51:08.260 | DEBUG    | pipecat.transports.services.daily:_on_transcription_message:2293 - Transcription (from: 5a473c88-7020-449c-9920-ea0ad32eec2b): [Yeah.]
2025-07-31 15:51:08.794 | DEBUG    | pipecat.transports.base_input:_handle_user_interruption:372 - User stopped speaking
Creating room
Room URL: https://heweijie.daily.co/pHW6f31GrkBUbpLga9Oo
INFO:     ***************:48476 - "GET / HTTP/1.1" 307 Temporary Redirect
2025-07-31 15:51:24.692 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:51:28.290 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-07-31 15:51:28.492 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
Invoking rtc_set_log_level, log enabled:1, level:4
Configuring the webrtc log level.
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-07-31 15:51:28.629 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> DailyInputTransport#0
2025-07-31 15:51:28.629 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyInputTransport#0 -> RTVIProcessor#0
2025-07-31 15:51:28.629 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking RTVIProcessor#0 -> OpenAIUserContextAggregator#0
2025-07-31 15:51:28.629 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> GroqLLMService#0
2025-07-31 15:51:28.629 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking GroqLLMService#0 -> EdgeTTSProcessor#0
2025-07-31 15:51:28.630 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking EdgeTTSProcessor#0 -> TalkingAnimationProcessor#0
2025-07-31 15:51:28.630 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking TalkingAnimationProcessor#0 -> DailyOutputTransport#0
2025-07-31 15:51:28.630 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-07-31 15:51:28.630 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-07-31 15:51:28.630 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-07-31 15:51:28.630 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-07-31 15:51:28.631 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-07-31 15:51:28.633 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-07-31 15:51:28.634 | INFO     | pipecat.transports.services.daily:join:597 - Joining https://heweijie.daily.co/pHW6f31GrkBUbpLga9Oo
2025-07-31 15:51:30.632 | INFO     | pipecat.transports.services.daily:join:617 - Joined https://heweijie.daily.co/pHW6f31GrkBUbpLga9Oo
2025-07-31 15:51:30.633 | DEBUG    | pipecat.transports.services.daily:start_transcription:856 - Starting transcription: settings=language='en' model='nova-2-general' profanity_filter=True redact=False endpointing=True punctuate=True includeRawResponse=True extra={'interim_results': True}
2025-07-31 15:51:31.702 | DEBUG    | pipecat.transports.services.daily:on_transcription_started:1221 - Transcription started: {'language': 'en', 'model': 'nova-2-general', 'instanceId': 'a1f2f6b7-b1ac-4202-85e5-d446cb6c3d3f', 'startedBy': '111eda39-3074-4b9b-a0b2-4b3792eef065', 'transcriptId': 'c82c2ef9-47c5-48b1-966a-fd2100383ceb'}
2025-07-31 15:51:31.704 | DEBUG    | pipecat.transports.services.daily:start_audio_in_streaming:1396 - Start receiving audio
2025-07-31 15:51:55.615 | DEBUG    | pipecat.transports.base_input:_handle_user_interruption:348 - User started speaking
2025-07-31 15:51:55.948 | DEBUG    | pipecat.transports.services.daily:_on_transcription_message:2293 - Transcription (from: 5a473c88-7020-449c-9920-ea0ad32eec2b): [Wait.]
2025-07-31 15:51:56.494 | DEBUG    | pipecat.transports.base_input:_handle_user_interruption:372 - User stopped speaking
2025-07-31 15:52:54.975 | DEBUG    | pipecat.transports.base_input:_handle_user_interruption:348 - User started speaking
2025-07-31 15:52:56.115 | DEBUG    | pipecat.transports.base_input:_handle_user_interruption:372 - User stopped speaking
2025-07-31 15:53:06.491 | WARNING  | pipecat.pipeline.task:_idle_timeout_detected:814 - Idle timeout detected. Last 10 frames received:
2025-07-31 15:53:06.492 | WARNING  | pipecat.pipeline.task:_idle_timeout_detected:820 - Idle pipeline detected, cancelling pipeline task...
2025-07-31 15:53:06.493 | DEBUG    | pipecat.pipeline.task:_cancel:497 - Canceling pipeline task PipelineTask#0
2025-07-31 15:53:06.501 | INFO     | pipecat.transports.services.daily:leave:703 - Leaving https://heweijie.daily.co/uQgzGdtlWQLAaznOzNJB
2025-07-31 15:53:06.502 | DEBUG    | pipecat.transports.services.daily:stop_transcription:872 - Stopping transcription
2025-07-31 15:53:06.567 | DEBUG    | pipecat.transports.services.daily:on_transcription_stopped:1232 - Transcription stopped
2025-07-31 15:53:06.588 | INFO     | pipecat.transports.services.daily:leave:715 - Left https://heweijie.daily.co/uQgzGdtlWQLAaznOzNJB
2025-07-31 15:53:06.593 | DEBUG    | pipecat.pipeline.runner:run:83 - Runner PipelineRunner#0 finished running PipelineTask#0
{"timestamp":"2025-07-31T15:53:06.646719Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/EU2pAtjmqntAkaqtxkXM
INFO:     **************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 15:53:22.721 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:53:26.303 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-07-31 15:53:26.485 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
Invoking rtc_set_log_level, log enabled:1, level:4
Configuring the webrtc log level.
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-07-31 15:53:26.620 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> DailyInputTransport#0
2025-07-31 15:53:26.620 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyInputTransport#0 -> RTVIProcessor#0
2025-07-31 15:53:26.621 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking RTVIProcessor#0 -> OpenAIUserContextAggregator#0
2025-07-31 15:53:26.621 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> GroqLLMService#0
2025-07-31 15:53:26.621 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking GroqLLMService#0 -> EdgeTTSProcessor#0
2025-07-31 15:53:26.621 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking EdgeTTSProcessor#0 -> TalkingAnimationProcessor#0
2025-07-31 15:53:26.621 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking TalkingAnimationProcessor#0 -> DailyOutputTransport#0
2025-07-31 15:53:26.621 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-07-31 15:53:26.621 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-07-31 15:53:26.622 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-07-31 15:53:26.622 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-07-31 15:53:26.623 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-07-31 15:53:26.624 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-07-31 15:53:26.625 | INFO     | pipecat.transports.services.daily:join:597 - Joining https://heweijie.daily.co/EU2pAtjmqntAkaqtxkXM
2025-07-31 15:53:28.863 | INFO     | pipecat.transports.services.daily:join:617 - Joined https://heweijie.daily.co/EU2pAtjmqntAkaqtxkXM
2025-07-31 15:53:28.863 | DEBUG    | pipecat.transports.services.daily:start_transcription:856 - Starting transcription: settings=language='en' model='nova-2-general' profanity_filter=True redact=False endpointing=True punctuate=True includeRawResponse=True extra={'interim_results': True}
2025-07-31 15:53:29.618 | DEBUG    | pipecat.transports.services.daily:on_transcription_started:1221 - Transcription started: {'transcriptId': '61071a82-5f85-47e0-a24e-4b5be8facb34', 'model': 'nova-2-general', 'startedBy': '307290c1-d5c1-4b21-91a6-e63ad349e094', 'instanceId': 'a1f2f6b7-b1ac-4202-85e5-d446cb6c3d3f', 'language': 'en'}
2025-07-31 15:53:31.473 | DEBUG    | pipecat.transports.services.daily:start_audio_in_streaming:1396 - Start receiving audio
2025-07-31 15:53:31.474 | INFO     | pipecat.transports.services.daily:_on_participant_joined:2246 - Participant joined 7ca80354-e916-4ed6-bfd6-e8f677c36fa0
2025-07-31 15:53:31.479 | DEBUG    | pipecat.transports.services.daily:capture_participant_audio:936 - Starting to capture [microphone] audio from participant 7ca80354-e916-4ed6-bfd6-e8f677c36fa0
2025-07-31 15:53:31.480 | INFO     | __main__:on_first_participant_joined:291 - Participant joined: {'id': '7ca80354-e916-4ed6-bfd6-e8f677c36fa0', 'media': {'customVideo': {}, 'screenAudio': {'offReasons': ['user'], 'state': 'off', 'subscribed': 'subscribed'}, 'screenVideo': {'state': 'off', 'subscribed': 'unsubscribed', 'offReasons': ['user']}, 'camera': {'subscribed': 'unsubscribed', 'offReasons': ['user'], 'state': 'off'}, 'microphone': {'subscribed': 'subscribed', 'state': 'loading'}, 'customAudio': {}}, 'info': {'isLocal': False, 'isOwner': True, 'permissions': {'canSend': ['microphone', 'camera', 'customVideo', 'customAudio', 'screenVideo', 'screenAudio'], 'hasPresence': True, 'canAdmin': ['streaming', 'transcription', 'participants'], 'canReceive': {'base': {'customVideo': {'*': True}, 'microphone': True, 'customAudio': {'*': True}, 'camera': True, 'screenAudio': True, 'screenVideo': True}}}, 'joinedAt': 1753977208}}
2025-07-31 15:53:33.801 | DEBUG    | pipecat.processors.frameworks.rtvi:_handle_client_ready:1431 - Received client-ready: version 1.0.0
2025-07-31 15:53:33.802 | DEBUG    | pipecat.processors.frameworks.rtvi:_handle_client_ready:1441 - Client Details: library='@pipecat-ai/client-react' library_version='1.0.0' platform='iOS' platform_version='17.6.1' platform_details={'platform_type': 'mobile', 'browser': 'Safari', 'browser_version': '17.6', 'engine': 'WebKit'}
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frameworks/rtvi.py:1549: DeprecationWarning: Configuration helpers are deprecated. If your application needs this behavior, use custom server and client messages.
  warnings.warn(
2025-07-31 15:53:33.889 | ERROR    | __main__:process_frame:143 - Groq API error: 404 - {"error":{"message":"Not Found"}}
2025-07-31 15:53:33.890 | ERROR    | pipecat.processors.frame_processor:__internal_push_frame:608 - Uncaught exception in GroqLLMService#0: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue'
Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-groq.py", line 304, in <module>
    asyncio.run(main())
    │       │   └ <function main at 0x7dd7138ef100>
    │       └ <function run at 0x7dd733b5c680>
    └ <module 'asyncio' from '/usr/lib/python3.12/asyncio/__init__.py'>

  File "/usr/lib/python3.12/asyncio/runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object main at 0x7dd71397a6c0>
           │      └ <function Runner.run at 0x7dd732d13ec0>
           └ <asyncio.runners.Runner object at 0x7dd713787b00>
  File "/usr/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<main() running at /www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-g...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x7dd732d11b20>
           │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x7dd713787b00>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x7dd732d11a80>
    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x7dd732d13880>
    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 1987, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x7dd73339dda0>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "/usr/lib/python3.12/asyncio/events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/utils/asyncio/task_manager.py", line 236, in run_coroutine
    await coroutine
          └ <coroutine object FrameProcessor.__input_frame_task_handler at 0x7dd7101e8480>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 654, in __input_frame_task_handler
    await self.process_frame(frame, direction)
          │    │             │      └ <FrameDirection.DOWNSTREAM: 1>
          │    │             └ TextFrame(id=110, name='TextFrame#0', pts=None, metadata={}, transport_source=None, transport_destination=None, text='你好！我是Gr...
          │    └ <function GroqLLMService.process_frame at 0x7dd7137e6700>
          └ <__main__.GroqLLMService object at 0x7dd713787bc0>

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-groq.py", line 144, in process_frame
    await self.push_frame(
          │    └ <function FrameProcessor.push_frame at 0x7dd715c3f9c0>
          └ <__main__.GroqLLMService object at 0x7dd713787bc0>

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 509, in push_frame
    await self.__internal_push_frame(frame, direction)
          │                          │      └ <FrameDirection.DOWNSTREAM: 1>
          │                          └ TextFrame(id=115, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无...
          └ <__main__.GroqLLMService object at 0x7dd713787bc0>
> File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 594, in __internal_push_frame
    await self._next.queue_frame(frame, direction)
          │    │     │           │      └ <FrameDirection.DOWNSTREAM: 1>
          │    │     │           └ TextFrame(id=115, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无...
          │    │     └ <function FrameProcessor.queue_frame at 0x7dd715c3f6a0>
          │    └ <__main__.EdgeTTSProcessor object at 0x7dd7137e8f20>
          └ <__main__.GroqLLMService object at 0x7dd713787bc0>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 457, in queue_frame
    await self.__input_queue.put((frame, direction, callback))
          │                       │      │          └ None
          │                       │      └ <FrameDirection.DOWNSTREAM: 1>
          │                       └ TextFrame(id=115, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无...
          └ <__main__.EdgeTTSProcessor object at 0x7dd7137e8f20>

AttributeError: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue'. Did you mean: '_FrameProcessor__input_event'?
2025-07-31 15:53:33.899 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#0(error: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue', fatal: False)
2025-07-31 15:53:37.695 | INFO     | pipecat.transports.services.daily:_on_participant_left:2264 - Participant left 5a473c88-7020-449c-9920-ea0ad32eec2b
2025-07-31 15:53:37.696 | INFO     | __main__:on_participant_left:296 - Participant left: {'info': {'isLocal': False, 'joinedAt': 1753977031, 'permissions': {'canReceive': {'base': {'customAudio': {'*': True}, 'customVideo': {'*': True}, 'screenVideo': True, 'microphone': True, 'screenAudio': True, 'camera': True}}, 'canSend': ['customAudio', 'screenAudio', 'microphone', 'camera', 'screenVideo', 'customVideo'], 'canAdmin': ['transcription', 'participants', 'streaming'], 'hasPresence': True}, 'isOwner': True}, 'id': '5a473c88-7020-449c-9920-ea0ad32eec2b'}
2025-07-31 15:53:37.696 | DEBUG    | pipecat.pipeline.task:_cancel:497 - Canceling pipeline task PipelineTask#0
2025-07-31 15:53:37.698 | INFO     | pipecat.transports.services.daily:leave:703 - Leaving https://heweijie.daily.co/TM6PlNXqSt1xCEeT9SBy
2025-07-31 15:53:37.698 | DEBUG    | pipecat.transports.services.daily:stop_transcription:872 - Stopping transcription
2025-07-31 15:53:37.759 | DEBUG    | pipecat.transports.services.daily:on_transcription_stopped:1232 - Transcription stopped
2025-07-31 15:53:37.795 | INFO     | pipecat.transports.services.daily:leave:715 - Left https://heweijie.daily.co/TM6PlNXqSt1xCEeT9SBy
2025-07-31 15:53:37.801 | DEBUG    | pipecat.pipeline.runner:run:83 - Runner PipelineRunner#0 finished running PipelineTask#0
{"timestamp":"2025-07-31T15:53:37.855266Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
{"timestamp":"2025-07-31T15:53:37.873095Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/OCeqKMadoPEzAh5fao3R
INFO:     *************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 15:53:53.688 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:53:55.001 | WARNING  | pipecat.pipeline.runner:_sig_cancel:108 - Interruption detected. Cancelling runner PipelineRunner#0
2025-07-31 15:53:55.002 | DEBUG    | pipecat.pipeline.runner:cancel:92 - Cancelling runner PipelineRunner#0
2025-07-31 15:53:55.002 | DEBUG    | pipecat.pipeline.task:_cancel:497 - Canceling pipeline task PipelineTask#0
2025-07-31 15:53:55.002 | WARNING  | pipecat.pipeline.runner:_sig_cancel:108 - Interruption detected. Cancelling runner PipelineRunner#0
2025-07-31 15:53:55.003 | DEBUG    | pipecat.pipeline.runner:cancel:92 - Cancelling runner PipelineRunner#0
2025-07-31 15:53:55.004 | DEBUG    | pipecat.pipeline.task:_cancel:497 - Canceling pipeline task PipelineTask#0
2025-07-31 15:53:55.004 | INFO     | pipecat.transports.services.daily:leave:703 - Leaving https://heweijie.daily.co/EU2pAtjmqntAkaqtxkXM
2025-07-31 15:53:55.004 | DEBUG    | pipecat.transports.services.daily:stop_transcription:872 - Stopping transcription
2025-07-31 15:53:55.007 | INFO     | pipecat.transports.services.daily:leave:703 - Leaving https://heweijie.daily.co/pHW6f31GrkBUbpLga9Oo
2025-07-31 15:53:55.007 | DEBUG    | pipecat.transports.services.daily:stop_transcription:872 - Stopping transcription
2025-07-31 15:53:55.066 | DEBUG    | pipecat.transports.services.daily:on_transcription_stopped:1232 - Transcription stopped
2025-07-31 15:53:55.066 | DEBUG    | pipecat.transports.services.daily:on_transcription_stopped:1232 - Transcription stopped
2025-07-31 15:53:55.087 | INFO     | pipecat.transports.services.daily:leave:715 - Left https://heweijie.daily.co/pHW6f31GrkBUbpLga9Oo
2025-07-31 15:53:55.092 | DEBUG    | pipecat.pipeline.runner:run:83 - Runner PipelineRunner#0 finished running PipelineTask#0
2025-07-31 15:53:55.106 | INFO     | pipecat.transports.services.daily:leave:715 - Left https://heweijie.daily.co/EU2pAtjmqntAkaqtxkXM
2025-07-31 15:53:55.110 | DEBUG    | pipecat.pipeline.runner:run:83 - Runner PipelineRunner#0 finished running PipelineTask#0
{"timestamp":"2025-07-31T15:53:55.148602Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
{"timestamp":"2025-07-31T15:53:55.165711Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
{"timestamp":"2025-07-31T15:53:55.165892Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/ygQ6SuuT4Ft6G1fvl004
INFO:     ***************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 15:54:10.403 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:54:13.612 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-07-31 15:54:13.789 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
Invoking rtc_set_log_level, log enabled:1, level:4
Configuring the webrtc log level.
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-07-31 15:54:13.931 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> DailyInputTransport#0
2025-07-31 15:54:13.931 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyInputTransport#0 -> RTVIProcessor#0
2025-07-31 15:54:13.931 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking RTVIProcessor#0 -> OpenAIUserContextAggregator#0
2025-07-31 15:54:13.931 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> GroqLLMService#0
2025-07-31 15:54:13.931 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking GroqLLMService#0 -> EdgeTTSProcessor#0
2025-07-31 15:54:13.931 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking EdgeTTSProcessor#0 -> TalkingAnimationProcessor#0
2025-07-31 15:54:13.931 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking TalkingAnimationProcessor#0 -> DailyOutputTransport#0
2025-07-31 15:54:13.931 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-07-31 15:54:13.931 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-07-31 15:54:13.932 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-07-31 15:54:13.932 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-07-31 15:54:13.933 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-07-31 15:54:13.935 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-07-31 15:54:13.936 | INFO     | pipecat.transports.services.daily:join:597 - Joining https://heweijie.daily.co/ygQ6SuuT4Ft6G1fvl004
2025-07-31 15:54:16.160 | INFO     | pipecat.transports.services.daily:join:617 - Joined https://heweijie.daily.co/ygQ6SuuT4Ft6G1fvl004
2025-07-31 15:54:16.160 | DEBUG    | pipecat.transports.services.daily:start_transcription:856 - Starting transcription: settings=language='en' model='nova-2-general' profanity_filter=True redact=False endpointing=True punctuate=True includeRawResponse=True extra={'interim_results': True}
2025-07-31 15:54:16.909 | DEBUG    | pipecat.transports.services.daily:start_audio_in_streaming:1396 - Start receiving audio
2025-07-31 15:54:16.911 | DEBUG    | pipecat.transports.services.daily:on_transcription_started:1221 - Transcription started: {'startedBy': 'f4209458-ef40-466f-8f49-2568261c02ea', 'model': 'nova-2-general', 'instanceId': 'a1f2f6b7-b1ac-4202-85e5-d446cb6c3d3f', 'transcriptId': 'c1ef8b1c-bfbe-4111-91e5-b8c0a56cad1f', 'language': 'en'}
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/HvFIIOFazYHhLp3d2Hsh
INFO:     *************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 15:56:36.222 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:56:40.278 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-07-31 15:56:40.448 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
Invoking rtc_set_log_level, log enabled:1, level:4
Configuring the webrtc log level.
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-07-31 15:56:40.632 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> DailyInputTransport#0
2025-07-31 15:56:40.632 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyInputTransport#0 -> RTVIProcessor#0
2025-07-31 15:56:40.632 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking RTVIProcessor#0 -> OpenAIUserContextAggregator#0
2025-07-31 15:56:40.633 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> GroqLLMService#0
2025-07-31 15:56:40.633 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking GroqLLMService#0 -> EdgeTTSProcessor#0
2025-07-31 15:56:40.633 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking EdgeTTSProcessor#0 -> TalkingAnimationProcessor#0
2025-07-31 15:56:40.633 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking TalkingAnimationProcessor#0 -> DailyOutputTransport#0
2025-07-31 15:56:40.633 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-07-31 15:56:40.633 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-07-31 15:56:40.633 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-07-31 15:56:40.634 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-07-31 15:56:40.635 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-07-31 15:56:40.636 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-07-31 15:56:40.637 | INFO     | pipecat.transports.services.daily:join:597 - Joining https://heweijie.daily.co/HvFIIOFazYHhLp3d2Hsh
2025-07-31 15:56:42.807 | INFO     | pipecat.transports.services.daily:join:617 - Joined https://heweijie.daily.co/HvFIIOFazYHhLp3d2Hsh
2025-07-31 15:56:42.807 | DEBUG    | pipecat.transports.services.daily:start_transcription:856 - Starting transcription: settings=language='en' model='nova-2-general' profanity_filter=True redact=False endpointing=True punctuate=True includeRawResponse=True extra={'interim_results': True}
2025-07-31 15:56:43.686 | DEBUG    | pipecat.transports.services.daily:start_audio_in_streaming:1396 - Start receiving audio
2025-07-31 15:56:43.686 | DEBUG    | pipecat.transports.services.daily:on_transcription_started:1221 - Transcription started: {'language': 'en', 'instanceId': 'a1f2f6b7-b1ac-4202-85e5-d446cb6c3d3f', 'startedBy': 'b1fc2a01-519a-48bf-a166-072682628e0b', 'transcriptId': 'a96e5095-b255-4414-949a-74abf2ee7e46', 'model': 'nova-2-general'}
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/A46gJ8x5hz7ISZ6Zn7tk
INFO:     **************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 15:57:19.312 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:57:23.131 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-07-31 15:57:23.312 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
Invoking rtc_set_log_level, log enabled:1, level:4
Configuring the webrtc log level.
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-07-31 15:57:23.431 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> DailyInputTransport#0
2025-07-31 15:57:23.432 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyInputTransport#0 -> RTVIProcessor#0
2025-07-31 15:57:23.432 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking RTVIProcessor#0 -> OpenAIUserContextAggregator#0
2025-07-31 15:57:23.432 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> GroqLLMService#0
2025-07-31 15:57:23.432 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking GroqLLMService#0 -> EdgeTTSProcessor#0
2025-07-31 15:57:23.432 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking EdgeTTSProcessor#0 -> TalkingAnimationProcessor#0
2025-07-31 15:57:23.432 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking TalkingAnimationProcessor#0 -> DailyOutputTransport#0
2025-07-31 15:57:23.432 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-07-31 15:57:23.432 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-07-31 15:57:23.432 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-07-31 15:57:23.433 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-07-31 15:57:23.433 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-07-31 15:57:23.435 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-07-31 15:57:23.436 | INFO     | pipecat.transports.services.daily:join:597 - Joining https://heweijie.daily.co/A46gJ8x5hz7ISZ6Zn7tk
2025-07-31 15:57:25.632 | INFO     | pipecat.transports.services.daily:join:617 - Joined https://heweijie.daily.co/A46gJ8x5hz7ISZ6Zn7tk
2025-07-31 15:57:25.632 | DEBUG    | pipecat.transports.services.daily:start_transcription:856 - Starting transcription: settings=language='en' model='nova-2-general' profanity_filter=True redact=False endpointing=True punctuate=True includeRawResponse=True extra={'interim_results': True}
2025-07-31 15:57:26.385 | DEBUG    | pipecat.transports.services.daily:on_transcription_started:1221 - Transcription started: {'model': 'nova-2-general', 'language': 'en', 'transcriptId': '8120758a-90f4-4952-a11c-d669cd5af581', 'instanceId': 'a1f2f6b7-b1ac-4202-85e5-d446cb6c3d3f', 'startedBy': '8402f188-fa83-4e02-8447-9a363951b41f'}
2025-07-31 15:57:26.386 | DEBUG    | pipecat.transports.services.daily:start_audio_in_streaming:1396 - Start receiving audio
2025-07-31 15:57:26.386 | INFO     | pipecat.transports.services.daily:_on_participant_joined:2246 - Participant joined b0230895-9e58-47e4-87ef-0c9f2d615056
2025-07-31 15:57:26.388 | DEBUG    | pipecat.transports.services.daily:capture_participant_audio:936 - Starting to capture [microphone] audio from participant b0230895-9e58-47e4-87ef-0c9f2d615056
2025-07-31 15:57:26.388 | INFO     | __main__:on_first_participant_joined:291 - Participant joined: {'info': {'isOwner': True, 'isLocal': False, 'joinedAt': 1753977442, 'permissions': {'canReceive': {'base': {'customAudio': {'*': True}, 'microphone': True, 'camera': True, 'screenAudio': True, 'customVideo': {'*': True}, 'screenVideo': True}}, 'canAdmin': ['transcription', 'streaming', 'participants'], 'hasPresence': True, 'canSend': ['camera', 'customVideo', 'microphone', 'customAudio', 'screenAudio', 'screenVideo']}}, 'media': {'microphone': {'state': 'loading', 'subscribed': 'subscribed'}, 'camera': {'state': 'off', 'offReasons': ['user'], 'subscribed': 'unsubscribed'}, 'customAudio': {}, 'screenAudio': {'offReasons': ['user'], 'state': 'off', 'subscribed': 'subscribed'}, 'screenVideo': {'state': 'off', 'subscribed': 'unsubscribed', 'offReasons': ['user']}, 'customVideo': {}}, 'id': 'b0230895-9e58-47e4-87ef-0c9f2d615056'}
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/KXIhF3XgR8Hmy5ZJD7a0
INFO:     **************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 15:57:52.345 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:57:55.946 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-07-31 15:57:56.134 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
Invoking rtc_set_log_level, log enabled:1, level:4
Configuring the webrtc log level.
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-07-31 15:57:56.311 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> DailyInputTransport#0
2025-07-31 15:57:56.312 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyInputTransport#0 -> RTVIProcessor#0
2025-07-31 15:57:56.312 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking RTVIProcessor#0 -> OpenAIUserContextAggregator#0
2025-07-31 15:57:56.312 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> GroqLLMService#0
2025-07-31 15:57:56.312 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking GroqLLMService#0 -> EdgeTTSProcessor#0
2025-07-31 15:57:56.312 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking EdgeTTSProcessor#0 -> TalkingAnimationProcessor#0
2025-07-31 15:57:56.312 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking TalkingAnimationProcessor#0 -> DailyOutputTransport#0
2025-07-31 15:57:56.313 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-07-31 15:57:56.313 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-07-31 15:57:56.313 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-07-31 15:57:56.313 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-07-31 15:57:56.314 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-07-31 15:57:56.316 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-07-31 15:57:56.317 | INFO     | pipecat.transports.services.daily:join:597 - Joining https://heweijie.daily.co/KXIhF3XgR8Hmy5ZJD7a0
2025-07-31 15:57:59.680 | INFO     | pipecat.transports.services.daily:join:617 - Joined https://heweijie.daily.co/KXIhF3XgR8Hmy5ZJD7a0
2025-07-31 15:57:59.680 | DEBUG    | pipecat.transports.services.daily:start_transcription:856 - Starting transcription: settings=language='en' model='nova-2-general' profanity_filter=True redact=False endpointing=True punctuate=True includeRawResponse=True extra={'interim_results': True}
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/jMaIGZ2DdIMDBpPeeoKV
INFO:     *************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 15:58:00.657 | DEBUG    | pipecat.transports.services.daily:on_transcription_started:1221 - Transcription started: {'startedBy': '5adf9b80-b852-474a-8726-264e34b4d138', 'instanceId': 'a1f2f6b7-b1ac-4202-85e5-d446cb6c3d3f', 'language': 'en', 'model': 'nova-2-general', 'transcriptId': '65102fb5-95b5-420c-b687-de0716ada35f'}
2025-07-31 15:58:00.657 | DEBUG    | pipecat.transports.services.daily:start_audio_in_streaming:1396 - Start receiving audio
2025-07-31 15:58:00.880 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:58:03.749 | INFO     | pipecat.transports.services.daily:_on_participant_left:2264 - Participant left b0230895-9e58-47e4-87ef-0c9f2d615056
2025-07-31 15:58:03.749 | INFO     | __main__:on_participant_left:296 - Participant left: {'id': 'b0230895-9e58-47e4-87ef-0c9f2d615056', 'info': {'permissions': {'canAdmin': ['transcription', 'streaming', 'participants'], 'canReceive': {'base': {'customVideo': {'*': True}, 'microphone': True, 'screenVideo': True, 'camera': True, 'customAudio': {'*': True}, 'screenAudio': True}}, 'hasPresence': True, 'canSend': ['camera', 'customVideo', 'microphone', 'customAudio', 'screenAudio', 'screenVideo']}, 'joinedAt': 1753977442, 'isLocal': False, 'isOwner': True}}
2025-07-31 15:58:03.750 | DEBUG    | pipecat.pipeline.task:_cancel:497 - Canceling pipeline task PipelineTask#0
2025-07-31 15:58:03.751 | INFO     | pipecat.transports.services.daily:leave:703 - Leaving https://heweijie.daily.co/A46gJ8x5hz7ISZ6Zn7tk
2025-07-31 15:58:03.751 | DEBUG    | pipecat.transports.services.daily:stop_transcription:872 - Stopping transcription
2025-07-31 15:58:03.810 | DEBUG    | pipecat.transports.services.daily:on_transcription_stopped:1232 - Transcription stopped
2025-07-31 15:58:03.844 | INFO     | pipecat.transports.services.daily:leave:715 - Left https://heweijie.daily.co/A46gJ8x5hz7ISZ6Zn7tk
2025-07-31 15:58:03.848 | DEBUG    | pipecat.pipeline.runner:run:83 - Runner PipelineRunner#0 finished running PipelineTask#0
{"timestamp":"2025-07-31T15:58:03.900830Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
{"timestamp":"2025-07-31T15:58:03.906252Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
2025-07-31 15:58:04.923 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-07-31 15:58:05.093 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
Invoking rtc_set_log_level, log enabled:1, level:4
Configuring the webrtc log level.
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-07-31 15:58:05.226 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> DailyInputTransport#0
2025-07-31 15:58:05.226 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyInputTransport#0 -> RTVIProcessor#0
2025-07-31 15:58:05.226 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking RTVIProcessor#0 -> OpenAIUserContextAggregator#0
2025-07-31 15:58:05.226 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> GroqLLMService#0
2025-07-31 15:58:05.226 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking GroqLLMService#0 -> EdgeTTSProcessor#0
2025-07-31 15:58:05.226 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking EdgeTTSProcessor#0 -> TalkingAnimationProcessor#0
2025-07-31 15:58:05.227 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking TalkingAnimationProcessor#0 -> DailyOutputTransport#0
2025-07-31 15:58:05.227 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-07-31 15:58:05.227 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-07-31 15:58:05.227 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-07-31 15:58:05.227 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-07-31 15:58:05.228 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-07-31 15:58:05.230 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-07-31 15:58:05.231 | INFO     | pipecat.transports.services.daily:join:597 - Joining https://heweijie.daily.co/jMaIGZ2DdIMDBpPeeoKV
2025-07-31 15:58:06.696 | INFO     | pipecat.transports.services.daily:join:617 - Joined https://heweijie.daily.co/jMaIGZ2DdIMDBpPeeoKV
2025-07-31 15:58:06.696 | DEBUG    | pipecat.transports.services.daily:start_transcription:856 - Starting transcription: settings=language='en' model='nova-2-general' profanity_filter=True redact=False endpointing=True punctuate=True includeRawResponse=True extra={'interim_results': True}
2025-07-31 15:58:07.444 | DEBUG    | pipecat.transports.services.daily:start_audio_in_streaming:1396 - Start receiving audio
2025-07-31 15:58:07.444 | DEBUG    | pipecat.transports.services.daily:on_transcription_started:1221 - Transcription started: {'language': 'en', 'model': 'nova-2-general', 'instanceId': 'a1f2f6b7-b1ac-4202-85e5-d446cb6c3d3f', 'startedBy': '24a4d6c6-138f-4878-8e8b-143eddf22653', 'transcriptId': '046e54a9-9ed0-448a-af62-39f7f7e8cddb'}
2025-07-31 15:58:26.890 | INFO     | pipecat.transports.services.daily:_on_participant_joined:2246 - Participant joined c611a8de-ae16-4229-96ce-12f30356d513
2025-07-31 15:58:26.892 | DEBUG    | pipecat.transports.services.daily:capture_participant_audio:936 - Starting to capture [microphone] audio from participant c611a8de-ae16-4229-96ce-12f30356d513
2025-07-31 15:58:26.892 | INFO     | __main__:on_first_participant_joined:291 - Participant joined: {'info': {'isLocal': False, 'isOwner': True, 'joinedAt': 1753977506, 'permissions': {'canAdmin': ['streaming', 'transcription', 'participants'], 'canSend': ['customAudio', 'microphone', 'screenAudio', 'camera', 'customVideo', 'screenVideo'], 'canReceive': {'base': {'customAudio': {'*': True}, 'camera': True, 'microphone': True, 'customVideo': {'*': True}, 'screenVideo': True, 'screenAudio': True}}, 'hasPresence': True}}, 'media': {'screenAudio': {'offReasons': ['user'], 'state': 'off', 'subscribed': 'subscribed'}, 'screenVideo': {'state': 'off', 'offReasons': ['user'], 'subscribed': 'unsubscribed'}, 'microphone': {'subscribed': 'subscribed', 'state': 'loading'}, 'customVideo': {}, 'camera': {'state': 'off', 'subscribed': 'unsubscribed', 'offReasons': ['user']}, 'customAudio': {}}, 'id': 'c611a8de-ae16-4229-96ce-12f30356d513'}
2025-07-31 15:58:29.513 | DEBUG    | pipecat.processors.frameworks.rtvi:_handle_client_ready:1431 - Received client-ready: version 1.0.0
2025-07-31 15:58:29.514 | DEBUG    | pipecat.processors.frameworks.rtvi:_handle_client_ready:1441 - Client Details: library='@pipecat-ai/client-react' library_version='1.0.0' platform='iOS' platform_version='17.6.1' platform_details={'platform_type': 'mobile', 'browser_version': '17.6', 'browser': 'Safari', 'engine': 'WebKit'}
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frameworks/rtvi.py:1549: DeprecationWarning: Configuration helpers are deprecated. If your application needs this behavior, use custom server and client messages.
  warnings.warn(
2025-07-31 15:58:29.601 | ERROR    | __main__:process_frame:143 - Groq API error: 404 - {"error":{"message":"Not Found"}}
2025-07-31 15:58:29.601 | ERROR    | pipecat.processors.frame_processor:__internal_push_frame:608 - Uncaught exception in GroqLLMService#0: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue'
Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-groq.py", line 304, in <module>
    asyncio.run(main())
    │       │   └ <function main at 0x70dbf4b77100>
    │       └ <function run at 0x70dc1508c680>
    └ <module 'asyncio' from '/usr/lib/python3.12/asyncio/__init__.py'>

  File "/usr/lib/python3.12/asyncio/runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object main at 0x70dbf4c066c0>
           │      └ <function Runner.run at 0x70dc13f37ec0>
           └ <asyncio.runners.Runner object at 0x70dbf4a0e480>
  File "/usr/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<main() running at /www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-g...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x70dc13f35b20>
           │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x70dbf4a0e480>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x70dc13f35a80>
    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x70dc13f37880>
    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 1987, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x70dc145c1da0>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "/usr/lib/python3.12/asyncio/events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/utils/asyncio/task_manager.py", line 236, in run_coroutine
    await coroutine
          └ <coroutine object FrameProcessor.__input_frame_task_handler at 0x70dbf0ac4480>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 654, in __input_frame_task_handler
    await self.process_frame(frame, direction)
          │    │             │      └ <FrameDirection.DOWNSTREAM: 1>
          │    │             └ TextFrame(id=73, name='TextFrame#0', pts=None, metadata={}, transport_source=None, transport_destination=None, text='你好！我是Gro...
          │    └ <function GroqLLMService.process_frame at 0x70dbf4a6a700>
          └ <__main__.GroqLLMService object at 0x70dbf4a0f9e0>

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-groq.py", line 144, in process_frame
    await self.push_frame(
          │    └ <function FrameProcessor.push_frame at 0x70dbf6e779c0>
          └ <__main__.GroqLLMService object at 0x70dbf4a0f9e0>

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 509, in push_frame
    await self.__internal_push_frame(frame, direction)
          │                          │      └ <FrameDirection.DOWNSTREAM: 1>
          │                          └ TextFrame(id=78, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无法...
          └ <__main__.GroqLLMService object at 0x70dbf4a0f9e0>
> File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 594, in __internal_push_frame
    await self._next.queue_frame(frame, direction)
          │    │     │           │      └ <FrameDirection.DOWNSTREAM: 1>
          │    │     │           └ TextFrame(id=78, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无法...
          │    │     └ <function FrameProcessor.queue_frame at 0x70dbf6e776a0>
          │    └ <__main__.EdgeTTSProcessor object at 0x70dbf4a6ce00>
          └ <__main__.GroqLLMService object at 0x70dbf4a0f9e0>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 457, in queue_frame
    await self.__input_queue.put((frame, direction, callback))
          │                       │      │          └ None
          │                       │      └ <FrameDirection.DOWNSTREAM: 1>
          │                       └ TextFrame(id=78, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无法...
          └ <__main__.EdgeTTSProcessor object at 0x70dbf4a6ce00>

AttributeError: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue'. Did you mean: '_FrameProcessor__input_event'?
2025-07-31 15:58:29.610 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#0(error: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue', fatal: False)
2025-07-31 15:58:59.234 | INFO     | pipecat.transports.services.daily:_on_participant_left:2264 - Participant left c611a8de-ae16-4229-96ce-12f30356d513
2025-07-31 15:58:59.235 | INFO     | __main__:on_participant_left:296 - Participant left: {'id': 'c611a8de-ae16-4229-96ce-12f30356d513', 'info': {'isLocal': False, 'isOwner': True, 'joinedAt': 1753977506, 'permissions': {'hasPresence': True, 'canSend': ['screenVideo', 'customVideo', 'customAudio', 'microphone', 'camera', 'screenAudio'], 'canReceive': {'base': {'camera': True, 'customAudio': {'*': True}, 'customVideo': {'*': True}, 'screenAudio': True, 'microphone': True, 'screenVideo': True}}, 'canAdmin': ['streaming', 'participants', 'transcription']}}}
2025-07-31 15:58:59.235 | DEBUG    | pipecat.pipeline.task:_cancel:497 - Canceling pipeline task PipelineTask#0
2025-07-31 15:58:59.236 | INFO     | pipecat.transports.services.daily:leave:703 - Leaving https://heweijie.daily.co/KXIhF3XgR8Hmy5ZJD7a0
2025-07-31 15:58:59.236 | DEBUG    | pipecat.transports.services.daily:stop_transcription:872 - Stopping transcription
2025-07-31 15:58:59.302 | DEBUG    | pipecat.transports.services.daily:on_transcription_stopped:1232 - Transcription stopped
2025-07-31 15:58:59.325 | INFO     | pipecat.transports.services.daily:leave:715 - Left https://heweijie.daily.co/KXIhF3XgR8Hmy5ZJD7a0
2025-07-31 15:58:59.329 | DEBUG    | pipecat.pipeline.runner:run:83 - Runner PipelineRunner#0 finished running PipelineTask#0
{"timestamp":"2025-07-31T15:58:59.383504Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
{"timestamp":"2025-07-31T15:58:59.389683Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/Kp9cZIDbbzXKSpgwqX8k
INFO:     ***************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 15:59:08.448 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:59:12.078 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-07-31 15:59:12.237 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
Invoking rtc_set_log_level, log enabled:1, level:4
Configuring the webrtc log level.
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-07-31 15:59:12.374 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> DailyInputTransport#0
2025-07-31 15:59:12.374 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyInputTransport#0 -> RTVIProcessor#0
2025-07-31 15:59:12.374 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking RTVIProcessor#0 -> OpenAIUserContextAggregator#0
2025-07-31 15:59:12.375 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> GroqLLMService#0
2025-07-31 15:59:12.375 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking GroqLLMService#0 -> EdgeTTSProcessor#0
2025-07-31 15:59:12.375 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking EdgeTTSProcessor#0 -> TalkingAnimationProcessor#0
2025-07-31 15:59:12.375 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking TalkingAnimationProcessor#0 -> DailyOutputTransport#0
2025-07-31 15:59:12.375 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-07-31 15:59:12.375 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-07-31 15:59:12.375 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-07-31 15:59:12.375 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-07-31 15:59:12.376 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-07-31 15:59:12.377 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-07-31 15:59:12.378 | INFO     | pipecat.transports.services.daily:join:597 - Joining https://heweijie.daily.co/Kp9cZIDbbzXKSpgwqX8k
2025-07-31 15:59:13.940 | WARNING  | pipecat.pipeline.task:_idle_timeout_detected:814 - Idle timeout detected. Last 10 frames received:
2025-07-31 15:59:13.941 | WARNING  | pipecat.pipeline.task:_idle_timeout_detected:820 - Idle pipeline detected, cancelling pipeline task...
2025-07-31 15:59:13.941 | DEBUG    | pipecat.pipeline.task:_cancel:497 - Canceling pipeline task PipelineTask#0
2025-07-31 15:59:13.943 | INFO     | pipecat.transports.services.daily:leave:703 - Leaving https://heweijie.daily.co/ygQ6SuuT4Ft6G1fvl004
2025-07-31 15:59:13.944 | DEBUG    | pipecat.transports.services.daily:stop_transcription:872 - Stopping transcription
2025-07-31 15:59:14.003 | DEBUG    | pipecat.transports.services.daily:on_transcription_stopped:1232 - Transcription stopped
2025-07-31 15:59:14.034 | INFO     | pipecat.transports.services.daily:leave:715 - Left https://heweijie.daily.co/ygQ6SuuT4Ft6G1fvl004
2025-07-31 15:59:14.039 | DEBUG    | pipecat.pipeline.runner:run:83 - Runner PipelineRunner#0 finished running PipelineTask#0
{"timestamp":"2025-07-31T15:59:14.090117Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
2025-07-31 15:59:14.595 | INFO     | pipecat.transports.services.daily:join:617 - Joined https://heweijie.daily.co/Kp9cZIDbbzXKSpgwqX8k
2025-07-31 15:59:14.596 | DEBUG    | pipecat.transports.services.daily:start_transcription:856 - Starting transcription: settings=language='en' model='nova-2-general' profanity_filter=True redact=False endpointing=True punctuate=True includeRawResponse=True extra={'interim_results': True}
2025-07-31 15:59:15.353 | DEBUG    | pipecat.transports.services.daily:start_audio_in_streaming:1396 - Start receiving audio
2025-07-31 15:59:15.354 | DEBUG    | pipecat.transports.services.daily:on_transcription_started:1221 - Transcription started: {'transcriptId': '0aa5ff00-0a4f-4c0b-a5b5-feab345e009b', 'model': 'nova-2-general', 'instanceId': 'a1f2f6b7-b1ac-4202-85e5-d446cb6c3d3f', 'startedBy': '982b5e9e-de17-44c0-a631-c452c0bbc67d', 'language': 'en'}
2025-07-31 15:59:30.794 | INFO     | pipecat.transports.services.daily:_on_participant_joined:2246 - Participant joined 4a1bbc61-78aa-45cf-97d3-81f87822d7c8
2025-07-31 15:59:30.795 | DEBUG    | pipecat.transports.services.daily:capture_participant_audio:936 - Starting to capture [microphone] audio from participant 4a1bbc61-78aa-45cf-97d3-81f87822d7c8
2025-07-31 15:59:30.796 | INFO     | __main__:on_first_participant_joined:291 - Participant joined: {'id': '4a1bbc61-78aa-45cf-97d3-81f87822d7c8', 'media': {'screenVideo': {'subscribed': 'unsubscribed', 'offReasons': ['user'], 'state': 'off'}, 'camera': {'state': 'off', 'subscribed': 'unsubscribed', 'offReasons': ['user']}, 'screenAudio': {'state': 'off', 'subscribed': 'subscribed', 'offReasons': ['user']}, 'customAudio': {}, 'customVideo': {}, 'microphone': {'state': 'loading', 'subscribed': 'subscribed'}}, 'info': {'isLocal': False, 'isOwner': True, 'permissions': {'canReceive': {'base': {'camera': True, 'customVideo': {'*': True}, 'microphone': True, 'screenAudio': True, 'customAudio': {'*': True}, 'screenVideo': True}}, 'hasPresence': True, 'canAdmin': ['streaming', 'transcription', 'participants'], 'canSend': ['microphone', 'camera', 'customAudio', 'screenVideo', 'customVideo', 'screenAudio']}, 'joinedAt': 1753977570}}
2025-07-31 15:59:32.270 | DEBUG    | pipecat.processors.frameworks.rtvi:_handle_client_ready:1431 - Received client-ready: version 1.0.0
2025-07-31 15:59:32.271 | DEBUG    | pipecat.processors.frameworks.rtvi:_handle_client_ready:1441 - Client Details: library='@pipecat-ai/client-react' library_version='1.0.0' platform='iOS' platform_version='17.6.1' platform_details={'platform_type': 'mobile', 'browser': 'Safari', 'browser_version': '17.6', 'engine': 'WebKit'}
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frameworks/rtvi.py:1549: DeprecationWarning: Configuration helpers are deprecated. If your application needs this behavior, use custom server and client messages.
  warnings.warn(
2025-07-31 15:59:32.419 | ERROR    | __main__:process_frame:143 - Groq API error: 404 - {"error":{"message":"Not Found"}}
2025-07-31 15:59:32.419 | ERROR    | pipecat.processors.frame_processor:__internal_push_frame:608 - Uncaught exception in GroqLLMService#0: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue'
Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-groq.py", line 304, in <module>
    asyncio.run(main())
    │       │   └ <function main at 0x7e0d400e3100>
    │       └ <function run at 0x7e0d6035c680>
    └ <module 'asyncio' from '/usr/lib/python3.12/asyncio/__init__.py'>

  File "/usr/lib/python3.12/asyncio/runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object main at 0x7e0d4016a6c0>
           │      └ <function Runner.run at 0x7e0d5f513ec0>
           └ <asyncio.runners.Runner object at 0x7e0d3ff7f980>
  File "/usr/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<main() running at /www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-g...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x7e0d5f511b20>
           │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x7e0d3ff7f980>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x7e0d5f511a80>
    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x7e0d5f513880>
    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 1987, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x7e0d5fb85da0>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "/usr/lib/python3.12/asyncio/events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/utils/asyncio/task_manager.py", line 236, in run_coroutine
    await coroutine
          └ <coroutine object FrameProcessor.__input_frame_task_handler at 0x7e0d3c1dc480>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 654, in __input_frame_task_handler
    await self.process_frame(frame, direction)
          │    │             │      └ <FrameDirection.DOWNSTREAM: 1>
          │    │             └ TextFrame(id=69, name='TextFrame#0', pts=None, metadata={}, transport_source=None, transport_destination=None, text='你好！我是Gro...
          │    └ <function GroqLLMService.process_frame at 0x7e0d3ffda700>
          └ <__main__.GroqLLMService object at 0x7e0d3ff7fc20>

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-groq.py", line 144, in process_frame
    await self.push_frame(
          │    └ <function FrameProcessor.push_frame at 0x7e0d4240f880>
          └ <__main__.GroqLLMService object at 0x7e0d3ff7fc20>

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 509, in push_frame
    await self.__internal_push_frame(frame, direction)
          │                          │      └ <FrameDirection.DOWNSTREAM: 1>
          │                          └ TextFrame(id=77, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无法...
          └ <__main__.GroqLLMService object at 0x7e0d3ff7fc20>
> File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 594, in __internal_push_frame
    await self._next.queue_frame(frame, direction)
          │    │     │           │      └ <FrameDirection.DOWNSTREAM: 1>
          │    │     │           └ TextFrame(id=77, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无法...
          │    │     └ <function FrameProcessor.queue_frame at 0x7e0d4240f560>
          │    └ <__main__.EdgeTTSProcessor object at 0x7e0d3ffdcd40>
          └ <__main__.GroqLLMService object at 0x7e0d3ff7fc20>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 457, in queue_frame
    await self.__input_queue.put((frame, direction, callback))
          │                       │      │          └ None
          │                       │      └ <FrameDirection.DOWNSTREAM: 1>
          │                       └ TextFrame(id=77, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无法...
          └ <__main__.EdgeTTSProcessor object at 0x7e0d3ffdcd40>

AttributeError: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue'. Did you mean: '_FrameProcessor__input_event'?
2025-07-31 15:59:32.430 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#0(error: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue', fatal: False)
Creating room
Room URL: https://heweijie.daily.co/3G8tQoQGipBcMc4HkyUM
INFO:     **************:55776 - "GET / HTTP/1.1" 307 Temporary Redirect
2025-07-31 16:00:08.220 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
Creating room
Room URL: https://heweijie.daily.co/WRJFsUPKdJWBSat4mJwJ
INFO:     **************:55800 - "GET / HTTP/1.1" 307 Temporary Redirect
2025-07-31 16:00:10.832 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
WARNING:  Invalid HTTP request received.
WARNING:  Invalid HTTP request received.
2025-07-31 16:00:12.206 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-07-31 16:00:12.412 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
Invoking rtc_set_log_level, log enabled:1, level:4
Configuring the webrtc log level.
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-07-31 16:00:12.596 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> DailyInputTransport#0
2025-07-31 16:00:12.596 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyInputTransport#0 -> RTVIProcessor#0
2025-07-31 16:00:12.596 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking RTVIProcessor#0 -> OpenAIUserContextAggregator#0
2025-07-31 16:00:12.596 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> GroqLLMService#0
2025-07-31 16:00:12.596 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking GroqLLMService#0 -> EdgeTTSProcessor#0
2025-07-31 16:00:12.596 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking EdgeTTSProcessor#0 -> TalkingAnimationProcessor#0
2025-07-31 16:00:12.596 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking TalkingAnimationProcessor#0 -> DailyOutputTransport#0
2025-07-31 16:00:12.596 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-07-31 16:00:12.596 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-07-31 16:00:12.597 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-07-31 16:00:12.597 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-07-31 16:00:12.598 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-07-31 16:00:12.600 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-07-31 16:00:12.603 | INFO     | pipecat.transports.services.daily:join:597 - Joining https://heweijie.daily.co/3G8tQoQGipBcMc4HkyUM
2025-07-31 16:00:14.417 | INFO     | pipecat.transports.services.daily:join:617 - Joined https://heweijie.daily.co/3G8tQoQGipBcMc4HkyUM
2025-07-31 16:00:14.418 | DEBUG    | pipecat.transports.services.daily:start_transcription:856 - Starting transcription: settings=language='en' model='nova-2-general' profanity_filter=True redact=False endpointing=True punctuate=True includeRawResponse=True extra={'interim_results': True}
2025-07-31 16:00:14.546 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-07-31 16:00:14.772 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
Invoking rtc_set_log_level, log enabled:1, level:4
Configuring the webrtc log level.
2025-07-31 16:00:15.192 | DEBUG    | pipecat.transports.services.daily:start_audio_in_streaming:1396 - Start receiving audio
2025-07-31 16:00:15.195 | DEBUG    | pipecat.transports.services.daily:on_transcription_started:1221 - Transcription started: {'transcriptId': 'dfb9c986-0b4b-4e8c-8285-6910640daec6', 'instanceId': 'a1f2f6b7-b1ac-4202-85e5-d446cb6c3d3f', 'startedBy': '27250ccc-8d78-4071-87d9-1d0889116d16', 'model': 'nova-2-general', 'language': 'en'}
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-07-31 16:00:15.236 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> DailyInputTransport#0
2025-07-31 16:00:15.236 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyInputTransport#0 -> RTVIProcessor#0
2025-07-31 16:00:15.236 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking RTVIProcessor#0 -> OpenAIUserContextAggregator#0
2025-07-31 16:00:15.237 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> GroqLLMService#0
2025-07-31 16:00:15.237 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking GroqLLMService#0 -> EdgeTTSProcessor#0
2025-07-31 16:00:15.237 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking EdgeTTSProcessor#0 -> TalkingAnimationProcessor#0
2025-07-31 16:00:15.237 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking TalkingAnimationProcessor#0 -> DailyOutputTransport#0
2025-07-31 16:00:15.237 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-07-31 16:00:15.237 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-07-31 16:00:15.237 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-07-31 16:00:15.238 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-07-31 16:00:15.239 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-07-31 16:00:15.243 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-07-31 16:00:15.245 | INFO     | pipecat.transports.services.daily:join:597 - Joining https://heweijie.daily.co/WRJFsUPKdJWBSat4mJwJ
2025-07-31 16:00:17.446 | INFO     | pipecat.transports.services.daily:join:617 - Joined https://heweijie.daily.co/WRJFsUPKdJWBSat4mJwJ
2025-07-31 16:00:17.446 | DEBUG    | pipecat.transports.services.daily:start_transcription:856 - Starting transcription: settings=language='en' model='nova-2-general' profanity_filter=True redact=False endpointing=True punctuate=True includeRawResponse=True extra={'interim_results': True}
2025-07-31 16:00:18.300 | DEBUG    | pipecat.transports.services.daily:on_transcription_started:1221 - Transcription started: {'transcriptId': 'ed8dba93-dbe9-4b20-aaf3-9da1185e39be', 'instanceId': 'a1f2f6b7-b1ac-4202-85e5-d446cb6c3d3f', 'startedBy': 'e1256226-fd8c-49d2-9d99-35520d827ed3', 'language': 'en', 'model': 'nova-2-general'}
2025-07-31 16:00:18.302 | DEBUG    | pipecat.transports.services.daily:start_audio_in_streaming:1396 - Start receiving audio
2025-07-31 16:01:40.641 | WARNING  | pipecat.pipeline.task:_idle_timeout_detected:814 - Idle timeout detected. Last 10 frames received:
2025-07-31 16:01:40.642 | WARNING  | pipecat.pipeline.task:_idle_timeout_detected:820 - Idle pipeline detected, cancelling pipeline task...
2025-07-31 16:01:40.642 | DEBUG    | pipecat.pipeline.task:_cancel:497 - Canceling pipeline task PipelineTask#0
2025-07-31 16:01:40.647 | INFO     | pipecat.transports.services.daily:leave:703 - Leaving https://heweijie.daily.co/HvFIIOFazYHhLp3d2Hsh
2025-07-31 16:01:40.647 | DEBUG    | pipecat.transports.services.daily:stop_transcription:872 - Stopping transcription
2025-07-31 16:01:40.707 | DEBUG    | pipecat.transports.services.daily:on_transcription_stopped:1232 - Transcription stopped
2025-07-31 16:01:40.736 | INFO     | pipecat.transports.services.daily:leave:715 - Left https://heweijie.daily.co/HvFIIOFazYHhLp3d2Hsh
2025-07-31 16:01:40.741 | DEBUG    | pipecat.pipeline.runner:run:83 - Runner PipelineRunner#0 finished running PipelineTask#0
{"timestamp":"2025-07-31T16:01:40.796287Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
2025-07-31 16:03:05.236 | WARNING  | pipecat.pipeline.task:_idle_timeout_detected:814 - Idle timeout detected. Last 10 frames received:
2025-07-31 16:03:05.237 | WARNING  | pipecat.pipeline.task:_idle_timeout_detected:820 - Idle pipeline detected, cancelling pipeline task...
2025-07-31 16:03:05.238 | DEBUG    | pipecat.pipeline.task:_cancel:497 - Canceling pipeline task PipelineTask#0
2025-07-31 16:03:05.240 | INFO     | pipecat.transports.services.daily:leave:703 - Leaving https://heweijie.daily.co/jMaIGZ2DdIMDBpPeeoKV
2025-07-31 16:03:05.241 | DEBUG    | pipecat.transports.services.daily:stop_transcription:872 - Stopping transcription
2025-07-31 16:03:05.307 | DEBUG    | pipecat.transports.services.daily:on_transcription_stopped:1232 - Transcription stopped
2025-07-31 16:03:05.337 | INFO     | pipecat.transports.services.daily:leave:715 - Left https://heweijie.daily.co/jMaIGZ2DdIMDBpPeeoKV
2025-07-31 16:03:05.344 | DEBUG    | pipecat.pipeline.runner:run:83 - Runner PipelineRunner#0 finished running PipelineTask#0
{"timestamp":"2025-07-31T16:03:05.395745Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/m36DdG45fhZx7IfCZLnN
INFO:     *************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 16:03:14.100 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 16:03:17.903 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-07-31 16:03:18.100 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
Invoking rtc_set_log_level, log enabled:1, level:4
Configuring the webrtc log level.
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-07-31 16:03:18.228 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> DailyInputTransport#0
2025-07-31 16:03:18.228 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyInputTransport#0 -> RTVIProcessor#0
2025-07-31 16:03:18.229 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking RTVIProcessor#0 -> OpenAIUserContextAggregator#0
2025-07-31 16:03:18.229 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> GroqLLMService#0
2025-07-31 16:03:18.229 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking GroqLLMService#0 -> EdgeTTSProcessor#0
2025-07-31 16:03:18.229 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking EdgeTTSProcessor#0 -> TalkingAnimationProcessor#0
2025-07-31 16:03:18.229 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking TalkingAnimationProcessor#0 -> DailyOutputTransport#0
2025-07-31 16:03:18.229 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-07-31 16:03:18.229 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-07-31 16:03:18.229 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-07-31 16:03:18.230 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-07-31 16:03:18.231 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-07-31 16:03:18.232 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-07-31 16:03:18.233 | INFO     | pipecat.transports.services.daily:join:597 - Joining https://heweijie.daily.co/m36DdG45fhZx7IfCZLnN
2025-07-31 16:03:21.374 | INFO     | pipecat.transports.services.daily:join:617 - Joined https://heweijie.daily.co/m36DdG45fhZx7IfCZLnN
2025-07-31 16:03:24.610 | DEBUG    | pipecat.transports.services.daily:start_transcription:856 - Starting transcription: settings=language='en' model='nova-2-general' profanity_filter=True redact=False endpointing=True punctuate=True includeRawResponse=True extra={'interim_results': True}
2025-07-31 16:03:25.402 | DEBUG    | pipecat.transports.services.daily:on_transcription_started:1221 - Transcription started: {'startedBy': 'f7afa18e-bfae-4c39-8f07-99d9f439b7bd', 'language': 'en', 'model': 'nova-2-general', 'instanceId': 'a1f2f6b7-b1ac-4202-85e5-d446cb6c3d3f', 'transcriptId': '7526468a-b7b2-4d2e-aefd-0d481be47ae5'}
2025-07-31 16:03:25.403 | DEBUG    | pipecat.transports.services.daily:start_audio_in_streaming:1396 - Start receiving audio
2025-07-31 16:03:32.790 | INFO     | pipecat.transports.services.daily:_on_participant_joined:2246 - Participant joined eecdb786-1777-47f5-973e-fe24fc7890e4
2025-07-31 16:03:27.195 | INFO     | pipecat.transports.services.daily:_on_participant_left:2264 - Participant left 4a1bbc61-78aa-45cf-97d3-81f87822d7c8
2025-07-31 16:03:32.791 | INFO     | __main__:on_participant_left:296 - Participant left: {'id': '4a1bbc61-78aa-45cf-97d3-81f87822d7c8', 'info': {'permissions': {'canSend': ['customVideo', 'camera', 'microphone', 'screenVideo', 'screenAudio', 'customAudio'], 'canReceive': {'base': {'camera': True, 'customVideo': {'*': True}, 'customAudio': {'*': True}, 'screenVideo': True, 'microphone': True, 'screenAudio': True}}, 'hasPresence': True, 'canAdmin': ['transcription', 'participants', 'streaming']}, 'joinedAt': 1753977570, 'isLocal': False, 'isOwner': True}}
2025-07-31 16:03:32.792 | DEBUG    | pipecat.pipeline.task:_cancel:497 - Canceling pipeline task PipelineTask#0
2025-07-31 16:03:32.793 | DEBUG    | pipecat.transports.services.daily:capture_participant_audio:936 - Starting to capture [microphone] audio from participant eecdb786-1777-47f5-973e-fe24fc7890e4
2025-07-31 16:03:32.794 | INFO     | __main__:on_first_participant_joined:291 - Participant joined: {'info': {'isLocal': False, 'joinedAt': 1753977798, 'isOwner': True, 'permissions': {'canReceive': {'base': {'screenAudio': True, 'screenVideo': True, 'customAudio': {'*': True}, 'camera': True, 'customVideo': {'*': True}, 'microphone': True}}, 'canAdmin': ['participants', 'transcription', 'streaming'], 'canSend': ['screenAudio', 'customAudio', 'microphone', 'screenVideo', 'camera', 'customVideo'], 'hasPresence': True}}, 'id': 'eecdb786-1777-47f5-973e-fe24fc7890e4', 'media': {'customVideo': {}, 'screenAudio': {'subscribed': 'subscribed', 'state': 'off', 'offReasons': ['user']}, 'microphone': {'state': 'loading', 'subscribed': 'subscribed'}, 'camera': {'subscribed': 'unsubscribed', 'offReasons': ['user'], 'state': 'off'}, 'customAudio': {}, 'screenVideo': {'subscribed': 'unsubscribed', 'state': 'off', 'offReasons': ['user']}}}
2025-07-31 16:03:32.794 | DEBUG    | pipecat.processors.frameworks.rtvi:_handle_client_ready:1431 - Received client-ready: version 1.0.0
2025-07-31 16:03:32.795 | DEBUG    | pipecat.processors.frameworks.rtvi:_handle_client_ready:1441 - Client Details: library='@pipecat-ai/client-react' library_version='1.0.0' platform='Windows' platform_version='NT 10.0' platform_details={'browser': 'Chrome', 'browser_version': '*********', 'platform_type': 'desktop', 'engine': 'Blink'}
2025-07-31 16:03:32.796 | INFO     | pipecat.transports.services.daily:leave:703 - Leaving https://heweijie.daily.co/Kp9cZIDbbzXKSpgwqX8k
2025-07-31 16:03:32.796 | DEBUG    | pipecat.transports.services.daily:stop_transcription:872 - Stopping transcription
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frameworks/rtvi.py:1549: DeprecationWarning: Configuration helpers are deprecated. If your application needs this behavior, use custom server and client messages.
  warnings.warn(
2025-07-31 16:03:32.867 | DEBUG    | pipecat.transports.services.daily:on_transcription_stopped:1232 - Transcription stopped
2025-07-31 16:03:32.899 | ERROR    | __main__:process_frame:143 - Groq API error: 404 - {"error":{"message":"Not Found"}}
2025-07-31 16:03:32.900 | ERROR    | pipecat.processors.frame_processor:__internal_push_frame:608 - Uncaught exception in GroqLLMService#0: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue'
Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-groq.py", line 304, in <module>
    asyncio.run(main())
    │       │   └ <function main at 0x783ba546f100>
    │       └ <function run at 0x783bc575c680>
    └ <module 'asyncio' from '/usr/lib/python3.12/asyncio/__init__.py'>

  File "/usr/lib/python3.12/asyncio/runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object main at 0x783ba56fe6c0>
           │      └ <function Runner.run at 0x783bc483bec0>
           └ <asyncio.runners.Runner object at 0x783ba53038f0>
  File "/usr/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<main() running at /www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-g...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x783bc4839b20>
           │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x783ba53038f0>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x783bc4839a80>
    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x783bc483b880>
    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 1987, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x783bc4971da0>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "/usr/lib/python3.12/asyncio/events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/utils/asyncio/task_manager.py", line 236, in run_coroutine
    await coroutine
          └ <coroutine object FrameProcessor.__input_frame_task_handler at 0x783ba13b8480>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 654, in __input_frame_task_handler
    await self.process_frame(frame, direction)
          │    │             │      └ <FrameDirection.DOWNSTREAM: 1>
          │    │             └ TextFrame(id=38, name='TextFrame#0', pts=None, metadata={}, transport_source=None, transport_destination=None, text='你好！我是Gro...
          │    └ <function GroqLLMService.process_frame at 0x783ba5362700>
          └ <__main__.GroqLLMService object at 0x783ba53038c0>

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-groq.py", line 144, in process_frame
    await self.push_frame(
          │    └ <function FrameProcessor.push_frame at 0x783ba65c3880>
          └ <__main__.GroqLLMService object at 0x783ba53038c0>

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 509, in push_frame
    await self.__internal_push_frame(frame, direction)
          │                          │      └ <FrameDirection.DOWNSTREAM: 1>
          │                          └ TextFrame(id=44, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无法...
          └ <__main__.GroqLLMService object at 0x783ba53038c0>
> File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 594, in __internal_push_frame
    await self._next.queue_frame(frame, direction)
          │    │     │           │      └ <FrameDirection.DOWNSTREAM: 1>
          │    │     │           └ TextFrame(id=44, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无法...
          │    │     └ <function FrameProcessor.queue_frame at 0x783ba65c3560>
          │    └ <__main__.EdgeTTSProcessor object at 0x783ba56df350>
          └ <__main__.GroqLLMService object at 0x783ba53038c0>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 457, in queue_frame
    await self.__input_queue.put((frame, direction, callback))
          │                       │      │          └ None
          │                       │      └ <FrameDirection.DOWNSTREAM: 1>
          │                       └ TextFrame(id=44, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无法...
          └ <__main__.EdgeTTSProcessor object at 0x783ba56df350>

AttributeError: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue'. Did you mean: '_FrameProcessor__input_event'?
2025-07-31 16:03:32.910 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#0(error: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue', fatal: False)
2025-07-31 16:03:32.911 | INFO     | pipecat.transports.services.daily:leave:715 - Left https://heweijie.daily.co/Kp9cZIDbbzXKSpgwqX8k
2025-07-31 16:03:32.917 | DEBUG    | pipecat.pipeline.runner:run:83 - Runner PipelineRunner#0 finished running PipelineTask#0
{"timestamp":"2025-07-31T16:03:32.967938Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
{"timestamp":"2025-07-31T16:03:32.968338Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
2025-07-31 16:04:59.611 | DEBUG    | pipecat.transports.base_input:_handle_user_interruption:348 - User started speaking
2025-07-31 16:05:00.632 | DEBUG    | pipecat.transports.base_input:_handle_user_interruption:372 - User stopped speaking
2025-07-31 16:05:12.606 | WARNING  | pipecat.pipeline.task:_idle_timeout_detected:814 - Idle timeout detected. Last 10 frames received:
2025-07-31 16:05:12.607 | WARNING  | pipecat.pipeline.task:_idle_timeout_detected:820 - Idle pipeline detected, cancelling pipeline task...
2025-07-31 16:05:12.607 | DEBUG    | pipecat.pipeline.task:_cancel:497 - Canceling pipeline task PipelineTask#0
2025-07-31 16:05:12.611 | INFO     | pipecat.transports.services.daily:leave:703 - Leaving https://heweijie.daily.co/3G8tQoQGipBcMc4HkyUM
2025-07-31 16:05:12.611 | DEBUG    | pipecat.transports.services.daily:stop_transcription:872 - Stopping transcription
2025-07-31 16:05:12.672 | DEBUG    | pipecat.transports.services.daily:on_transcription_stopped:1232 - Transcription stopped
2025-07-31 16:05:12.698 | INFO     | pipecat.transports.services.daily:leave:715 - Left https://heweijie.daily.co/3G8tQoQGipBcMc4HkyUM
2025-07-31 16:05:12.704 | DEBUG    | pipecat.pipeline.runner:run:83 - Runner PipelineRunner#0 finished running PipelineTask#0
{"timestamp":"2025-07-31T16:05:12.754798Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
2025-07-31 16:05:15.248 | WARNING  | pipecat.pipeline.task:_idle_timeout_detected:814 - Idle timeout detected. Last 10 frames received:
2025-07-31 16:05:15.250 | WARNING  | pipecat.pipeline.task:_idle_timeout_detected:820 - Idle pipeline detected, cancelling pipeline task...
2025-07-31 16:05:15.250 | DEBUG    | pipecat.pipeline.task:_cancel:497 - Canceling pipeline task PipelineTask#0
2025-07-31 16:05:15.252 | INFO     | pipecat.transports.services.daily:leave:703 - Leaving https://heweijie.daily.co/WRJFsUPKdJWBSat4mJwJ
2025-07-31 16:05:15.252 | DEBUG    | pipecat.transports.services.daily:stop_transcription:872 - Stopping transcription
2025-07-31 16:05:15.314 | DEBUG    | pipecat.transports.services.daily:on_transcription_stopped:1232 - Transcription stopped
2025-07-31 16:05:15.336 | INFO     | pipecat.transports.services.daily:leave:715 - Left https://heweijie.daily.co/WRJFsUPKdJWBSat4mJwJ
2025-07-31 16:05:15.343 | DEBUG    | pipecat.pipeline.runner:run:83 - Runner PipelineRunner#0 finished running PipelineTask#0
{"timestamp":"2025-07-31T16:05:15.394780Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
