2025-07-31 15:47:45.243 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
INFO:     Started server process [62225]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     <PERSON>vicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/uQgzGdtlWQLAaznOzNJB
INFO:     ***************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 15:48:02.339 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:48:06.111 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-07-31 15:48:06.322 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
Invoking rtc_set_log_level, log enabled:1, level:4
Configuring the webrtc log level.
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-07-31 15:48:06.484 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> DailyInputTransport#0
2025-07-31 15:48:06.484 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyInputTransport#0 -> RTVIProcessor#0
2025-07-31 15:48:06.484 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking RTVIProcessor#0 -> OpenAIUserContextAggregator#0
2025-07-31 15:48:06.484 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> GroqLLMService#0
2025-07-31 15:48:06.484 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking GroqLLMService#0 -> EdgeTTSProcessor#0
2025-07-31 15:48:06.484 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking EdgeTTSProcessor#0 -> TalkingAnimationProcessor#0
2025-07-31 15:48:06.485 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking TalkingAnimationProcessor#0 -> DailyOutputTransport#0
2025-07-31 15:48:06.485 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-07-31 15:48:06.485 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-07-31 15:48:06.485 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-07-31 15:48:06.485 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-07-31 15:48:06.486 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-07-31 15:48:06.487 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-07-31 15:48:06.488 | INFO     | pipecat.transports.services.daily:join:597 - Joining https://heweijie.daily.co/uQgzGdtlWQLAaznOzNJB
2025-07-31 15:48:08.749 | INFO     | pipecat.transports.services.daily:join:617 - Joined https://heweijie.daily.co/uQgzGdtlWQLAaznOzNJB
2025-07-31 15:48:08.749 | DEBUG    | pipecat.transports.services.daily:start_transcription:856 - Starting transcription: settings=language='en' model='nova-2-general' profanity_filter=True redact=False endpointing=True punctuate=True includeRawResponse=True extra={'interim_results': True}
2025-07-31 15:48:09.667 | DEBUG    | pipecat.transports.services.daily:on_transcription_started:1221 - Transcription started: {'language': 'en', 'model': 'nova-2-general', 'transcriptId': '784dba78-a2ce-4204-9528-48693618af1b', 'instanceId': 'a1f2f6b7-b1ac-4202-85e5-d446cb6c3d3f', 'startedBy': '5d52e5d3-d81e-4de6-a5a9-1859dec9481c'}
2025-07-31 15:48:09.668 | DEBUG    | pipecat.transports.services.daily:start_audio_in_streaming:1396 - Start receiving audio
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/TM6PlNXqSt1xCEeT9SBy
INFO:     *************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 15:50:26.599 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:50:30.399 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-07-31 15:50:30.591 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
Invoking rtc_set_log_level, log enabled:1, level:4
Configuring the webrtc log level.
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-07-31 15:50:30.705 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> DailyInputTransport#0
2025-07-31 15:50:30.705 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyInputTransport#0 -> RTVIProcessor#0
2025-07-31 15:50:30.705 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking RTVIProcessor#0 -> OpenAIUserContextAggregator#0
2025-07-31 15:50:30.705 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> GroqLLMService#0
2025-07-31 15:50:30.706 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking GroqLLMService#0 -> EdgeTTSProcessor#0
2025-07-31 15:50:30.706 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking EdgeTTSProcessor#0 -> TalkingAnimationProcessor#0
2025-07-31 15:50:30.706 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking TalkingAnimationProcessor#0 -> DailyOutputTransport#0
2025-07-31 15:50:30.706 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-07-31 15:50:30.706 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-07-31 15:50:30.706 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-07-31 15:50:30.706 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-07-31 15:50:30.707 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-07-31 15:50:30.708 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-07-31 15:50:30.709 | INFO     | pipecat.transports.services.daily:join:597 - Joining https://heweijie.daily.co/TM6PlNXqSt1xCEeT9SBy
2025-07-31 15:50:32.227 | INFO     | pipecat.transports.services.daily:join:617 - Joined https://heweijie.daily.co/TM6PlNXqSt1xCEeT9SBy
2025-07-31 15:50:32.228 | DEBUG    | pipecat.transports.services.daily:start_transcription:856 - Starting transcription: settings=language='en' model='nova-2-general' profanity_filter=True redact=False endpointing=True punctuate=True includeRawResponse=True extra={'interim_results': True}
2025-07-31 15:50:33.296 | DEBUG    | pipecat.transports.services.daily:on_transcription_started:1221 - Transcription started: {'instanceId': 'a1f2f6b7-b1ac-4202-85e5-d446cb6c3d3f', 'transcriptId': '6346811f-83b0-4ed5-af03-86c989ed6317', 'language': 'en', 'startedBy': '338da51d-32a7-49a5-9760-3f4d589f81dd', 'model': 'nova-2-general'}
2025-07-31 15:50:33.297 | DEBUG    | pipecat.transports.services.daily:start_audio_in_streaming:1396 - Start receiving audio
2025-07-31 15:50:33.298 | INFO     | pipecat.transports.services.daily:_on_participant_joined:2246 - Participant joined 5a473c88-7020-449c-9920-ea0ad32eec2b
2025-07-31 15:50:33.299 | DEBUG    | pipecat.transports.services.daily:capture_participant_audio:936 - Starting to capture [microphone] audio from participant 5a473c88-7020-449c-9920-ea0ad32eec2b
2025-07-31 15:50:33.300 | INFO     | __main__:on_first_participant_joined:291 - Participant joined: {'info': {'isLocal': False, 'joinedAt': 1753977031, 'isOwner': True, 'permissions': {'canAdmin': ['streaming', 'transcription', 'participants'], 'hasPresence': True, 'canReceive': {'base': {'screenVideo': True, 'customVideo': {'*': True}, 'screenAudio': True, 'camera': True, 'customAudio': {'*': True}, 'microphone': True}}, 'canSend': ['screenAudio', 'microphone', 'camera', 'customAudio', 'screenVideo', 'customVideo']}}, 'id': '5a473c88-7020-449c-9920-ea0ad32eec2b', 'media': {'camera': {'offReasons': ['user'], 'state': 'off', 'subscribed': 'unsubscribed'}, 'screenAudio': {'state': 'off', 'offReasons': ['user'], 'subscribed': 'subscribed'}, 'microphone': {'state': 'loading', 'subscribed': 'subscribed'}, 'customAudio': {}, 'customVideo': {}, 'screenVideo': {'offReasons': ['user'], 'state': 'off', 'subscribed': 'unsubscribed'}}}
2025-07-31 15:50:33.300 | DEBUG    | pipecat.processors.frameworks.rtvi:_handle_client_ready:1431 - Received client-ready: version 1.0.0
2025-07-31 15:50:33.300 | DEBUG    | pipecat.processors.frameworks.rtvi:_handle_client_ready:1441 - Client Details: library='@pipecat-ai/client-react' library_version='1.0.0' platform='Windows' platform_version='NT 10.0' platform_details={'browser': 'Chrome', 'platform_type': 'desktop', 'engine': 'Blink', 'browser_version': '*********'}
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frameworks/rtvi.py:1549: DeprecationWarning: Configuration helpers are deprecated. If your application needs this behavior, use custom server and client messages.
  warnings.warn(
2025-07-31 15:50:33.417 | ERROR    | __main__:process_frame:143 - Groq API error: 404 - {"error":{"message":"Not Found"}}
2025-07-31 15:50:33.417 | ERROR    | pipecat.processors.frame_processor:__internal_push_frame:608 - Uncaught exception in GroqLLMService#0: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue'
Traceback (most recent call last):

  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-groq.py", line 304, in <module>
    asyncio.run(main())
    │       │   └ <function main at 0x77125afeb100>
    │       └ <function run at 0x77127b4e0680>
    └ <module 'asyncio' from '/usr/lib/python3.12/asyncio/__init__.py'>

  File "/usr/lib/python3.12/asyncio/runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object main at 0x77125b0766c0>
           │      └ <function Runner.run at 0x77127a337ec0>
           └ <asyncio.runners.Runner object at 0x77125ae89790>
  File "/usr/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<main() running at /www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-g...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x77127a335b20>
           │    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x77125ae89790>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x77127a335a80>
    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x77127a337880>
    └ <_UnixSelectorEventLoop running=True closed=False debug=False>
  File "/usr/lib/python3.12/asyncio/base_events.py", line 1987, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x77127aa35da0>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "/usr/lib/python3.12/asyncio/events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/utils/asyncio/task_manager.py", line 236, in run_coroutine
    await coroutine
          └ <coroutine object FrameProcessor.__input_frame_task_handler at 0x7712566e0480>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 654, in __input_frame_task_handler
    await self.process_frame(frame, direction)
          │    │             │      └ <FrameDirection.DOWNSTREAM: 1>
          │    │             └ TextFrame(id=38, name='TextFrame#0', pts=None, metadata={}, transport_source=None, transport_destination=None, text='你好！我是Gro...
          │    └ <function GroqLLMService.process_frame at 0x77125aee2700>
          └ <__main__.GroqLLMService object at 0x77125ae895b0>

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-groq.py", line 144, in process_frame
    await self.push_frame(
          │    └ <function FrameProcessor.push_frame at 0x77125c1cf880>
          └ <__main__.GroqLLMService object at 0x77125ae895b0>

  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 509, in push_frame
    await self.__internal_push_frame(frame, direction)
          │                          │      └ <FrameDirection.DOWNSTREAM: 1>
          │                          └ TextFrame(id=45, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无法...
          └ <__main__.GroqLLMService object at 0x77125ae895b0>
> File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 594, in __internal_push_frame
    await self._next.queue_frame(frame, direction)
          │    │     │           │      └ <FrameDirection.DOWNSTREAM: 1>
          │    │     │           └ TextFrame(id=45, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无法...
          │    │     └ <function FrameProcessor.queue_frame at 0x77125c1cf560>
          │    └ <__main__.EdgeTTSProcessor object at 0x77125aee4d40>
          └ <__main__.GroqLLMService object at 0x77125ae895b0>
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/processors/frame_processor.py", line 457, in queue_frame
    await self.__input_queue.put((frame, direction, callback))
          │                       │      │          └ None
          │                       │      └ <FrameDirection.DOWNSTREAM: 1>
          │                       └ TextFrame(id=45, name='TextFrame#1', pts=None, metadata={}, transport_source=None, transport_destination=None, text='抱歉，我现在无法...
          └ <__main__.EdgeTTSProcessor object at 0x77125aee4d40>

AttributeError: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue'. Did you mean: '_FrameProcessor__input_event'?
2025-07-31 15:50:33.465 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#0(error: 'EdgeTTSProcessor' object has no attribute '_FrameProcessor__input_queue', fatal: False)
2025-07-31 15:50:35.054 | DEBUG    | pipecat.transports.base_input:_handle_user_interruption:348 - User started speaking
2025-07-31 15:50:35.501 | DEBUG    | pipecat.transports.services.daily:_on_transcription_message:2293 - Transcription (from: 5a473c88-7020-449c-9920-ea0ad32eec2b): [Neehaw.]
2025-07-31 15:50:37.015 | DEBUG    | pipecat.transports.base_input:_handle_user_interruption:372 - User stopped speaking
2025-07-31 15:51:07.895 | DEBUG    | pipecat.transports.base_input:_handle_user_interruption:348 - User started speaking
2025-07-31 15:51:08.260 | DEBUG    | pipecat.transports.services.daily:_on_transcription_message:2293 - Transcription (from: 5a473c88-7020-449c-9920-ea0ad32eec2b): [Yeah.]
2025-07-31 15:51:08.794 | DEBUG    | pipecat.transports.base_input:_handle_user_interruption:372 - User stopped speaking
Creating room
Room URL: https://heweijie.daily.co/pHW6f31GrkBUbpLga9Oo
INFO:     ***************:48476 - "GET / HTTP/1.1" 307 Temporary Redirect
2025-07-31 15:51:24.692 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:51:28.290 | DEBUG    | pipecat.audio.vad.silero:__init__:147 - Loading Silero VAD model...
2025-07-31 15:51:28.492 | DEBUG    | pipecat.audio.vad.silero:__init__:169 - Loaded Silero VAD
Invoking rtc_set_log_level, log enabled:1, level:4
Configuring the webrtc log level.
/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/transports/base_input.py:102: DeprecationWarning: Parameter 'vad_enabled' is deprecated, use 'audio_in_enabled' and 'vad_analyzer' instead.
  warnings.warn(
2025-07-31 15:51:28.629 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineSource#0 -> DailyInputTransport#0
2025-07-31 15:51:28.629 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyInputTransport#0 -> RTVIProcessor#0
2025-07-31 15:51:28.629 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking RTVIProcessor#0 -> OpenAIUserContextAggregator#0
2025-07-31 15:51:28.629 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIUserContextAggregator#0 -> GroqLLMService#0
2025-07-31 15:51:28.629 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking GroqLLMService#0 -> EdgeTTSProcessor#0
2025-07-31 15:51:28.630 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking EdgeTTSProcessor#0 -> TalkingAnimationProcessor#0
2025-07-31 15:51:28.630 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking TalkingAnimationProcessor#0 -> DailyOutputTransport#0
2025-07-31 15:51:28.630 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking DailyOutputTransport#0 -> OpenAIAssistantContextAggregator#0
2025-07-31 15:51:28.630 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking OpenAIAssistantContextAggregator#0 -> PipelineSink#0
2025-07-31 15:51:28.630 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking PipelineTaskSource#0 -> Pipeline#0
2025-07-31 15:51:28.630 | DEBUG    | pipecat.processors.frame_processor:link:394 - Linking Pipeline#0 -> PipelineTaskSink#0
2025-07-31 15:51:28.631 | DEBUG    | pipecat.pipeline.runner:run:66 - Runner PipelineRunner#0 started running PipelineTask#0
2025-07-31 15:51:28.633 | DEBUG    | pipecat.audio.vad.vad_analyzer:set_params:150 - Setting VAD params to: confidence=0.7 start_secs=0.2 stop_secs=0.8 min_volume=0.6
2025-07-31 15:51:28.634 | INFO     | pipecat.transports.services.daily:join:597 - Joining https://heweijie.daily.co/pHW6f31GrkBUbpLga9Oo
2025-07-31 15:51:30.632 | INFO     | pipecat.transports.services.daily:join:617 - Joined https://heweijie.daily.co/pHW6f31GrkBUbpLga9Oo
2025-07-31 15:51:30.633 | DEBUG    | pipecat.transports.services.daily:start_transcription:856 - Starting transcription: settings=language='en' model='nova-2-general' profanity_filter=True redact=False endpointing=True punctuate=True includeRawResponse=True extra={'interim_results': True}
2025-07-31 15:51:31.702 | DEBUG    | pipecat.transports.services.daily:on_transcription_started:1221 - Transcription started: {'language': 'en', 'model': 'nova-2-general', 'instanceId': 'a1f2f6b7-b1ac-4202-85e5-d446cb6c3d3f', 'startedBy': '111eda39-3074-4b9b-a0b2-4b3792eef065', 'transcriptId': 'c82c2ef9-47c5-48b1-966a-fd2100383ceb'}
2025-07-31 15:51:31.704 | DEBUG    | pipecat.transports.services.daily:start_audio_in_streaming:1396 - Start receiving audio
2025-07-31 15:51:55.615 | DEBUG    | pipecat.transports.base_input:_handle_user_interruption:348 - User started speaking
2025-07-31 15:51:55.948 | DEBUG    | pipecat.transports.services.daily:_on_transcription_message:2293 - Transcription (from: 5a473c88-7020-449c-9920-ea0ad32eec2b): [Wait.]
2025-07-31 15:51:56.494 | DEBUG    | pipecat.transports.base_input:_handle_user_interruption:372 - User stopped speaking
