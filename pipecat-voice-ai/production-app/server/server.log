2025-07-31 15:29:28.985 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
INFO:     Started server process [60093]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     <PERSON>vicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/KDIwyqTe7psIqoRoKXbI
INFO:     ***************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 15:29:45.551 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:29:48.962 | ERROR    | pipecat.services.cartesia.tts:<module>:42 - Exception: No module named 'cartesia'
2025-07-31 15:29:48.962 | ERROR    | pipecat.services.cartesia.tts:<module>:43 - In order to use Cartesia, you need to `pip install pipecat-ai[cartesia]`.
Traceback (most recent call last):
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/services/cartesia/tts.py", line 40, in <module>
    from cartesia import AsyncCartesia
ModuleNotFoundError: No module named 'cartesia'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-openai.py", line 44, in <module>
    from pipecat.services.cartesia.tts import CartesiaTTSService
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/services/cartesia/__init__.py", line 12, in <module>
    from .tts import *
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/services/cartesia/tts.py", line 44, in <module>
    raise Exception(f"Missing module: {e}")
Exception: Missing module: No module named 'cartesia'
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/Z0ioGhf7kXaqhx4SE94V
INFO:     ***************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 15:31:09.421 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:31:13.085 | ERROR    | pipecat.services.cartesia.tts:<module>:42 - Exception: No module named 'cartesia'
2025-07-31 15:31:13.086 | ERROR    | pipecat.services.cartesia.tts:<module>:43 - In order to use Cartesia, you need to `pip install pipecat-ai[cartesia]`.
Traceback (most recent call last):
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/services/cartesia/tts.py", line 40, in <module>
    from cartesia import AsyncCartesia
ModuleNotFoundError: No module named 'cartesia'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-openai.py", line 44, in <module>
    from pipecat.services.cartesia.tts import CartesiaTTSService
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/services/cartesia/__init__.py", line 12, in <module>
    from .tts import *
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/services/cartesia/tts.py", line 44, in <module>
    raise Exception(f"Missing module: {e}")
Exception: Missing module: No module named 'cartesia'
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/94Cx7Vn9ndQh2oJgRjN2
INFO:     *************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 15:33:10.524 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:33:13.612 | ERROR    | pipecat.services.cartesia.tts:<module>:42 - Exception: No module named 'cartesia'
2025-07-31 15:33:13.612 | ERROR    | pipecat.services.cartesia.tts:<module>:43 - In order to use Cartesia, you need to `pip install pipecat-ai[cartesia]`.
Traceback (most recent call last):
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/services/cartesia/tts.py", line 40, in <module>
    from cartesia import AsyncCartesia
ModuleNotFoundError: No module named 'cartesia'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-openai.py", line 44, in <module>
    from pipecat.services.cartesia.tts import CartesiaTTSService
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/services/cartesia/__init__.py", line 12, in <module>
    from .tts import *
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/services/cartesia/tts.py", line 44, in <module>
    raise Exception(f"Missing module: {e}")
Exception: Missing module: No module named 'cartesia'
Creating room for RTVI connection
Room URL: https://heweijie.daily.co/VEwM8PwshU8hysQOQ4IZ
INFO:     **************:0 - "POST /connect HTTP/1.1" 200 OK
2025-07-31 15:34:42.202 | INFO     | pipecat:<module>:14 - ᓚᘏᗢ Pipecat 0.0.76 (Python 3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]) ᓚᘏᗢ
2025-07-31 15:34:45.683 | ERROR    | pipecat.services.cartesia.tts:<module>:42 - Exception: No module named 'cartesia'
2025-07-31 15:34:45.683 | ERROR    | pipecat.services.cartesia.tts:<module>:43 - In order to use Cartesia, you need to `pip install pipecat-ai[cartesia]`.
Traceback (most recent call last):
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/services/cartesia/tts.py", line 40, in <module>
    from cartesia import AsyncCartesia
ModuleNotFoundError: No module named 'cartesia'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/bot-openai.py", line 44, in <module>
    from pipecat.services.cartesia.tts import CartesiaTTSService
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/services/cartesia/__init__.py", line 12, in <module>
    from .tts import *
  File "/www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/venv/lib/python3.12/site-packages/pipecat/services/cartesia/tts.py", line 44, in <module>
    raise Exception(f"Missing module: {e}")
Exception: Missing module: No module named 'cartesia'
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              2025-07-31 15:29:29.288 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:29:29.288 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:29:29.288 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:29:29.310 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:29:29.310 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:29:29.311 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:29:29.311 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#35(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:29:33.592 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:29:33.592 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:29:33.593 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:29:33.595 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:29:33.595 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:29:33.595 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:29:33.596 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#36(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:29:37.894 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:29:37.894 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:29:37.894 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:29:37.896 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:29:37.896 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:29:37.896 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:29:37.897 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#37(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:29:42.194 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:29:42.195 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:29:42.195 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:29:42.197 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:29:42.197 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:29:42.198 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:29:42.198 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#38(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:29:46.485 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:29:46.485 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:29:46.486 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:29:46.488 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:29:46.488 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:29:46.489 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:29:46.489 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#39(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:29:50.776 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:29:50.776 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:29:50.776 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:29:50.778 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:29:50.779 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:29:50.779 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:29:50.780 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#40(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:29:55.071 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:29:55.072 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:29:55.072 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:29:55.076 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:29:55.076 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:29:55.076 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:29:55.077 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#41(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:29:59.362 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:29:59.362 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:29:59.362 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:29:59.364 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:29:59.365 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:29:59.365 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:29:59.366 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#42(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:30:03.649 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:30:03.650 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:03.650 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:03.654 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:30:03.654 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:30:03.654 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:30:03.655 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#43(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:30:07.947 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:30:07.947 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:07.947 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:07.950 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:30:07.950 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:30:07.951 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:30:07.951 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#44(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:30:12.247 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:30:12.247 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:12.247 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:12.250 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:30:12.250 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:30:12.250 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:30:12.251 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#45(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:30:16.521 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:30:16.522 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:16.522 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:16.524 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:30:16.524 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:30:16.525 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:30:16.525 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#46(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:30:20.844 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:30:20.844 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:20.844 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:20.847 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:30:20.848 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:30:20.848 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:30:20.848 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#47(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:30:25.141 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:30:25.141 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:25.141 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:25.144 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:30:25.146 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:30:25.146 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:30:25.147 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#48(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:30:29.419 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:30:29.420 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:29.420 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:29.423 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:30:29.423 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:30:29.423 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:30:29.424 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#49(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:30:33.692 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:30:33.693 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:33.693 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:33.695 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:30:33.696 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:30:33.696 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:30:33.697 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#50(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:30:37.982 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:30:37.983 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:37.983 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:37.986 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:30:37.987 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:30:37.987 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:30:37.988 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#51(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:30:42.280 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:30:42.281 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:42.282 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:42.286 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:30:42.286 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:30:42.286 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:30:42.289 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#52(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:30:46.707 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:30:46.708 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:46.708 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:46.711 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:30:46.711 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:30:46.711 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:30:46.712 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#53(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:30:51.005 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:30:51.005 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:51.005 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:51.007 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:30:51.008 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:30:51.008 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:30:51.009 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#54(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:30:55.283 | WARNING  | pipecat.services.elevenlabs.tts:_receive_messages:523 - Ignoring message from unavailable context: None
2025-07-31 15:30:55.284 | ERROR    | pipecat.services.websocket_service:_receive_task_handler:93 - ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:55.285 | WARNING  | pipecat.services.websocket_service:_receive_task_handler:101 - ElevenLabsTTSService#0 connection error, will retry: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key
2025-07-31 15:30:55.288 | WARNING  | pipecat.services.websocket_service:_reconnect_websocket:63 - ElevenLabsTTSService#0 reconnecting (attempt: 1)
2025-07-31 15:30:55.288 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:30:55.289 | DEBUG    | pipecat.services.elevenlabs.tts:_connect_websocket:427 - Connecting to ElevenLabs
2025-07-31 15:30:55.290 | WARNING  | pipecat.pipeline.task:_process_up_queue:694 - Something went wrong: ErrorFrame#55(error: ElevenLabsTTSService#0 error receiving messages: received 1008 (policy violation) Invalid API key; then sent 1008 (policy violation) Invalid API key, fatal: False)
2025-07-31 15:30:56.184 | WARNING  | pipecat.pipeline.runner:_sig_cancel:108 - Interruption detected. Cancelling runner PipelineRunner#0
2025-07-31 15:30:56.185 | DEBUG    | pipecat.pipeline.runner:cancel:92 - Cancelling runner PipelineRunner#0
2025-07-31 15:30:56.185 | DEBUG    | pipecat.pipeline.task:_cancel:497 - Canceling pipeline task PipelineTask#0
2025-07-31 15:30:56.188 | DEBUG    | pipecat.services.elevenlabs.tts:_disconnect_websocket:465 - Disconnecting from ElevenLabs
2025-07-31 15:30:57.382 | INFO     | pipecat.transports.services.daily:leave:703 - Leaving https://heweijie.daily.co/2HZp8jdx3U90lOYlRRJY
2025-07-31 15:30:57.383 | DEBUG    | pipecat.transports.services.daily:stop_transcription:872 - Stopping transcription
2025-07-31 15:30:57.445 | DEBUG    | pipecat.transports.services.daily:on_transcription_stopped:1232 - Transcription stopped
2025-07-31 15:30:57.474 | INFO     | pipecat.transports.services.daily:leave:715 - Left https://heweijie.daily.co/2HZp8jdx3U90lOYlRRJY
2025-07-31 15:30:57.478 | DEBUG    | pipecat.pipeline.runner:run:83 - Runner PipelineRunner#0 finished running PipelineTask#0
{"timestamp":"2025-07-31T15:30:57.534826Z","level":"ERROR","fields":{"message":"Failed to post message: TrySendError { kind: Disconnected }"},"target":"daily_core::call_manager"}
