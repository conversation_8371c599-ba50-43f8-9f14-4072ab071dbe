#!/bin/bash

# Pipecat Voice AI 服务器停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 检查PID文件
PID_FILE="$SCRIPT_DIR/server.pid"
if [ ! -f "$PID_FILE" ]; then
    log_warning "PID文件不存在: $PID_FILE"
    log_info "尝试通过端口查找进程..."
    
    # 通过端口查找进程
    PORT=8080
    PID=$(lsof -ti:$PORT 2>/dev/null || true)
    if [ -n "$PID" ]; then
        log_info "找到运行在端口 $PORT 的进程: $PID"
        echo $PID > "$PID_FILE"
    else
        log_error "未找到运行的服务器进程"
        exit 1
    fi
fi

# 读取PID
SERVER_PID=$(cat "$PID_FILE")

log_info "尝试停止服务器进程 (PID: $SERVER_PID)..."

# 检查进程是否存在
if ! kill -0 $SERVER_PID 2>/dev/null; then
    log_warning "进程 $SERVER_PID 不存在或已停止"
    rm -f "$PID_FILE"
    exit 0
fi

# 优雅停止
log_info "发送TERM信号..."
kill -TERM $SERVER_PID 2>/dev/null || true

# 等待进程停止
for i in {1..10}; do
    if ! kill -0 $SERVER_PID 2>/dev/null; then
        log_success "服务器已优雅停止"
        rm -f "$PID_FILE"
        exit 0
    fi
    log_info "等待进程停止... ($i/10)"
    sleep 1
done

# 强制停止
log_warning "优雅停止失败，强制终止进程..."
kill -KILL $SERVER_PID 2>/dev/null || true

# 再次检查
sleep 1
if ! kill -0 $SERVER_PID 2>/dev/null; then
    log_success "服务器已强制停止"
    rm -f "$PID_FILE"
else
    log_error "无法停止服务器进程"
    exit 1
fi
