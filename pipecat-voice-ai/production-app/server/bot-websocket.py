#!/usr/bin/env python3

"""
WebSocket版本的Groq AI Chatbot

使用WebSocket传输，避免Daily.co的复杂性
"""

import argparse
import asyncio
import os
import sys

from dotenv import load_dotenv
from loguru import logger

from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineParams, PipelineTask
from pipecat.processors.aggregators.openai_llm_context import (
    OpenAILLMContext,
    OpenAILLMContextAggregator,
)
from pipecat.services.openai.llm import OpenAILLMService
from pipecat.transports.services.websocket import WebsocketServerTransport

load_dotenv(override=True)
logger.remove(0)
logger.add(sys.stderr, level="DEBUG")


async def run_bot(host: str = "0.0.0.0", port: int = 8765):
    """运行WebSocket版本的Groq AI机器人"""
    
    # 配置WebSocket传输
    transport = WebsocketServerTransport(
        host=host,
        port=port,
        params={}
    )

    # 配置Groq LLM (使用OpenAI兼容接口)
    groq_api_key = os.getenv("GROQ_API_KEY")
    if not groq_api_key:
        raise ValueError("GROQ_API_KEY environment variable is required")

    # LLM (使用OpenAI兼容的Groq API)
    llm = OpenAILLMService(
        api_key=groq_api_key,
        base_url="https://api.groq.com/openai/v1",
        model="llama-3.3-70b-versatile"
    )

    # 设置对话上下文
    messages = [
        {
            "role": "system",
            "content": """你是一个友好的AI助手，请用中文回答问题。

特点：
- 回答要简洁明了，适合文字对话
- 语言要自然流畅
- 每次回答控制在100字以内，除非用户要求详细解释

请用温暖友好的语气与用户对话。"""
        }
    ]

    context = OpenAILLMContext(messages)
    context_aggregator = llm.create_context_aggregator(context)

    # 构建处理管道 (简化版，只有文字对话)
    pipeline = Pipeline([
        transport.input(),           # WebSocket输入
        context_aggregator.user(),  # 用户上下文
        llm,                        # 大语言模型
        transport.output(),         # WebSocket输出
        context_aggregator.assistant(),  # 助手上下文
    ])

    # 创建任务
    task = PipelineTask(
        pipeline,
        params=PipelineParams(
            enable_metrics=True,
            enable_usage_metrics=True,
        ),
    )

    # 事件处理
    @transport.event_handler("on_client_connected")
    async def on_client_connected(transport, client):
        logger.info(f"WebSocket客户端已连接: {client}")
        # 发送欢迎消息
        await task.queue_frames([context_aggregator.user().get_context_frame()])

    @transport.event_handler("on_client_disconnected")
    async def on_client_disconnected(transport, client, reason):
        logger.info(f"WebSocket客户端已断开: {client}, 原因: {reason}")

    # 运行任务
    runner = PipelineRunner()
    await runner.run(task)


def main():
    parser = argparse.ArgumentParser(description="WebSocket Groq AI Chatbot")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="WebSocket host")
    parser.add_argument("--port", type=int, default=8765, help="WebSocket port")
    
    args = parser.parse_args()
    
    try:
        asyncio.run(run_bot(args.host, args.port))
    except KeyboardInterrupt:
        logger.info("机器人已停止")
    except Exception as e:
        logger.error(f"运行错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
