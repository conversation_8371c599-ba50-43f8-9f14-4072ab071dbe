#!/usr/bin/env python3

"""
Groq AI Chatbot Implementation for Pipecat

This module implements a chatbot using Groq's official Pipecat integration.
It includes:
- Real-time audio/video interaction through Daily
- Animated robot avatar
- Edge TTS for free Chinese text-to-speech
- Support for Chinese language

The bot runs as part of a pipeline that processes audio/video frames and manages
the conversation flow.
"""

import argparse
import asyncio
import os
import sys

from dotenv import load_dotenv
from loguru import logger
from PIL import Image

from pipecat.audio.vad.silero import SileroVADAnalyzer
from pipecat.frames.frames import (
    BotStartedSpeakingFrame,
    BotStoppedSpeakingFrame,
    Frame,
    ImageRawFrame,
    TextFrame,
    TTSAudioRawFrame,
    TTSStartedFrame,
    TTSStoppedFrame,
)
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineParams, PipelineTask
from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext
from pipecat.processors.frame_processor import FrameDirection, FrameProcessor
from pipecat.processors.frameworks.rtvi import RTVIConfig, RTVIObserver, RTVIProcessor
from pipecat.services.groq.llm import GroqLLMService
from pipecat.services.openai.llm import OpenAILLMService
from pipecat.transports.services.daily import DailyParams, DailyTransport

load_dotenv(override=True)
logger.remove(0)
logger.add(sys.stderr, level="DEBUG")

# Edge TTS处理器 - 完全免费的TTS
class EdgeTTSProcessor(FrameProcessor):
    def __init__(self):
        super().__init__()

    async def process_frame(self, frame: Frame, direction: FrameDirection):
        await super().process_frame(frame, direction)

        if isinstance(frame, TextFrame):
            try:
                import edge_tts
                import io
                import wave
                from pipecat.frames.frames import TTSAudioRawFrame, TTSStartedFrame, TTSStoppedFrame

                # 使用中文语音
                voice = "zh-CN-XiaoxiaoNeural"  # 中文女声

                await self.push_frame(TTSStartedFrame(), direction)

                # 生成语音
                communicate = edge_tts.Communicate(frame.text, voice)
                audio_data = b""

                async for chunk in communicate.stream():
                    if chunk["type"] == "audio":
                        audio_data += chunk["data"]

                if audio_data:
                    # 转换音频格式
                    with wave.open(io.BytesIO(audio_data)) as w:
                        channels = w.getnchannels()
                        frame_rate = w.getframerate()
                        num_frames = w.getnframes()
                        audio_bytes = w.readframes(num_frames)

                        await self.push_frame(
                            TTSAudioRawFrame(audio_bytes, frame_rate, channels),
                            direction
                        )

                await self.push_frame(TTSStoppedFrame(), direction)

            except Exception as e:
                logger.error(f"Edge TTS error: {e}")
                # 如果TTS失败，至少传递文本
                await self.push_frame(frame, direction)

# Groq LLM服务
class GroqLLMService(FrameProcessor):
    def __init__(self, api_key: str):
        super().__init__()
        self.api_key = api_key
        self.base_url = "https://api.groq.com/openai/v1"
        
    async def process_frame(self, frame: Frame, direction: FrameDirection):
        await super().process_frame(frame, direction)
        
        if isinstance(frame, TextFrame):
            try:
                # 调用Groq API
                async with aiohttp.ClientSession() as session:
                    headers = {
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json"
                    }
                    
                    data = {
                        "model": "llama3-8b-8192",
                        "messages": [
                            {
                                "role": "system", 
                                "content": "你是一个友好的AI助手，请用中文回答问题。回答要简洁明了。"
                            },
                            {
                                "role": "user", 
                                "content": frame.text
                            }
                        ],
                        "max_tokens": 150,
                        "temperature": 0.7
                    }
                    
                    async with session.post(
                        f"{self.base_url}/chat/completions",
                        headers=headers,
                        json=data
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            reply = result["choices"][0]["message"]["content"]
                            await self.push_frame(TextFrame(reply), direction)
                        else:
                            error_text = await response.text()
                            logger.error(f"Groq API error: {response.status} - {error_text}")
                            await self.push_frame(
                                TextFrame("抱歉，我现在无法回答您的问题。"), 
                                direction
                            )
                            
            except Exception as e:
                logger.error(f"Error calling Groq API: {e}")
                await self.push_frame(
                    TextFrame("抱歉，出现了技术问题。"), 
                    direction
                )

class TalkingAnimation:
    def __init__(self):
        self.is_talking = False
        self.current_frame = 0
        self.frames = []
        
        # 加载动画帧
        for i in range(1, 26):  # robot01.png to robot025.png
            try:
                image_path = f"assets/robot{i:02d}.png"
                if os.path.exists(image_path):
                    image = Image.open(image_path)
                    self.frames.append(image)
            except Exception as e:
                logger.warning(f"Could not load {image_path}: {e}")
        
        if not self.frames:
            # 创建一个默认图像
            default_image = Image.new('RGB', (512, 512), color='gray')
            self.frames.append(default_image)
    
    def get_current_frame(self):
        if self.is_talking:
            frame = self.frames[self.current_frame % len(self.frames)]
            self.current_frame += 1
        else:
            frame = self.frames[0]  # 静态帧
        
        return ImageRawFrame(
            image=frame.tobytes(),
            size=frame.size,
            format=frame.mode
        )

class TalkingAnimationProcessor(FrameProcessor):
    def __init__(self):
        super().__init__()
        self.animation = TalkingAnimation()
        
    async def process_frame(self, frame: Frame, direction: FrameDirection):
        await super().process_frame(frame, direction)
        
        if isinstance(frame, BotStartedSpeakingFrame):
            self.animation.is_talking = True
        elif isinstance(frame, BotStoppedSpeakingFrame):
            self.animation.is_talking = False
            
        # 定期发送动画帧
        await self.push_frame(self.animation.get_current_frame(), direction)

async def main():
    parser = argparse.ArgumentParser(description="Groq AI Chatbot")
    parser.add_argument("-u", "--url", type=str, required=True, help="Daily room URL")
    parser.add_argument("-t", "--token", type=str, required=True, help="Daily room token")

    args = parser.parse_args()

    # 直接使用参数，不使用configure函数
    room_url = args.url
    token = args.token
    
    # 初始化Daily传输
    transport = DailyTransport(
        room_url=room_url,
        token=token,
        bot_name="Groq AI Assistant",
        params=DailyParams(
            audio_out_enabled=True,
            audio_in_enabled=True,
            video_out_enabled=True,
            vad_enabled=True,
            vad_analyzer=SileroVADAnalyzer(),
            transcription_enabled=True,
        )
    )
    
    # 初始化服务
    groq_api_key = os.getenv("GROQ_API_KEY")
    if not groq_api_key:
        raise ValueError("GROQ_API_KEY environment variable is required")
    
    llm = GroqLLMService(api_key=groq_api_key)
    tts = EdgeTTSProcessor()  # 使用免费的Edge TTS
    ta = TalkingAnimationProcessor()

    # 创建OpenAI兼容的LLM服务用于上下文聚合
    from pipecat.services.openai.llm import OpenAILLMService
    openai_llm = OpenAILLMService(api_key="dummy")  # 只用于创建context aggregator

    # 上下文聚合器
    messages = [
        {
            "role": "system",
            "content": "你是一个友好的AI助手，请用中文回答问题。回答要简洁明了。"
        }
    ]
    context = OpenAILLMContext(messages)
    context_aggregator = openai_llm.create_context_aggregator(context)

    # RTVI处理器
    rtvi_config = RTVIConfig(config=[])
    rtvi = RTVIProcessor(config=rtvi_config)
    
    # 创建管道
    pipeline = Pipeline([
        transport.input(),
        rtvi,
        context_aggregator.user(),
        llm,
        tts,
        ta,
        transport.output(),
        context_aggregator.assistant(),
    ])
    
    # 创建任务
    task = PipelineTask(
        pipeline,
        params=PipelineParams(
            allow_interruptions=True,
            enable_metrics=True,
            enable_usage_metrics=True,
        )
    )
    
    # 事件处理器
    @rtvi.event_handler("on_client_ready")
    async def on_client_ready(rtvi):
        await rtvi.set_bot_ready()
        # 发送欢迎消息
        welcome_frame = TextFrame("你好！我是Groq AI助手，很高兴为您服务！")
        await task.queue_frame(welcome_frame)
    
    @transport.event_handler("on_first_participant_joined")
    async def on_first_participant_joined(transport, participant):
        logger.info(f"Participant joined: {participant}")
        await transport.capture_participant_transcription(participant["id"])
    
    @transport.event_handler("on_participant_left")
    async def on_participant_left(transport, participant, reason):
        logger.info(f"Participant left: {participant}")
        await task.cancel()
    
    # 运行任务
    runner = PipelineRunner()
    await runner.run(task)

if __name__ == "__main__":
    asyncio.run(main())
