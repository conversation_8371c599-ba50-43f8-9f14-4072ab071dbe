#!/bin/bash

# Pipecat Voice AI 服务器启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"

log_info "项目根目录: $PROJECT_ROOT"
log_info "服务器目录: $SCRIPT_DIR"

# 检查虚拟环境
VENV_PATH="$PROJECT_ROOT/venv"
if [ ! -d "$VENV_PATH" ]; then
    log_error "虚拟环境不存在: $VENV_PATH"
    exit 1
fi

# 激活虚拟环境
log_info "激活虚拟环境..."
source "$VENV_PATH/bin/activate"

# 检查环境变量文件
ENV_FILE="$SCRIPT_DIR/.env"
if [ ! -f "$ENV_FILE" ]; then
    log_warning ".env 文件不存在，复制示例文件..."
    cp "$SCRIPT_DIR/env.example" "$ENV_FILE"
    log_warning "请编辑 $ENV_FILE 文件配置API密钥"
fi

# 检查依赖
log_info "检查Python依赖..."
cd "$SCRIPT_DIR"

# 检查端口是否被占用
PORT=8080
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    log_warning "端口 $PORT 已被占用，尝试终止现有进程..."
    sudo lsof -ti:$PORT | xargs sudo kill -9 2>/dev/null || true
    sleep 2
fi

# 启动服务器
log_info "启动Pipecat Voice AI服务器 (端口: $PORT)..."
log_info "访问地址: https://su.guiyunai.fun"
log_info "API地址: https://su.guiyunai.fun/api/"

# 使用nohup在后台运行
nohup python3 server.py --host 0.0.0.0 --port $PORT > server.log 2>&1 &
SERVER_PID=$!

# 保存PID
echo $SERVER_PID > server.pid

log_success "服务器已启动 (PID: $SERVER_PID)"
log_info "日志文件: $SCRIPT_DIR/server.log"
log_info "PID文件: $SCRIPT_DIR/server.pid"

# 等待服务器启动
log_info "等待服务器启动..."
sleep 3

# 检查服务器是否正在运行
if kill -0 $SERVER_PID 2>/dev/null; then
    log_success "服务器运行正常!"
    log_info "使用以下命令查看日志: tail -f $SCRIPT_DIR/server.log"
    log_info "使用以下命令停止服务器: kill $SERVER_PID"
else
    log_error "服务器启动失败，请检查日志: $SCRIPT_DIR/server.log"
    exit 1
fi
