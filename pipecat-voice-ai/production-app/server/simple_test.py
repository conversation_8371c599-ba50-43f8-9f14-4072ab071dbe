#!/usr/bin/env python3
"""
简单的测试服务器，不依赖Daily.co
用于验证基本的AI对话功能
"""

import os
import asyncio
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

app = FastAPI(title="Pipecat Voice AI Test Server")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Pipecat Voice AI Test Server", "status": "running"}

@app.get("/health")
async def health_check():
    return JSONResponse({
        "status": "healthy",
        "service": "Pipecat Voice AI Test",
        "version": "1.0.0",
        "daily_configured": bool(os.getenv("DAILY_API_KEY")),
        "groq_configured": bool(os.getenv("GROQ_API_KEY"))
    })

@app.post("/connect")
async def test_connect():
    """测试连接端点"""
    daily_key = os.getenv("DAILY_API_KEY", "")
    
    if not daily_key:
        raise HTTPException(
            status_code=500, 
            detail="Daily API密钥未配置"
        )
    
    # 模拟成功响应
    return JSONResponse({
        "status": "success",
        "message": "Daily API密钥已配置",
        "daily_key_length": len(daily_key),
        "note": "实际连接需要Daily账户添加付款方式"
    })

@app.post("/test-groq")
async def test_groq():
    """测试Groq AI连接"""
    try:
        from groq import Groq
        
        api_key = os.getenv("GROQ_API_KEY")
        if not api_key:
            raise HTTPException(status_code=500, detail="Groq API密钥未配置")
        
        client = Groq(api_key=api_key)
        
        # 测试API调用
        response = client.chat.completions.create(
            model="llama3-8b-8192",
            messages=[
                {"role": "user", "content": "你好，请用中文简单介绍一下你自己"}
            ],
            max_tokens=100,
            temperature=0.7
        )
        
        content = response.choices[0].message.content
        
        return JSONResponse({
            "status": "success",
            "message": "Groq API连接成功",
            "response": content,
            "model": "llama3-8b-8192"
        })
        
    except ImportError:
        raise HTTPException(status_code=500, detail="Groq库未安装")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Groq API调用失败: {str(e)}")

@app.get("/config-status")
async def config_status():
    """检查配置状态"""
    return JSONResponse({
        "daily_api_key": "已配置" if os.getenv("DAILY_API_KEY") else "未配置",
        "groq_api_key": "已配置" if os.getenv("GROQ_API_KEY") else "未配置",
        "openai_api_key": "已配置" if os.getenv("OPENAI_API_KEY") else "未配置",
        "elevenlabs_api_key": "已配置" if os.getenv("ELEVENLABS_API_KEY") else "未配置",
        "bot_implementation": os.getenv("BOT_IMPLEMENTATION", "未设置")
    })

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8081)
