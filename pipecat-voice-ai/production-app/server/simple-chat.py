#!/usr/bin/env python3

"""
超简单的WebSocket聊天服务器
直接调用Groq API，不依赖Pipecat
"""

import asyncio
import json
import os
import websockets
import aiohttp
from dotenv import load_dotenv

load_dotenv()

class GroqChatBot:
    def __init__(self):
        self.api_key = os.getenv("GROQ_API_KEY")
        if not self.api_key:
            raise ValueError("GROQ_API_KEY environment variable is required")
        
        self.base_url = "https://api.groq.com/openai/v1"
        self.conversation_history = [
            {
                "role": "system",
                "content": "你是一个友好的AI助手，请用中文回答问题。回答要简洁明了，每次回答控制在100字以内。"
            }
        ]
    
    async def get_response(self, user_message):
        """调用Groq API获取回复"""
        # 添加用户消息到历史
        self.conversation_history.append({
            "role": "user",
            "content": user_message
        })
        
        # 保持历史记录不超过10条
        if len(self.conversation_history) > 10:
            # 保留系统消息和最近的8条对话
            self.conversation_history = [self.conversation_history[0]] + self.conversation_history[-8:]
        
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
                
                data = {
                    "model": "llama-3.3-70b-versatile",
                    "messages": self.conversation_history,
                    "max_tokens": 200,
                    "temperature": 0.7
                }
                
                async with session.post(f"{self.base_url}/chat/completions", 
                                      headers=headers, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        reply = result["choices"][0]["message"]["content"]
                        
                        # 添加助手回复到历史
                        self.conversation_history.append({
                            "role": "assistant",
                            "content": reply
                        })
                        
                        return reply
                    else:
                        error_text = await response.text()
                        print(f"Groq API error: {response.status} - {error_text}")
                        return "抱歉，我现在无法回答。请稍后再试。"
                        
        except Exception as e:
            print(f"Error calling Groq API: {e}")
            return "抱歉，出现了网络错误。"

# 全局bot实例
bot = GroqChatBot()

async def handle_client(websocket, path):
    """处理WebSocket客户端连接"""
    print(f"新客户端连接: {websocket.remote_address}")
    
    try:
        # 发送欢迎消息
        welcome_msg = {
            "type": "text",
            "content": "你好！我是AI助手，有什么可以帮助你的吗？"
        }
        await websocket.send(json.dumps(welcome_msg, ensure_ascii=False))
        
        async for message in websocket:
            try:
                # 解析消息
                data = json.loads(message)
                
                if data.get("type") == "text" and data.get("content"):
                    user_message = data["content"].strip()
                    print(f"收到消息: {user_message}")
                    
                    # 获取AI回复
                    ai_response = await bot.get_response(user_message)
                    print(f"AI回复: {ai_response}")
                    
                    # 发送回复
                    response_msg = {
                        "type": "text",
                        "content": ai_response
                    }
                    await websocket.send(json.dumps(response_msg, ensure_ascii=False))
                    
            except json.JSONDecodeError:
                # 如果不是JSON，当作纯文本处理
                user_message = message.strip()
                if user_message:
                    print(f"收到文本消息: {user_message}")
                    ai_response = await bot.get_response(user_message)
                    await websocket.send(ai_response)
                    
            except Exception as e:
                print(f"处理消息时出错: {e}")
                error_msg = {
                    "type": "text",
                    "content": "抱歉，处理你的消息时出现了错误。"
                }
                await websocket.send(json.dumps(error_msg, ensure_ascii=False))
                
    except websockets.exceptions.ConnectionClosed:
        print(f"客户端断开连接: {websocket.remote_address}")
    except Exception as e:
        print(f"连接错误: {e}")

async def main():
    """启动WebSocket服务器"""
    host = "0.0.0.0"
    port = 8765
    
    print(f"启动WebSocket聊天服务器...")
    print(f"地址: ws://{host}:{port}")
    print("按 Ctrl+C 停止服务器")
    
    try:
        async with websockets.serve(handle_client, host, port):
            await asyncio.Future()  # 永远运行
    except KeyboardInterrupt:
        print("\n服务器已停止")

if __name__ == "__main__":
    asyncio.run(main())
