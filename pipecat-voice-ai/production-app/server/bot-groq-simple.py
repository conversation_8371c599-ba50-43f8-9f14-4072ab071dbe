#!/usr/bin/env python3

"""
简化的Groq AI Chatbot - 使用官方Pipecat Groq集成

这个版本使用Pipecat官方的Groq服务，应该更稳定可靠。
"""

import argparse
import asyncio
import os
import sys

from dotenv import load_dotenv
from loguru import logger

from pipecat.audio.vad.silero import SileroVADAnalyzer
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineParams, PipelineTask
from pipecat.processors.aggregators.openai_llm_context import (
    OpenAILLMContext,
    OpenAILLMContextAggregator,
)
from pipecat.services.groq.llm import GroqLLMService
from pipecat.services.groq.stt import GroqSTTService
from pipecat.services.groq.tts import GroqTTSService
from pipecat.transports.services.daily import DailyParams, DailyTransport

load_dotenv(override=True)
logger.remove(0)
logger.add(sys.stderr, level="DEBUG")


async def run_bot(room_url: str, token: str):
    """运行Groq AI机器人"""
    
    # 配置Daily传输
    transport = DailyTransport(
        room_url,
        token,
        "Groq AI Bot",
        DailyParams(
            audio_out_enabled=True,
            audio_in_enabled=True,
            vad_enabled=True,
            vad_analyzer=SileroVADAnalyzer(),
            transcription_enabled=True,
        ),
    )

    # 配置Groq服务
    groq_api_key = os.getenv("GROQ_API_KEY")
    if not groq_api_key:
        raise ValueError("GROQ_API_KEY environment variable is required")

    # STT (语音转文字)
    stt = GroqSTTService(
        api_key=groq_api_key,
        model="whisper-large-v3-turbo",
        language="zh"  # 中文
    )

    # LLM (大语言模型)
    llm = GroqLLMService(
        api_key=groq_api_key,
        model="llama-3.3-70b-versatile"
    )

    # TTS (文字转语音)
    tts = GroqTTSService(
        api_key=groq_api_key,
        voice_id="alloy"  # 使用alloy语音
    )

    # 设置对话上下文
    messages = [
        {
            "role": "system",
            "content": """你是一个友好的AI助手，请用中文回答问题。
            
特点：
- 回答要简洁明了，适合语音对话
- 语言要自然流畅
- 避免使用特殊符号和格式化文本
- 每次回答控制在50字以内，除非用户要求详细解释

请用温暖友好的语气与用户对话。"""
        }
    ]

    context = OpenAILLMContext(messages)
    context_aggregator = llm.create_context_aggregator(context)

    # 构建处理管道
    pipeline = Pipeline([
        transport.input(),           # 音频输入
        stt,                        # 语音转文字
        context_aggregator.user(),  # 用户上下文
        llm,                        # 大语言模型
        tts,                        # 文字转语音
        transport.output(),         # 音频输出
        context_aggregator.assistant(),  # 助手上下文
    ])

    # 创建任务
    task = PipelineTask(
        pipeline,
        params=PipelineParams(
            enable_metrics=True,
            enable_usage_metrics=True,
        ),
    )

    # 事件处理
    @transport.event_handler("on_client_connected")
    async def on_client_connected(transport, client):
        logger.info(f"客户端已连接: {client}")
        # 发送欢迎消息
        await task.queue_frames([context_aggregator.user().get_context_frame()])

    @transport.event_handler("on_client_disconnected")
    async def on_client_disconnected(transport, client, reason):
        logger.info(f"客户端已断开: {client}, 原因: {reason}")

    # 运行任务
    runner = PipelineRunner()
    await runner.run(task)


def main():
    parser = argparse.ArgumentParser(description="Groq AI Chatbot")
    parser.add_argument("-u", "--url", type=str, required=True, help="Daily room URL")
    parser.add_argument("-t", "--token", type=str, required=True, help="Daily room token")
    
    args = parser.parse_args()
    
    try:
        asyncio.run(run_bot(args.url, args.token))
    except KeyboardInterrupt:
        logger.info("机器人已停止")
    except Exception as e:
        logger.error(f"运行错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
