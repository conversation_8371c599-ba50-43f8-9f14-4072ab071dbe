#!/usr/bin/env python3
"""
简单的Groq API测试脚本
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_groq_api():
    """测试Groq API连接"""
    try:
        from groq import Groq
        
        api_key = os.getenv("GROQ_API_KEY")
        if not api_key:
            print("❌ GROQ_API_KEY 未设置")
            return False
            
        print(f"🔑 使用API密钥: {api_key[:10]}...")
        
        client = Groq(api_key=api_key)
        
        # 尝试不同的模型名称
        models_to_try = [
            "llama3-8b-8192",
            "llama3-70b-8192", 
            "mixtral-8x7b-32768",
            "gemma-7b-it"
        ]
        
        for model in models_to_try:
            try:
                print(f"🧪 测试模型: {model}")
                response = client.chat.completions.create(
                    model=model,
                    messages=[
                        {"role": "user", "content": "你好，请用中文简单介绍一下你自己"}
                    ],
                    max_tokens=100,
                    temperature=0.7
                )
                
                content = response.choices[0].message.content
                print(f"✅ {model} 测试成功!")
                print(f"📝 回复: {content}")
                return True
                
            except Exception as e:
                print(f"❌ {model} 测试失败: {e}")
                continue
                
        print("❌ 所有模型测试都失败了")
        return False
        
    except ImportError:
        print("❌ 无法导入groq库，请确保已安装: pip install groq")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试AI服务...")
    print("=" * 50)
    
    groq_success = test_groq_api()
    print("\n" + "=" * 50)
    
    if groq_success:
        print("✅ Groq API 可用!")
    else:
        print("❌ Groq API 不可用，请检查API密钥配置")
