import { type PropsWithChildren } from 'react';
import { PipecatClient } from '@pipecat-ai/client-js';
import { DailyTransport } from '@pipecat-ai/daily-transport';
import { PipecatClientProvider } from '@pipecat-ai/client-react';

const client = new PipecatClient({
  transport: new DailyTransport(),
  enableMic: true,
  enableCam: false,
});

// 添加事件监听器用于调试
client.on('transportStateChanged', (state) => {
  console.log('Transport state changed:', state);
});

client.on('connected', () => {
  console.log('Client connected successfully');
});

client.on('disconnected', () => {
  console.log('Client disconnected');
});

client.on('error', (error) => {
  console.error('Client error:', error);
});

export function PipecatProvider({ children }: PropsWithChildren) {
  return (
    <PipecatClientProvider client={client}>{children}</PipecatClientProvider>
  );
}
