{"version": 3, "file": "sessiontiming.js", "sources": ["../../../src/integrations/sessiontiming.ts"], "sourcesContent": ["import { defineIntegration } from '../integration';\nimport type { IntegrationFn } from '../types-hoist';\nimport { timestampInSeconds } from '../utils-hoist/time';\n\nconst INTEGRATION_NAME = 'SessionTiming';\n\nconst _sessionTimingIntegration = (() => {\n  const startTime = timestampInSeconds() * 1000;\n\n  return {\n    name: INTEGRATION_NAME,\n    processEvent(event) {\n      const now = timestampInSeconds() * 1000;\n\n      return {\n        ...event,\n        extra: {\n          ...event.extra,\n          ['session:start']: startTime,\n          ['session:duration']: now - startTime,\n          ['session:end']: now,\n        },\n      };\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * This function adds duration since the sessionTimingIntegration was initialized\n * till the time event was sent.\n *\n * @deprecated This integration is deprecated and will be removed in the next major version of the SDK.\n * To capture session durations alongside events, use [Context](https://docs.sentry.io/platforms/javascript/enriching-events/context/) (`Sentry.setContext()`).\n */\nexport const sessionTimingIntegration = defineIntegration(_sessionTimingIntegration);\n"], "names": [], "mappings": ";;;AAIA,MAAM,gBAAA,GAAmB,eAAe;;AAExC,MAAM,yBAA0B,IAAG,MAAM;AACzC,EAAE,MAAM,SAAU,GAAE,kBAAkB,EAAC,GAAI,IAAI;;AAE/C,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,YAAY,CAAC,KAAK,EAAE;AACxB,MAAM,MAAM,GAAI,GAAE,kBAAkB,EAAC,GAAI,IAAI;;AAE7C,MAAM,OAAO;AACb,QAAQ,GAAG,KAAK;AAChB,QAAQ,KAAK,EAAE;AACf,UAAU,GAAG,KAAK,CAAC,KAAK;AACxB,UAAU,CAAC,eAAe,GAAG,SAAS;AACtC,UAAU,CAAC,kBAAkB,GAAG,GAAA,GAAM,SAAS;AAC/C,UAAU,CAAC,aAAa,GAAG,GAAG;AAC9B,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH,CAAC,CAAE;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;MACa,wBAAyB,GAAE,iBAAiB,CAAC,yBAAyB;;;;"}