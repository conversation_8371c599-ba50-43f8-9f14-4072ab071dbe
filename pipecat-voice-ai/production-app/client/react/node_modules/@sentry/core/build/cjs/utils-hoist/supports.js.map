{"version": 3, "file": "supports.js", "sources": ["../../../src/utils-hoist/supports.ts"], "sourcesContent": ["import { DEBUG_BUILD } from './debug-build';\nimport { logger } from './logger';\nimport { GLOBAL_OBJ } from './worldwide';\n\nconst WINDOW = GLOBAL_OBJ as unknown as Window;\n\ndeclare const EdgeRuntime: string | undefined;\n\nexport { supportsHistory } from './vendor/supportsHistory';\n\n/**\n * Tells whether current environment supports ErrorEvent objects\n * {@link supportsErrorEvent}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsErrorEvent(): boolean {\n  try {\n    new ErrorEvent('');\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Tells whether current environment supports DOMError objects\n * {@link supportsDOMError}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsDOMError(): boolean {\n  try {\n    // Chrome: VM89:1 Uncaught TypeError: Failed to construct 'DOMError':\n    // 1 argument required, but only 0 present.\n    // @ts-expect-error It really needs 1 argument, not 0.\n    new DOMError('');\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Tells whether current environment supports DOMException objects\n * {@link supportsDOMException}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsDOMException(): boolean {\n  try {\n    new DOMException('');\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Tells whether current environment supports Fetch API\n * {@link supportsFetch}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsFetch(): boolean {\n  if (!('fetch' in WINDOW)) {\n    return false;\n  }\n\n  try {\n    new Headers();\n    new Request('http://www.example.com');\n    new Response();\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * isNative checks if the given function is a native implementation\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function isNativeFunction(func: Function): boolean {\n  return func && /^function\\s+\\w+\\(\\)\\s+\\{\\s+\\[native code\\]\\s+\\}$/.test(func.toString());\n}\n\n/**\n * Tells whether current environment supports Fetch API natively\n * {@link supportsNativeFetch}.\n *\n * @returns true if `window.fetch` is natively implemented, false otherwise\n */\nexport function supportsNativeFetch(): boolean {\n  if (typeof EdgeRuntime === 'string') {\n    return true;\n  }\n\n  if (!supportsFetch()) {\n    return false;\n  }\n\n  // Fast path to avoid DOM I/O\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  if (isNativeFunction(WINDOW.fetch)) {\n    return true;\n  }\n\n  // window.fetch is implemented, but is polyfilled or already wrapped (e.g: by a chrome extension)\n  // so create a \"pure\" iframe to see if that has native fetch\n  let result = false;\n  const doc = WINDOW.document;\n  // eslint-disable-next-line deprecation/deprecation\n  if (doc && typeof (doc.createElement as unknown) === 'function') {\n    try {\n      const sandbox = doc.createElement('iframe');\n      sandbox.hidden = true;\n      doc.head.appendChild(sandbox);\n      if (sandbox.contentWindow && sandbox.contentWindow.fetch) {\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        result = isNativeFunction(sandbox.contentWindow.fetch);\n      }\n      doc.head.removeChild(sandbox);\n    } catch (err) {\n      DEBUG_BUILD &&\n        logger.warn('Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ', err);\n    }\n  }\n\n  return result;\n}\n\n/**\n * Tells whether current environment supports ReportingObserver API\n * {@link supportsReportingObserver}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsReportingObserver(): boolean {\n  return 'ReportingObserver' in WINDOW;\n}\n\n/**\n * Tells whether current environment supports Referrer Policy API\n * {@link supportsReferrerPolicy}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsReferrerPolicy(): boolean {\n  // Despite all stars in the sky saying that Edge supports old draft syntax, aka 'never', 'always', 'origin' and 'default'\n  // (see https://caniuse.com/#feat=referrer-policy),\n  // it doesn't. And it throws an exception instead of ignoring this parameter...\n  // REF: https://github.com/getsentry/raven-js/issues/1233\n\n  if (!supportsFetch()) {\n    return false;\n  }\n\n  try {\n    new Request('_', {\n      referrerPolicy: 'origin' as ReferrerPolicy,\n    });\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n"], "names": ["GLOBAL_OBJ", "DEBUG_BUILD", "logger"], "mappings": ";;;;;;AAIA,MAAM,MAAA,GAASA,oBAAW;;AAM1B;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,kBAAkB,GAAY;AAC9C,EAAE,IAAI;AACN,IAAI,IAAI,UAAU,CAAC,EAAE,CAAC;AACtB,IAAI,OAAO,IAAI;AACf,GAAI,CAAA,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,KAAK;AAChB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,gBAAgB,GAAY;AAC5C,EAAE,IAAI;AACN;AACA;AACA;AACA,IAAI,IAAI,QAAQ,CAAC,EAAE,CAAC;AACpB,IAAI,OAAO,IAAI;AACf,GAAI,CAAA,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,KAAK;AAChB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,oBAAoB,GAAY;AAChD,EAAE,IAAI;AACN,IAAI,IAAI,YAAY,CAAC,EAAE,CAAC;AACxB,IAAI,OAAO,IAAI;AACf,GAAI,CAAA,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,KAAK;AAChB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,GAAY;AACzC,EAAE,IAAI,EAAE,WAAW,MAAM,CAAC,EAAE;AAC5B,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,IAAI;AACN,IAAI,IAAI,OAAO,EAAE;AACjB,IAAI,IAAI,OAAO,CAAC,wBAAwB,CAAC;AACzC,IAAI,IAAI,QAAQ,EAAE;AAClB,IAAI,OAAO,IAAI;AACf,GAAI,CAAA,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,KAAK;AAChB;AACA;;AAEA;AACA;AACA;AACA;AACO,SAAS,gBAAgB,CAAC,IAAI,EAAqB;AAC1D,EAAE,OAAO,IAAA,IAAQ,kDAAkD,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;AACzF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,GAAY;AAC/C,EAAE,IAAI,OAAO,WAAY,KAAI,QAAQ,EAAE;AACvC,IAAI,OAAO,IAAI;AACf;;AAEA,EAAE,IAAI,CAAC,aAAa,EAAE,EAAE;AACxB,IAAI,OAAO,KAAK;AAChB;;AAEA;AACA;AACA,EAAE,IAAI,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;AACtC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA,EAAE,IAAI,MAAO,GAAE,KAAK;AACpB,EAAE,MAAM,GAAA,GAAM,MAAM,CAAC,QAAQ;AAC7B;AACA,EAAE,IAAI,GAAI,IAAG,QAAQ,GAAG,CAAC,aAAc,EAAA,KAAgB,UAAU,EAAE;AACnE,IAAI,IAAI;AACR,MAAM,MAAM,UAAU,GAAG,CAAC,aAAa,CAAC,QAAQ,CAAC;AACjD,MAAM,OAAO,CAAC,MAAO,GAAE,IAAI;AAC3B,MAAM,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACnC,MAAM,IAAI,OAAO,CAAC,aAAA,IAAiB,OAAO,CAAC,aAAa,CAAC,KAAK,EAAE;AAChE;AACA,QAAQ,MAAA,GAAS,gBAAgB,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC;AAC9D;AACA,MAAM,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACnC,KAAM,CAAA,OAAO,GAAG,EAAE;AAClB,MAAMC,sBAAY;AAClB,QAAQC,aAAM,CAAC,IAAI,CAAC,iFAAiF,EAAE,GAAG,CAAC;AAC3G;AACA;;AAEA,EAAE,OAAO,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,yBAAyB,GAAY;AACrD,EAAE,OAAO,mBAAoB,IAAG,MAAM;AACtC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,sBAAsB,GAAY;AAClD;AACA;AACA;AACA;;AAEA,EAAE,IAAI,CAAC,aAAa,EAAE,EAAE;AACxB,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,IAAI;AACN,IAAI,IAAI,OAAO,CAAC,GAAG,EAAE;AACrB,MAAM,cAAc,EAAE,QAAS;AAC/B,KAAK,CAAC;AACN,IAAI,OAAO,IAAI;AACf,GAAI,CAAA,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,KAAK;AAChB;AACA;;;;;;;;;;;"}