import { Client, DsnComponents, MetricBucketItem, SdkMetadata, StatsdEnvelope } from '../types-hoist';
/**
 * Captures aggregated metrics to the supplied client.
 */
export declare function captureAggregateMetrics(client: Client, metricBucketItems: Array<MetricBucketItem>): void;
/**
 * Create envelope from a metric aggregate.
 */
export declare function createMetricEnvelope(metricBucketItems: Array<MetricBucketItem>, dsn?: DsnComponents, metadata?: SdkMetadata, tunnel?: string): StatsdEnvelope;
//# sourceMappingURL=envelope.d.ts.map
