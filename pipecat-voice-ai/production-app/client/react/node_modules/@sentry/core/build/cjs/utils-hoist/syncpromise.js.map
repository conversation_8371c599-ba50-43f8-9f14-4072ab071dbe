{"version": 3, "file": "syncpromise.js", "sources": ["../../../src/utils-hoist/syncpromise.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/explicit-function-return-type */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { isThenable } from './is';\n\n/** SyncPromise internal states */\nconst enum States {\n  /** Pending */\n  PENDING = 0,\n  /** Resolved / OK */\n  RESOLVED = 1,\n  /** Rejected / Error */\n  REJECTED = 2,\n}\n\n// Overloads so we can call resolvedSyncPromise without arguments and generic argument\nexport function resolvedSyncPromise(): PromiseLike<void>;\nexport function resolvedSyncPromise<T>(value: T | PromiseLike<T>): PromiseLike<T>;\n\n/**\n * Creates a resolved sync promise.\n *\n * @param value the value to resolve the promise with\n * @returns the resolved sync promise\n */\nexport function resolvedSyncPromise<T>(value?: T | PromiseLike<T>): PromiseLike<T> {\n  return new SyncPromise(resolve => {\n    resolve(value);\n  });\n}\n\n/**\n * Creates a rejected sync promise.\n *\n * @param value the value to reject the promise with\n * @returns the rejected sync promise\n */\nexport function rejectedSyncPromise<T = never>(reason?: any): PromiseLike<T> {\n  return new SyncPromise((_, reject) => {\n    reject(reason);\n  });\n}\n\n/**\n * Thenable class that behaves like a Promise and follows it's interface\n * but is not async internally\n */\nclass SyncPromise<T> implements PromiseLike<T> {\n  private _state: States;\n  private _handlers: Array<[boolean, (value: T) => void, (reason: any) => any]>;\n  private _value: any;\n\n  public constructor(\n    executor: (resolve: (value?: T | PromiseLike<T> | null) => void, reject: (reason?: any) => void) => void,\n  ) {\n    this._state = States.PENDING;\n    this._handlers = [];\n\n    try {\n      executor(this._resolve, this._reject);\n    } catch (e) {\n      this._reject(e);\n    }\n  }\n\n  /** JSDoc */\n  public then<TResult1 = T, TResult2 = never>(\n    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | null,\n    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null,\n  ): PromiseLike<TResult1 | TResult2> {\n    return new SyncPromise((resolve, reject) => {\n      this._handlers.push([\n        false,\n        result => {\n          if (!onfulfilled) {\n            // TODO: ¯\\_(ツ)_/¯\n            // TODO: FIXME\n            resolve(result as any);\n          } else {\n            try {\n              resolve(onfulfilled(result));\n            } catch (e) {\n              reject(e);\n            }\n          }\n        },\n        reason => {\n          if (!onrejected) {\n            reject(reason);\n          } else {\n            try {\n              resolve(onrejected(reason));\n            } catch (e) {\n              reject(e);\n            }\n          }\n        },\n      ]);\n      this._executeHandlers();\n    });\n  }\n\n  /** JSDoc */\n  public catch<TResult = never>(\n    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | null,\n  ): PromiseLike<T | TResult> {\n    return this.then(val => val, onrejected);\n  }\n\n  /** JSDoc */\n  public finally<TResult>(onfinally?: (() => void) | null): PromiseLike<TResult> {\n    return new SyncPromise<TResult>((resolve, reject) => {\n      let val: TResult | any;\n      let isRejected: boolean;\n\n      return this.then(\n        value => {\n          isRejected = false;\n          val = value;\n          if (onfinally) {\n            onfinally();\n          }\n        },\n        reason => {\n          isRejected = true;\n          val = reason;\n          if (onfinally) {\n            onfinally();\n          }\n        },\n      ).then(() => {\n        if (isRejected) {\n          reject(val);\n          return;\n        }\n\n        resolve(val as unknown as any);\n      });\n    });\n  }\n\n  /** JSDoc */\n  private readonly _resolve = (value?: T | PromiseLike<T> | null) => {\n    this._setResult(States.RESOLVED, value);\n  };\n\n  /** JSDoc */\n  private readonly _reject = (reason?: any) => {\n    this._setResult(States.REJECTED, reason);\n  };\n\n  /** JSDoc */\n  private readonly _setResult = (state: States, value?: T | PromiseLike<T> | any) => {\n    if (this._state !== States.PENDING) {\n      return;\n    }\n\n    if (isThenable(value)) {\n      void (value as PromiseLike<T>).then(this._resolve, this._reject);\n      return;\n    }\n\n    this._state = state;\n    this._value = value;\n\n    this._executeHandlers();\n  };\n\n  /** JSDoc */\n  private readonly _executeHandlers = () => {\n    if (this._state === States.PENDING) {\n      return;\n    }\n\n    const cachedHandlers = this._handlers.slice();\n    this._handlers = [];\n\n    cachedHandlers.forEach(handler => {\n      if (handler[0]) {\n        return;\n      }\n\n      if (this._state === States.RESOLVED) {\n        handler[1](this._value as unknown as any);\n      }\n\n      if (this._state === States.REJECTED) {\n        handler[2](this._value);\n      }\n\n      handler[0] = true;\n    });\n  };\n}\n\nexport { SyncPromise };\n"], "names": ["isThenable"], "mappings": ";;;;AAAA;AACA;;AAGA;AACA,IAAkB,MAAA,CAAA,CAAA,CAAA,UAAA,MAAA,EAAA;AAClB;AACA,EAAE,MAAA,OAAA,GAAU,CAAC,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,SAAA,CAAA,GAAA,OAAA,CAAA,GAAA,SAAA;AACb;AACA,EAAE,MAAA,QAAA,GAAW,CAAC,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,UAAA,CAAA,GAAA,QAAA,CAAA,GAAA,UAAA;AACd;AACA,EAAE,MAAA,QAAA,GAAW,CAAC,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,UAAA,CAAA,GAAA,QAAA,CAAA,GAAA,UAAA;AACd,CAAA,EAAA,MAAA,KAAA,MAAA,GAAA,EAAA,CAAA,CAAA;;AAEA;;AAIA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAI,KAAK,EAAuC;AACnF,EAAE,OAAO,IAAI,WAAW,CAAC,WAAW;AACpC,IAAI,OAAO,CAAC,KAAK,CAAC;AAClB,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAY,MAAM,EAAwB;AAC7E,EAAE,OAAO,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,KAAK;AACxC,IAAI,MAAM,CAAC,MAAM,CAAC;AAClB,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAM,WAAW,CAA8B;;AAK/C,GAAS,WAAW;AACpB,IAAI,QAAQ;AACZ,IAAI,CAAA,WAAA,CAAA,SAAA,CAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,WAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,WAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,WAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACJ,IAAI,IAAI,CAAC,MAAA,GAAS,MAAM,CAAC,OAAO;AAChC,IAAI,IAAI,CAAC,SAAU,GAAE,EAAE;;AAEvB,IAAI,IAAI;AACR,MAAM,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC;AAC3C,KAAM,CAAA,OAAO,CAAC,EAAE;AAChB,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;AACrB;AACA;;AAEA;AACA,GAAS,IAAI;AACb,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAsC;AACtC,IAAI,OAAO,IAAI,WAAW,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;AAChD,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;AAC1B,QAAQ,KAAK;AACb,QAAQ,UAAU;AAClB,UAAU,IAAI,CAAC,WAAW,EAAE;AAC5B;AACA;AACA,YAAY,OAAO,CAAC,MAAA,EAAc;AAClC,iBAAiB;AACjB,YAAY,IAAI;AAChB,cAAc,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AAC1C,aAAc,CAAA,OAAO,CAAC,EAAE;AACxB,cAAc,MAAM,CAAC,CAAC,CAAC;AACvB;AACA;AACA,SAAS;AACT,QAAQ,UAAU;AAClB,UAAU,IAAI,CAAC,UAAU,EAAE;AAC3B,YAAY,MAAM,CAAC,MAAM,CAAC;AAC1B,iBAAiB;AACjB,YAAY,IAAI;AAChB,cAAc,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AACzC,aAAc,CAAA,OAAO,CAAC,EAAE;AACxB,cAAc,MAAM,CAAC,CAAC,CAAC;AACvB;AACA;AACA,SAAS;AACT,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,gBAAgB,EAAE;AAC7B,KAAK,CAAC;AACN;;AAEA;AACA,GAAS,KAAK;AACd,IAAI,UAAU;AACd,IAA8B;AAC9B,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,UAAU,CAAC;AAC5C;;AAEA;AACA,GAAS,OAAO,CAAU,SAAS,EAA8C;AACjF,IAAI,OAAO,IAAI,WAAW,CAAU,CAAC,OAAO,EAAE,MAAM,KAAK;AACzD,MAAM,IAAI,GAAG;AACb,MAAM,IAAI,UAAU;;AAEpB,MAAM,OAAO,IAAI,CAAC,IAAI;AACtB,QAAQ,SAAS;AACjB,UAAU,UAAA,GAAa,KAAK;AAC5B,UAAU,GAAA,GAAM,KAAK;AACrB,UAAU,IAAI,SAAS,EAAE;AACzB,YAAY,SAAS,EAAE;AACvB;AACA,SAAS;AACT,QAAQ,UAAU;AAClB,UAAU,UAAA,GAAa,IAAI;AAC3B,UAAU,GAAA,GAAM,MAAM;AACtB,UAAU,IAAI,SAAS,EAAE;AACzB,YAAY,SAAS,EAAE;AACvB;AACA,SAAS;AACT,OAAO,CAAC,IAAI,CAAC,MAAM;AACnB,QAAQ,IAAI,UAAU,EAAE;AACxB,UAAU,MAAM,CAAC,GAAG,CAAC;AACrB,UAAU;AACV;;AAEA,QAAQ,OAAO,CAAC,GAAA,EAAsB;AACtC,OAAO,CAAC;AACR,KAAK,CAAC;AACN;;AAEA;AACA,IAAmB,MAAA,GAAA,CAAA,IAAA,CAAA,QAAA,GAAW,CAAC,KAAK,KAAiC;AACrE,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC;AAC3C,IAAG;;AAEH;AACA,IAAmB,OAAA,GAAA,CAAA,IAAA,CAAA,OAAA,GAAU,CAAC,MAAM,KAAW;AAC/C,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC;AAC5C,IAAG;;AAEH;AACA,oBAAmB,UAAW,GAAE,CAAC,KAAK,EAAU,KAAK,KAAgC;AACrF,IAAI,IAAI,IAAI,CAAC,WAAW,MAAM,CAAC,OAAO,EAAE;AACxC,MAAM;AACN;;AAEA,IAAI,IAAIA,aAAU,CAAC,KAAK,CAAC,EAAE;AAC3B,MAAM,KAAK,CAAC,KAAM,GAAmB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC;AACtE,MAAM;AACN;;AAEA,IAAI,IAAI,CAAC,MAAO,GAAE,KAAK;AACvB,IAAI,IAAI,CAAC,MAAO,GAAE,KAAK;;AAEvB,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAC3B,IAAG;;AAEH;AACA,IAAmB,OAAA,GAAA,CAAA,IAAA,CAAA,gBAAA,GAAmB,MAAM;AAC5C,IAAI,IAAI,IAAI,CAAC,WAAW,MAAM,CAAC,OAAO,EAAE;AACxC,MAAM;AACN;;AAEA,IAAI,MAAM,iBAAiB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;AACjD,IAAI,IAAI,CAAC,SAAU,GAAE,EAAE;;AAEvB,IAAI,cAAc,CAAC,OAAO,CAAC,WAAW;AACtC,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;AACtB,QAAQ;AACR;;AAEA,MAAM,IAAI,IAAI,CAAC,WAAW,MAAM,CAAC,QAAQ,EAAE;AAC3C,QAAQ,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAyB;AACjD;;AAEA,MAAM,IAAI,IAAI,CAAC,WAAW,MAAM,CAAC,QAAQ,EAAE;AAC3C,QAAQ,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/B;;AAEA,MAAM,OAAO,CAAC,CAAC,CAAA,GAAI,IAAI;AACvB,KAAK,CAAC;AACN,IAAG;AACH;;;;;;"}