{"version": 3, "file": "span.d.ts", "sourceRoot": "", "sources": ["../../../src/types-hoist/span.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,eAAe,CAAC;AAClD,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC;AACxC,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AAC9C,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC/C,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,eAAe,CAAC;AAEvD,KAAK,cAAc,GAAG,QAAQ,GAAG,MAAM,CAAC;AACxC,KAAK,kBAAkB,GAAG,MAAM,CAAC;AACjC,KAAK,yBAAyB,GAAG,MAAM,CAAC;AACxC,KAAK,yBAAyB,GAAG,MAAM,CAAC;AACxC,MAAM,MAAM,UAAU,GAClB,cAAc,GACd,GAAG,cAAc,IAAI,kBAAkB,EAAE,GACzC,GAAG,cAAc,IAAI,kBAAkB,IAAI,yBAAyB,EAAE,GACtE,GAAG,cAAc,IAAI,kBAAkB,IAAI,yBAAyB,IAAI,yBAAyB,EAAE,CAAC;AAGxG,MAAM,MAAM,kBAAkB,GAC1B,MAAM,GACN,MAAM,GACN,OAAO,GACP,KAAK,CAAC,IAAI,GAAG,SAAS,GAAG,MAAM,CAAC,GAChC,KAAK,CAAC,IAAI,GAAG,SAAS,GAAG,MAAM,CAAC,GAChC,KAAK,CAAC,IAAI,GAAG,SAAS,GAAG,OAAO,CAAC,CAAC;AAEtC,MAAM,MAAM,cAAc,GAAG,OAAO,CAAC;IACnC,eAAe,EAAE,MAAM,CAAC;IACxB,WAAW,EAAE,MAAM,CAAC;IACpB,eAAe,EAAE,iBAAiB,CAAC;IACnC,oBAAoB,EAAE,MAAM,CAAC;CAC9B,CAAC,GACA,MAAM,CAAC,MAAM,EAAE,kBAAkB,GAAG,SAAS,CAAC,CAAC;AAEjD,MAAM,MAAM,aAAa,GAAG;IAC1B,GAAG,EAAE,MAAM,CAAC;IACZ,GAAG,EAAE,MAAM,CAAC;IACZ,KAAK,EAAE,MAAM,CAAC;IACd,GAAG,EAAE,MAAM,CAAC;IACZ,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,SAAS,CAAC;CAC9C,CAAC;AAEF,kEAAkE;AAClE,MAAM,MAAM,aAAa,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;AAEnD,uCAAuC;AACvC,MAAM,WAAW,QAAQ;IACvB,IAAI,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,CAAC;IAC9B,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,OAAO,EAAE,MAAM,CAAC;IAChB,eAAe,EAAE,MAAM,CAAC;IACxB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,CAAC,EAAE,UAAU,CAAC;IACpB,gBAAgB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;IACxD,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,YAAY,CAAC,EAAE,YAAY,CAAC;IAC5B,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAGD,KAAK,aAAa,GAAG,CAAC,CAAC;AACvB,KAAK,gBAAgB,GAAG,CAAC,CAAC;AAC1B,MAAM,MAAM,SAAS,GAAG,aAAa,GAAG,gBAAgB,CAAC;AAEzD,MAAM,WAAW,UAAU;IACzB;;;;;;;OAOG;IACH,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,UAAU,CAAC;IAC5C;;;;;OAKG;IACH,KAAK,CAAC,GAAG,EAAE,MAAM,GAAG,UAAU,CAAC;IAC/B;;;;;;;OAOG;IACH,GAAG,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;IACrC;;;;;;;;OAQG;IACH,SAAS,IAAI,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,eAAe;IAC9B;;;;;OAKG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;;;OAIG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,QAAQ,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAE/B;;;;;;;OAOG;IACH,UAAU,EAAE,SAAS,GAAG,MAAM,CAAC;IAE/B,oGAAoG;IACpG,UAAU,CAAC,EAAE,UAAU,GAAG,SAAS,CAAC;CACrC;AAED;;;GAGG;AACH,MAAM,WAAW,mBAAmB;IAClC;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE1B;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAExB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAElC;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAE9B;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE5B;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE7B;;OAEG;IACH,UAAU,CAAC,EAAE,cAAc,CAAC;IAE5B;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAEpC;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAElC;;;;;;OAMG;IACH,YAAY,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;CACpC;AAED;;GAEG;AACH,MAAM,WAAW,IAAI;IACnB;;;OAGG;IACH,WAAW,IAAI,eAAe,CAAC;IAE/B;;OAEG;IACH,GAAG,CAAC,YAAY,CAAC,EAAE,aAAa,GAAG,IAAI,CAAC;IAExC;;;OAGG;IACH,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,kBAAkB,GAAG,SAAS,GAAG,IAAI,CAAC;IAEvE;;;OAGG;IACH,aAAa,CAAC,UAAU,EAAE,cAAc,GAAG,IAAI,CAAC;IAEhD;;OAEG;IACH,SAAS,CAAC,MAAM,EAAE,UAAU,GAAG,IAAI,CAAC;IAEpC;;;;;;;;;;;;OAYG;IACH,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IAE/B;;;OAGG;IACH,WAAW,IAAI,OAAO,CAAC;IAEvB;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,qBAAqB,CAAC,EAAE,cAAc,GAAG,aAAa,EAAE,SAAS,CAAC,EAAE,aAAa,GAAG,IAAI,CAAC;IAEhH;;OAEG;IACH,OAAO,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,CAAC;IAE7B;;OAEG;IACH,QAAQ,CAAC,KAAK,EAAE,OAAO,GAAG,IAAI,CAAC;IAE/B;;OAEG;IACH,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CAC1D"}