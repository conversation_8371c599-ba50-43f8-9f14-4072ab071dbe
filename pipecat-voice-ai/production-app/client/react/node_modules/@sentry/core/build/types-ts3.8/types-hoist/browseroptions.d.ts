/**
 * Options added to the Browser SDK's init options that are specific for Replay.
 * Note: This type was moved to @sentry/core to avoid a circular dependency between Browser and Replay.
 */
export type BrowserClientReplayOptions = {
    /**
     * The sample rate for session-long replays.
     * 1.0 will record all sessions and 0 will record none.
     */
    replaysSessionSampleRate?: number;
    /**
     * The sample rate for sessions that has had an error occur.
     * This is independent of `sessionSampleRate`.
     * 1.0 will record all sessions and 0 will record none.
     */
    replaysOnErrorSampleRate?: number;
};
export type BrowserClientProfilingOptions = {
    /**
     * The sample rate for profiling
     * 1.0 will profile all transactions and 0 will profile none.
     */
    profilesSampleRate?: number;
};
//# sourceMappingURL=browseroptions.d.ts.map
