{"version": 3, "file": "worldwide.js", "sources": ["../../../src/utils-hoist/worldwide.ts"], "sourcesContent": ["/**\n * NOTE: In order to avoid circular dependencies, if you add a function to this module and it needs to print something,\n * you must either a) use `console.log` rather than the logger, or b) put your function elsewhere.\n *\n * Note: This file was originally called `global.ts`, but was changed to unblock users which might be doing\n * string replaces with bundlers like Vite for `global` (would break imports that rely on importing from utils/src/global).\n *\n * Why worldwide?\n *\n * Why not?\n */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport type { Client, MetricsAggregator, Scope } from '../types-hoist';\n\nimport type { SdkSource } from './env';\nimport type { logger } from './logger';\nimport { SDK_VERSION } from './version';\n\ninterface SentryCarrier {\n  acs?: any;\n  stack?: any;\n\n  globalScope?: Scope;\n  defaultIsolationScope?: Scope;\n  defaultCurrentScope?: Scope;\n  globalMetricsAggregators?: WeakMap<Client, MetricsAggregator> | undefined;\n  logger?: typeof logger;\n\n  /** Overwrites TextEncoder used in `@sentry/core`, need for `react-native@0.73` and older */\n  encodePolyfill?: (input: string) => Uint8Array;\n  /** Overwrites TextDecoder used in `@sentry/core`, need for `react-native@0.73` and older */\n  decodePolyfill?: (input: Uint8Array) => string;\n}\n\n// TODO(v9): Clean up or remove this type\ntype BackwardsCompatibleSentryCarrier = SentryCarrier & {\n  // pre-v7 hub (replaced by .stack)\n  hub: any;\n  integrations?: any[];\n  logger: any;\n  extensions?: {\n    /** Extension methods for the hub, which are bound to the current Hub instance */\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    [key: string]: Function;\n  };\n};\n\n/** Internal global with common properties and Sentry extensions  */\nexport type InternalGlobal = {\n  navigator?: { userAgent?: string; maxTouchPoints?: number };\n  console: Console;\n  PerformanceObserver?: any;\n  Sentry?: any;\n  onerror?: {\n    (event: object | string, source?: string, lineno?: number, colno?: number, error?: Error): any;\n    __SENTRY_INSTRUMENTED__?: true;\n  };\n  onunhandledrejection?: {\n    (event: unknown): boolean;\n    __SENTRY_INSTRUMENTED__?: true;\n  };\n  SENTRY_ENVIRONMENT?: string;\n  SENTRY_DSN?: string;\n  SENTRY_RELEASE?: {\n    id?: string;\n  };\n  SENTRY_SDK_SOURCE?: SdkSource;\n  /**\n   * Debug IDs are indirectly injected by Sentry CLI or bundler plugins to directly reference a particular source map\n   * for resolving of a source file. The injected code will place an entry into the record for each loaded bundle/JS\n   * file.\n   */\n  _sentryDebugIds?: Record<string, string>;\n  __SENTRY__: Record<Exclude<string, 'version'>, SentryCarrier> & {\n    version?: string;\n  } & BackwardsCompatibleSentryCarrier;\n  /**\n   * Raw module metadata that is injected by bundler plugins.\n   *\n   * Keys are `error.stack` strings, values are the metadata.\n   */\n  _sentryModuleMetadata?: Record<string, any>;\n  _sentryEsmLoaderHookRegistered?: boolean;\n};\n\n/** Get's the global object for the current JavaScript runtime */\nexport const GLOBAL_OBJ = globalThis as unknown as InternalGlobal;\n\n/**\n * Returns a global singleton contained in the global `__SENTRY__[]` object.\n *\n * If the singleton doesn't already exist in `__SENTRY__`, it will be created using the given factory\n * function and added to the `__SENTRY__` object.\n *\n * @param name name of the global singleton on __SENTRY__\n * @param creator creator Factory function to create the singleton if it doesn't already exist on `__SENTRY__`\n * @param obj (Optional) The global object on which to look for `__SENTRY__`, if not `GLOBAL_OBJ`'s return value\n * @returns the singleton\n */\nexport function getGlobalSingleton<T>(name: keyof SentryCarrier, creator: () => T, obj?: unknown): T {\n  const gbl = (obj || GLOBAL_OBJ) as InternalGlobal;\n  const __SENTRY__ = (gbl.__SENTRY__ = gbl.__SENTRY__ || {});\n  const versionedCarrier = (__SENTRY__[SDK_VERSION] = __SENTRY__[SDK_VERSION] || {});\n  return versionedCarrier[name] || (versionedCarrier[name] = creator());\n}\n"], "names": [], "mappings": ";;AAuFA;AACO,MAAM,UAAW,GAAE,UAAW;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,kBAAkB,CAAI,IAAI,EAAuB,OAAO,EAAW,GAAG,EAAe;AACrG,EAAE,MAAM,GAAI,IAAG,GAAI,IAAG,UAAU,CAAE;AAClC,EAAE,MAAM,UAAA,IAAc,GAAG,CAAC,UAAA,GAAa,GAAG,CAAC,UAAA,IAAc,EAAE,CAAC;AAC5D,EAAE,MAAM,gBAAiB,IAAG,UAAU,CAAC,WAAW,CAAA,GAAI,UAAU,CAAC,WAAW,CAAA,IAAK,EAAE,CAAC;AACpF,EAAE,OAAO,gBAAgB,CAAC,IAAI,MAAM,gBAAgB,CAAC,IAAI,CAAE,GAAE,OAAO,EAAE,CAAC;AACvE;;;;"}