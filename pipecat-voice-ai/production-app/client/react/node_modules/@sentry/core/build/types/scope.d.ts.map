{"version": 3, "file": "scope.d.ts", "sourceRoot": "", "sources": ["../../src/scope.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EACV,UAAU,EACV,UAAU,EACV,cAAc,EACd,MAAM,EACN,OAAO,EACP,QAAQ,EACR,KAAK,EACL,SAAS,EACT,cAAc,EACd,KAAK,EACL,MAAM,EACN,SAAS,EACT,kBAAkB,EAClB,cAAc,EACd,KAAK,IAAI,cAAc,EAEvB,SAAS,EACT,OAAO,EACP,aAAa,EACb,IAAI,EACL,MAAM,eAAe,CAAC;AAgBvB;;GAEG;AACH,cAAM,UAAW,YAAW,cAAc;IACxC,sCAAsC;IACtC,SAAS,CAAC,mBAAmB,EAAE,OAAO,CAAC;IAEvC,oDAAoD;IACpD,SAAS,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC,CAAC;IAEzD,iEAAiE;IACjE,SAAS,CAAC,gBAAgB,EAAE,cAAc,EAAE,CAAC;IAE7C,4BAA4B;IAC5B,SAAS,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;IAErC,WAAW;IACX,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC;IAEtB,WAAW;IACX,SAAS,CAAC,KAAK,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,CAAC;IAE9C,YAAY;IACZ,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;IAEzB,eAAe;IACf,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAC;IAE9B,kBAAkB;IAClB,SAAS,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;IAErC,kDAAkD;IAClD,SAAS,CAAC,mBAAmB,EAAE,kBAAkB,CAAC;IAElD;;;OAGG;IACH,SAAS,CAAC,sBAAsB,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAA;KAAE,CAAC;IAE7D,kBAAkB;IAClB,SAAS,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;IAElC,eAAe;IACf,SAAS,CAAC,MAAM,CAAC,EAAE,aAAa,CAAC;IAEjC;;;;;OAKG;IACH,SAAS,CAAC,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAEpC,cAAc;IACd,SAAS,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC;IAE7B,kCAAkC;IAElC,SAAS,CAAC,eAAe,CAAC,EAAE,cAAc,CAAC;IAE3C,+BAA+B;IAC/B,SAAS,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC;IAE3B,uDAAuD;IACvD,SAAS,CAAC,YAAY,CAAC,EAAE,MAAM,CAAC;;IAqBhC;;OAEG;IACI,KAAK,IAAI,UAAU;IAgC1B;;OAEG;IACI,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI;IAIlD;;OAEG;IACI,cAAc,CAAC,WAAW,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI;IAI5D;;OAEG;IACI,SAAS,CAAC,CAAC,SAAS,MAAM,KAAK,CAAC,GAAG,SAAS;IAInD;;OAEG;IACI,WAAW,IAAI,MAAM,GAAG,SAAS;IAIxC;;OAEG;IACI,gBAAgB,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,GAAG,IAAI;IAI/D;;OAEG;IACI,iBAAiB,CAAC,QAAQ,EAAE,cAAc,GAAG,IAAI;IAKxD;;OAEG;IACI,OAAO,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI;IAkBvC;;OAEG;IACI,OAAO,IAAI,IAAI,GAAG,SAAS;IAIlC;;OAEG;IAEI,iBAAiB,IAAI,cAAc,GAAG,SAAS;IAItD;;OAEG;IAEI,iBAAiB,CAAC,cAAc,CAAC,EAAE,cAAc,GAAG,IAAI;IAK/D;;OAEG;IACI,OAAO,CAAC,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,GAAG,IAAI;IASxD;;OAEG;IACI,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,GAAG,IAAI;IAMlD;;OAEG;IACI,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAStC;;OAEG;IACI,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI;IAMhD;;OAEG;IACI,cAAc,CAAC,WAAW,EAAE,MAAM,EAAE,GAAG,IAAI;IAMlD;;OAEG;IACI,QAAQ,CAAC,KAAK,EAAE,aAAa,GAAG,IAAI;IAM3C;;;;;;;;;;OAUG;IACI,kBAAkB,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI;IAM9C;;OAEG;IACI,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI;IAY7D;;OAEG;IACI,UAAU,CAAC,OAAO,CAAC,EAAE,OAAO,GAAG,IAAI;IAU1C;;OAEG;IACI,UAAU,IAAI,OAAO,GAAG,SAAS;IAIxC;;OAEG;IACI,MAAM,CAAC,cAAc,CAAC,EAAE,cAAc,GAAG,IAAI;IA4CpD;;OAEG;IACI,KAAK,IAAI,IAAI;IAoBpB;;OAEG;IACI,aAAa,CAAC,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI;IA0B3E;;OAEG;IACI,iBAAiB,IAAI,UAAU,GAAG,SAAS;IAIlD;;OAEG;IACI,gBAAgB,IAAI,IAAI;IAM/B;;OAEG;IACI,aAAa,CAAC,UAAU,EAAE,UAAU,GAAG,IAAI;IAKlD;;OAEG;IACI,gBAAgB,IAAI,IAAI;IAK/B,kBAAkB;IACX,YAAY,IAAI,SAAS;IAkBhC;;OAEG;IACI,wBAAwB,CAAC,OAAO,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAA;KAAE,GAAG,IAAI;IAK1E;;OAEG;IACI,qBAAqB,CAC1B,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,QAAQ,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC,GACxF,IAAI;IASP;;OAEG;IACI,qBAAqB,IAAI,kBAAkB;IAIlD;;OAEG;IACI,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,MAAM;IAwBrE;;OAEG;IACI,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,MAAM;IAyBvF;;OAEG;IACI,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,MAAM;IAa3D;;OAEG;IACH,SAAS,CAAC,qBAAqB,IAAI,IAAI;CAYxC;AAED;;GAEG;AACH,eAAO,MAAM,KAAK,mBAAa,CAAC;AAEhC;;GAEG;AACH,MAAM,MAAM,KAAK,GAAG,cAAc,CAAC"}