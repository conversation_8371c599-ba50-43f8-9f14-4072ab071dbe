{"version": 3, "file": "linkederrors.js", "sources": ["../../../src/integrations/linkederrors.ts"], "sourcesContent": ["import { defineIntegration } from '../integration';\nimport type { IntegrationFn } from '../types-hoist';\nimport { applyAggregateErrorsToEvent } from '../utils-hoist/aggregate-errors';\nimport { exceptionFromError } from '../utils-hoist/eventbuilder';\n\ninterface LinkedErrorsOptions {\n  key?: string;\n  limit?: number;\n}\n\nconst DEFAULT_KEY = 'cause';\nconst DEFAULT_LIMIT = 5;\n\nconst INTEGRATION_NAME = 'LinkedErrors';\n\nconst _linkedErrorsIntegration = ((options: LinkedErrorsOptions = {}) => {\n  const limit = options.limit || DEFAULT_LIMIT;\n  const key = options.key || DEFAULT_KEY;\n\n  return {\n    name: INTEGRATION_NAME,\n    preprocessEvent(event, hint, client) {\n      const options = client.getOptions();\n\n      applyAggregateErrorsToEvent(\n        exceptionFromError,\n        options.stackParser,\n        options.maxValueLength,\n        key,\n        limit,\n        event,\n        hint,\n      );\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const linkedErrorsIntegration = defineIntegration(_linkedErrorsIntegration);\n"], "names": ["applyAggregateErrorsToEvent", "exceptionFromError", "defineIntegration"], "mappings": ";;;;;;AAUA,MAAM,WAAA,GAAc,OAAO;AAC3B,MAAM,aAAA,GAAgB,CAAC;;AAEvB,MAAM,gBAAA,GAAmB,cAAc;;AAEvC,MAAM,wBAAA,IAA4B,CAAC,OAAO,GAAwB,EAAE,KAAK;AACzE,EAAE,MAAM,KAAM,GAAE,OAAO,CAAC,KAAA,IAAS,aAAa;AAC9C,EAAE,MAAM,GAAI,GAAE,OAAO,CAAC,GAAA,IAAO,WAAW;;AAExC,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE;AACzC,MAAM,MAAM,OAAQ,GAAE,MAAM,CAAC,UAAU,EAAE;;AAEzC,MAAMA,2CAA2B;AACjC,QAAQC,+BAAkB;AAC1B,QAAQ,OAAO,CAAC,WAAW;AAC3B,QAAQ,OAAO,CAAC,cAAc;AAC9B,QAAQ,GAAG;AACX,QAAQ,KAAK;AACb,QAAQ,KAAK;AACb,QAAQ,IAAI;AACZ,OAAO;AACP,KAAK;AACL,GAAG;AACH,CAAC,CAAE;;MAEU,uBAAwB,GAAEC,6BAAiB,CAAC,wBAAwB;;;;"}