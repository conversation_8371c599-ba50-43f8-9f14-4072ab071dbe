interface ExtraErrorDataOptions {
    /**
     * The object depth up to which to capture data on error objects.
     */
    depth: number;
    /**
     * Whether to capture error causes. Defaults to true.
     *
     * More information: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error/cause
     */
    captureErrorCause: boolean;
}
export declare const extraErrorDataIntegration: (options?: Partial<ExtraErrorDataOptions> | undefined) => import("../types-hoist").Integration;
export {};
//# sourceMappingURL=extraerrordata.d.ts.map
