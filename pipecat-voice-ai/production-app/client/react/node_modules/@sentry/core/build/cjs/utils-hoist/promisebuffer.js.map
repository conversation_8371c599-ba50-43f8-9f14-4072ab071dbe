{"version": 3, "file": "promisebuffer.js", "sources": ["../../../src/utils-hoist/promisebuffer.ts"], "sourcesContent": ["import { SentryError } from './error';\nimport { SyncPromise, rejectedSyncPromise, resolvedSyncPromise } from './syncpromise';\n\nexport interface PromiseBuffer<T> {\n  // exposes the internal array so tests can assert on the state of it.\n  // XXX: this really should not be public api.\n  $: Array<PromiseLike<T>>;\n  add(taskProducer: () => PromiseLike<T>): PromiseLike<T>;\n  drain(timeout?: number): PromiseLike<boolean>;\n}\n\n/**\n * Creates an new PromiseBuffer object with the specified limit\n * @param limit max number of promises that can be stored in the buffer\n */\nexport function makePromiseBuffer<T>(limit?: number): PromiseBuffer<T> {\n  const buffer: Array<PromiseLike<T>> = [];\n\n  function isReady(): boolean {\n    return limit === undefined || buffer.length < limit;\n  }\n\n  /**\n   * Remove a promise from the queue.\n   *\n   * @param task Can be any PromiseLike<T>\n   * @returns Removed promise.\n   */\n  function remove(task: PromiseLike<T>): PromiseLike<T | void> {\n    return buffer.splice(buffer.indexOf(task), 1)[0] || Promise.resolve(undefined);\n  }\n\n  /**\n   * Add a promise (representing an in-flight action) to the queue, and set it to remove itself on fulfillment.\n   *\n   * @param taskProducer A function producing any PromiseLike<T>; In previous versions this used to be `task:\n   *        PromiseLike<T>`, but under that model, Promises were instantly created on the call-site and their executor\n   *        functions therefore ran immediately. Thus, even if the buffer was full, the action still happened. By\n   *        requiring the promise to be wrapped in a function, we can defer promise creation until after the buffer\n   *        limit check.\n   * @returns The original promise.\n   */\n  function add(taskProducer: () => PromiseLike<T>): PromiseLike<T> {\n    if (!isReady()) {\n      return rejectedSyncPromise(new SentryError('Not adding Promise because buffer limit was reached.'));\n    }\n\n    // start the task and add its promise to the queue\n    const task = taskProducer();\n    if (buffer.indexOf(task) === -1) {\n      buffer.push(task);\n    }\n    void task\n      .then(() => remove(task))\n      // Use `then(null, rejectionHandler)` rather than `catch(rejectionHandler)` so that we can use `PromiseLike`\n      // rather than `Promise`. `PromiseLike` doesn't have a `.catch` method, making its polyfill smaller. (ES5 didn't\n      // have promises, so TS has to polyfill when down-compiling.)\n      .then(null, () =>\n        remove(task).then(null, () => {\n          // We have to add another catch here because `remove()` starts a new promise chain.\n        }),\n      );\n    return task;\n  }\n\n  /**\n   * Wait for all promises in the queue to resolve or for timeout to expire, whichever comes first.\n   *\n   * @param timeout The time, in ms, after which to resolve to `false` if the queue is still non-empty. Passing `0` (or\n   * not passing anything) will make the promise wait as long as it takes for the queue to drain before resolving to\n   * `true`.\n   * @returns A promise which will resolve to `true` if the queue is already empty or drains before the timeout, and\n   * `false` otherwise\n   */\n  function drain(timeout?: number): PromiseLike<boolean> {\n    return new SyncPromise<boolean>((resolve, reject) => {\n      let counter = buffer.length;\n\n      if (!counter) {\n        return resolve(true);\n      }\n\n      // wait for `timeout` ms and then resolve to `false` (if not cancelled first)\n      const capturedSetTimeout = setTimeout(() => {\n        if (timeout && timeout > 0) {\n          resolve(false);\n        }\n      }, timeout);\n\n      // if all promises resolve in time, cancel the timer and resolve to `true`\n      buffer.forEach(item => {\n        void resolvedSyncPromise(item).then(() => {\n          if (!--counter) {\n            clearTimeout(capturedSetTimeout);\n            resolve(true);\n          }\n        }, reject);\n      });\n    });\n  }\n\n  return {\n    $: buffer,\n    add,\n    drain,\n  };\n}\n"], "names": ["rejectedSyncPromise", "SentryError", "SyncPromise", "resolvedSyncPromise"], "mappings": ";;;;;AAWA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAI,KAAK,EAA6B;AACvE,EAAE,MAAM,MAAM,GAA0B,EAAE;;AAE1C,EAAE,SAAS,OAAO,GAAY;AAC9B,IAAI,OAAO,UAAU,SAAA,IAAa,MAAM,CAAC,MAAO,GAAE,KAAK;AACvD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,MAAM,CAAC,IAAI,EAAyC;AAC/D,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAE,IAAG,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC;AAClF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,GAAG,CAAC,YAAY,EAAwC;AACnE,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;AACpB,MAAM,OAAOA,+BAAmB,CAAC,IAAIC,iBAAW,CAAC,sDAAsD,CAAC,CAAC;AACzG;;AAEA;AACA,IAAI,MAAM,IAAA,GAAO,YAAY,EAAE;AAC/B,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAE,KAAI,CAAC,CAAC,EAAE;AACrC,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;AACvB;AACA,IAAI,KAAK;AACT,OAAO,IAAI,CAAC,MAAM,MAAM,CAAC,IAAI,CAAC;AAC9B;AACA;AACA;AACA,OAAO,IAAI,CAAC,IAAI,EAAE;AAClB,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM;AACtC;AACA,SAAS,CAAC;AACV,OAAO;AACP,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,KAAK,CAAC,OAAO,EAAiC;AACzD,IAAI,OAAO,IAAIC,uBAAW,CAAU,CAAC,OAAO,EAAE,MAAM,KAAK;AACzD,MAAM,IAAI,OAAA,GAAU,MAAM,CAAC,MAAM;;AAEjC,MAAM,IAAI,CAAC,OAAO,EAAE;AACpB,QAAQ,OAAO,OAAO,CAAC,IAAI,CAAC;AAC5B;;AAEA;AACA,MAAM,MAAM,kBAAmB,GAAE,UAAU,CAAC,MAAM;AAClD,QAAQ,IAAI,OAAA,IAAW,OAAQ,GAAE,CAAC,EAAE;AACpC,UAAU,OAAO,CAAC,KAAK,CAAC;AACxB;AACA,OAAO,EAAE,OAAO,CAAC;;AAEjB;AACA,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ;AAC7B,QAAQ,KAAKC,+BAAmB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM;AAClD,UAAU,IAAI,CAAC,EAAE,OAAO,EAAE;AAC1B,YAAY,YAAY,CAAC,kBAAkB,CAAC;AAC5C,YAAY,OAAO,CAAC,IAAI,CAAC;AACzB;AACA,SAAS,EAAE,MAAM,CAAC;AAClB,OAAO,CAAC;AACR,KAAK,CAAC;AACN;;AAEA,EAAE,OAAO;AACT,IAAI,CAAC,EAAE,MAAM;AACb,IAAI,GAAG;AACP,IAAI,KAAK;AACT,GAAG;AACH;;;;"}