import { Options } from '../types-hoist';
/**
 * Determines if tracing is currently enabled.
 *
 * Tracing is enabled when at least one of `tracesSampleRate` and `tracesSampler` is defined in the SDK config.
 */
export declare function hasTracingEnabled(maybeOptions?: Pick<Options, 'tracesSampleRate' | 'tracesSampler' | 'enableTracing'> | undefined): boolean;
//# sourceMappingURL=hasTracingEnabled.d.ts.map
