{"version": 3, "file": "metric-summary.js", "sources": ["../../../src/metrics/metric-summary.ts"], "sourcesContent": ["import type { MeasurementUnit, Span } from '../types-hoist';\nimport type { MetricSummary } from '../types-hoist';\nimport type { Primitive } from '../types-hoist';\nimport { dropUndefinedKeys } from '../utils-hoist/object';\nimport type { MetricType } from './types';\n\n/**\n * key: bucketKey\n * value: [exportKey, MetricSummary]\n */\ntype MetricSummaryStorage = Map<string, [string, MetricSummary]>;\n\nconst METRICS_SPAN_FIELD = '_sentryMetrics';\n\ntype SpanWithPotentialMetrics = Span & {\n  [METRICS_SPAN_FIELD]?: MetricSummaryStorage;\n};\n\n/**\n * Fetches the metric summary if it exists for the passed span\n */\nexport function getMetricSummaryJsonForSpan(span: Span): Record<string, Array<MetricSummary>> | undefined {\n  const storage = (span as SpanWithPotentialMetrics)[METRICS_SPAN_FIELD];\n\n  if (!storage) {\n    return undefined;\n  }\n  const output: Record<string, Array<MetricSummary>> = {};\n\n  for (const [, [exportKey, summary]] of storage) {\n    const arr = output[exportKey] || (output[exportKey] = []);\n    arr.push(dropUndefinedKeys(summary));\n  }\n\n  return output;\n}\n\n/**\n * Updates the metric summary on a span.\n */\nexport function updateMetricSummaryOnSpan(\n  span: Span,\n  metricType: MetricType,\n  sanitizedName: string,\n  value: number,\n  unit: MeasurementUnit,\n  tags: Record<string, Primitive>,\n  bucketKey: string,\n): void {\n  const existingStorage = (span as SpanWithPotentialMetrics)[METRICS_SPAN_FIELD];\n  const storage =\n    existingStorage ||\n    ((span as SpanWithPotentialMetrics)[METRICS_SPAN_FIELD] = new Map<string, [string, MetricSummary]>());\n\n  const exportKey = `${metricType}:${sanitizedName}@${unit}`;\n  const bucketItem = storage.get(bucketKey);\n\n  if (bucketItem) {\n    const [, summary] = bucketItem;\n    storage.set(bucketKey, [\n      exportKey,\n      {\n        min: Math.min(summary.min, value),\n        max: Math.max(summary.max, value),\n        count: (summary.count += 1),\n        sum: (summary.sum += value),\n        tags: summary.tags,\n      },\n    ]);\n  } else {\n    storage.set(bucketKey, [\n      exportKey,\n      {\n        min: value,\n        max: value,\n        count: 1,\n        sum: value,\n        tags,\n      },\n    ]);\n  }\n}\n"], "names": [], "mappings": ";;AAMA;AACA;AACA;AACA;;AAGA,MAAM,kBAAA,GAAqB,gBAAgB;;AAM3C;AACA;AACA;AACO,SAAS,2BAA2B,CAAC,IAAI,EAA0D;AAC1G,EAAE,MAAM,UAAU,CAAC,OAAkC,kBAAkB,CAAC;;AAExE,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,SAAS;AACpB;AACA,EAAE,MAAM,MAAM,GAAyC,EAAE;;AAEzD,EAAE,KAAK,MAAM,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAE,IAAG,OAAO,EAAE;AAClD,IAAI,MAAM,GAAI,GAAE,MAAM,CAAC,SAAS,CAAE,KAAI,MAAM,CAAC,SAAS,CAAA,GAAI,EAAE,CAAC;AAC7D,IAAI,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;AACxC;;AAEA,EAAE,OAAO,MAAM;AACf;;AAEA;AACA;AACA;AACO,SAAS,yBAAyB;AACzC,EAAE,IAAI;AACN,EAAE,UAAU;AACZ,EAAE,aAAa;AACf,EAAE,KAAK;AACP,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,SAAS;AACX,EAAQ;AACR,EAAE,MAAM,kBAAkB,CAAC,OAAkC,kBAAkB,CAAC;AAChF,EAAE,MAAM,OAAQ;AAChB,IAAI,eAAgB;AACpB,KAAK,CAAC,IAAK,GAA6B,kBAAkB,CAAE,GAAE,IAAI,GAAG,EAAmC,CAAC;;AAEzG,EAAE,MAAM,SAAA,GAAY,CAAC,EAAA,UAAA,CAAA,CAAA,EAAA,aAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA;AACA,EAAA,MAAA,UAAA,GAAA,OAAA,CAAA,GAAA,CAAA,SAAA,CAAA;;AAEA,EAAA,IAAA,UAAA,EAAA;AACA,IAAA,MAAA,GAAA,OAAA,CAAA,GAAA,UAAA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA;AACA,MAAA,SAAA;AACA,MAAA;AACA,QAAA,GAAA,EAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,GAAA,EAAA,KAAA,CAAA;AACA,QAAA,GAAA,EAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,GAAA,EAAA,KAAA,CAAA;AACA,QAAA,KAAA,GAAA,OAAA,CAAA,KAAA,IAAA,CAAA,CAAA;AACA,QAAA,GAAA,GAAA,OAAA,CAAA,GAAA,IAAA,KAAA,CAAA;AACA,QAAA,IAAA,EAAA,OAAA,CAAA,IAAA;AACA,OAAA;AACA,KAAA,CAAA;AACA,GAAA,MAAA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA;AACA,MAAA,SAAA;AACA,MAAA;AACA,QAAA,GAAA,EAAA,KAAA;AACA,QAAA,GAAA,EAAA,KAAA;AACA,QAAA,KAAA,EAAA,CAAA;AACA,QAAA,GAAA,EAAA,KAAA;AACA,QAAA,IAAA;AACA,OAAA;AACA,KAAA,CAAA;AACA;AACA;;;;"}