{"version": 3, "file": "sampling.js", "sources": ["../../../src/tracing/sampling.ts"], "sourcesContent": ["import type { Options, RequestEventData, SamplingContext } from '../types-hoist';\n\nimport { getIsolationScope } from '../currentScopes';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { logger } from '../utils-hoist/logger';\nimport { hasTracingEnabled } from '../utils/hasTracingEnabled';\nimport { parseSampleRate } from '../utils/parseSampleRate';\n\n/**\n * Makes a sampling decision for the given options.\n *\n * Called every time a root span is created. Only root spans which emerge with a `sampled` value of `true` will be\n * sent to Sentry.\n */\nexport function sampleSpan(\n  options: Pick<Options, 'tracesSampleRate' | 'tracesSampler' | 'enableTracing'>,\n  samplingContext: SamplingContext,\n): [sampled: boolean, sampleRate?: number] {\n  // nothing to do if tracing is not enabled\n  if (!hasTracingEnabled(options)) {\n    return [false];\n  }\n\n  // Casting this from unknown, as the type of `sdkProcessingMetadata` is only changed in v9 and `normalizedRequest` is set in SentryHttpInstrumentation\n  const normalizedRequest = getIsolationScope().getScopeData().sdkProcessingMetadata\n    .normalizedRequest as RequestEventData;\n\n  const enhancedSamplingContext = {\n    ...samplingContext,\n    normalizedRequest: samplingContext.normalizedRequest || normalizedRequest,\n  };\n\n  // we would have bailed already if neither `tracesSampler` nor `tracesSampleRate` nor `enableTracing` were defined, so one of these should\n  // work; prefer the hook if so\n  let sampleRate;\n  if (typeof options.tracesSampler === 'function') {\n    sampleRate = options.tracesSampler(enhancedSamplingContext);\n  } else if (enhancedSamplingContext.parentSampled !== undefined) {\n    sampleRate = enhancedSamplingContext.parentSampled;\n  } else if (typeof options.tracesSampleRate !== 'undefined') {\n    sampleRate = options.tracesSampleRate;\n  } else {\n    // When `enableTracing === true`, we use a sample rate of 100%\n    sampleRate = 1;\n  }\n\n  // Since this is coming from the user (or from a function provided by the user), who knows what we might get.\n  // (The only valid values are booleans or numbers between 0 and 1.)\n  const parsedSampleRate = parseSampleRate(sampleRate);\n\n  if (parsedSampleRate === undefined) {\n    DEBUG_BUILD && logger.warn('[Tracing] Discarding transaction because of invalid sample rate.');\n    return [false];\n  }\n\n  // if the function returned 0 (or false), or if `tracesSampleRate` is 0, it's a sign the transaction should be dropped\n  if (!parsedSampleRate) {\n    DEBUG_BUILD &&\n      logger.log(\n        `[Tracing] Discarding transaction because ${\n          typeof options.tracesSampler === 'function'\n            ? 'tracesSampler returned 0 or false'\n            : 'a negative sampling decision was inherited or tracesSampleRate is set to 0'\n        }`,\n      );\n    return [false, parsedSampleRate];\n  }\n\n  // Now we roll the dice. Math.random is inclusive of 0, but not of 1, so strict < is safe here. In case sampleRate is\n  // a boolean, the < comparison will cause it to be automatically cast to 1 if it's true and 0 if it's false.\n  const shouldSample = Math.random() < parsedSampleRate;\n\n  // if we're not going to keep it, we're done\n  if (!shouldSample) {\n    DEBUG_BUILD &&\n      logger.log(\n        `[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(\n          sampleRate,\n        )})`,\n      );\n    return [false, parsedSampleRate];\n  }\n\n  return [true, parsedSampleRate];\n}\n"], "names": [], "mappings": ";;;;;;AAQA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU;AAC1B,EAAE,OAAO;AACT,EAAE,eAAe;AACjB,EAA2C;AAC3C;AACA,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE;AACnC,IAAI,OAAO,CAAC,KAAK,CAAC;AAClB;;AAEA;AACA,EAAE,MAAM,iBAAkB,GAAE,iBAAiB,EAAE,CAAC,YAAY,EAAE,CAAC;AAC/D,KAAK,iBAAkB;;AAEvB,EAAE,MAAM,0BAA0B;AAClC,IAAI,GAAG,eAAe;AACtB,IAAI,iBAAiB,EAAE,eAAe,CAAC,iBAAA,IAAqB,iBAAiB;AAC7E,GAAG;;AAEH;AACA;AACA,EAAE,IAAI,UAAU;AAChB,EAAE,IAAI,OAAO,OAAO,CAAC,aAAc,KAAI,UAAU,EAAE;AACnD,IAAI,aAAa,OAAO,CAAC,aAAa,CAAC,uBAAuB,CAAC;AAC/D,GAAE,MAAO,IAAI,uBAAuB,CAAC,aAAA,KAAkB,SAAS,EAAE;AAClE,IAAI,UAAW,GAAE,uBAAuB,CAAC,aAAa;AACtD,GAAE,MAAO,IAAI,OAAO,OAAO,CAAC,gBAAA,KAAqB,WAAW,EAAE;AAC9D,IAAI,UAAW,GAAE,OAAO,CAAC,gBAAgB;AACzC,SAAS;AACT;AACA,IAAI,UAAA,GAAa,CAAC;AAClB;;AAEA;AACA;AACA,EAAE,MAAM,gBAAiB,GAAE,eAAe,CAAC,UAAU,CAAC;;AAEtD,EAAE,IAAI,gBAAiB,KAAI,SAAS,EAAE;AACtC,IAAI,eAAe,MAAM,CAAC,IAAI,CAAC,kEAAkE,CAAC;AAClG,IAAI,OAAO,CAAC,KAAK,CAAC;AAClB;;AAEA;AACA,EAAE,IAAI,CAAC,gBAAgB,EAAE;AACzB,IAAI,WAAY;AAChB,MAAM,MAAM,CAAC,GAAG;AAChB,QAAQ,CAAC,yCAAyC;AAClD,UAAU,OAAO,OAAO,CAAC,aAAA,KAAkB;AAC3C,cAAc;AACd,cAAc;AACd,SAAS,CAAA;AACA,OAAA;AACA,IAAA,OAAA,CAAA,KAAA,EAAA,gBAAA,CAAA;AACA;;AAEA;AACA;AACA,EAAA,MAAA,YAAA,GAAA,IAAA,CAAA,MAAA,EAAA,GAAA,gBAAA;;AAEA;AACA,EAAA,IAAA,CAAA,YAAA,EAAA;AACA,IAAA,WAAA;AACA,MAAA,MAAA,CAAA,GAAA;AACA,QAAA,CAAA,iGAAA,EAAA,MAAA;AACA,UAAA,UAAA;AACA,SAAA,CAAA,CAAA,CAAA;AACA,OAAA;AACA,IAAA,OAAA,CAAA,KAAA,EAAA,gBAAA,CAAA;AACA;;AAEA,EAAA,OAAA,CAAA,IAAA,EAAA,gBAAA,CAAA;AACA;;;;"}