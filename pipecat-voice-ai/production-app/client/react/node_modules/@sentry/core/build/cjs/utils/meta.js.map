{"version": 3, "file": "meta.js", "sources": ["../../../src/utils/meta.ts"], "sourcesContent": ["import { getTraceData } from './traceData';\n\n/**\n * Returns a string of meta tags that represent the current trace data.\n *\n * You can use this to propagate a trace from your server-side rendered Html to the browser.\n * This function returns up to two meta tags, `sentry-trace` and `baggage`, depending on the\n * current trace data state.\n *\n * @example\n * Usage example:\n *\n * ```js\n * function renderHtml() {\n *   return `\n *     <head>\n *       ${getTraceMetaTags()}\n *     </head>\n *   `;\n * }\n * ```\n *\n */\nexport function getTraceMetaTags(): string {\n  return Object.entries(getTraceData())\n    .map(([key, value]) => `<meta name=\"${key}\" content=\"${value}\"/>`)\n    .join('\\n');\n}\n"], "names": ["getTraceData"], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,gBAAgB,GAAW;AAC3C,EAAE,OAAO,MAAM,CAAC,OAAO,CAACA,sBAAY,EAAE;AACtC,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,YAAY,EAAE,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,GAAG,CAAC;AACrE,KAAK,IAAI,CAAC,IAAI,CAAC;AACf;;;;"}