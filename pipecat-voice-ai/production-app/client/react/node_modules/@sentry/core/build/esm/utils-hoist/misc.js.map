{"version": 3, "file": "misc.js", "sources": ["../../../src/utils-hoist/misc.ts"], "sourcesContent": ["import type { Event, Exception, Mechanism, StackFrame } from '../types-hoist';\n\nimport { addNonEnumerableProperty } from './object';\nimport { snipLine } from './string';\nimport { GLOBAL_OBJ } from './worldwide';\n\ninterface CryptoInternal {\n  getRandomValues(array: Uint8Array): Uint8Array;\n  randomUUID?(): string;\n}\n\n/** An interface for common properties on global */\ninterface CryptoGlobal {\n  msCrypto?: CryptoInternal;\n  crypto?: CryptoInternal;\n}\n\n/**\n * UUID4 generator\n *\n * @returns string Generated UUID4.\n */\nexport function uuid4(): string {\n  const gbl = GLOBAL_OBJ as typeof GLOBAL_OBJ & CryptoGlobal;\n  const crypto = gbl.crypto || gbl.msCrypto;\n\n  let getRandomByte = (): number => Math.random() * 16;\n  try {\n    if (crypto && crypto.randomUUID) {\n      return crypto.randomUUID().replace(/-/g, '');\n    }\n    if (crypto && crypto.getRandomValues) {\n      getRandomByte = () => {\n        // crypto.getRandomValues might return undefined instead of the typed array\n        // in old Chromium versions (e.g. 23.0.1235.0 (151422))\n        // However, `typedArray` is still filled in-place.\n        // @see https://developer.mozilla.org/en-US/docs/Web/API/Crypto/getRandomValues#typedarray\n        const typedArray = new Uint8Array(1);\n        crypto.getRandomValues(typedArray);\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        return typedArray[0]!;\n      };\n    }\n  } catch (_) {\n    // some runtimes can crash invoking crypto\n    // https://github.com/getsentry/sentry-javascript/issues/8935\n  }\n\n  // http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript/2117523#2117523\n  // Concatenating the following numbers as strings results in '10000000100040008000100000000000'\n  return (([1e7] as unknown as string) + 1e3 + 4e3 + 8e3 + 1e11).replace(/[018]/g, c =>\n    // eslint-disable-next-line no-bitwise\n    ((c as unknown as number) ^ ((getRandomByte() & 15) >> ((c as unknown as number) / 4))).toString(16),\n  );\n}\n\nfunction getFirstException(event: Event): Exception | undefined {\n  return event.exception && event.exception.values ? event.exception.values[0] : undefined;\n}\n\n/**\n * Extracts either message or type+value from an event that can be used for user-facing logs\n * @returns event's description\n */\nexport function getEventDescription(event: Event): string {\n  const { message, event_id: eventId } = event;\n  if (message) {\n    return message;\n  }\n\n  const firstException = getFirstException(event);\n  if (firstException) {\n    if (firstException.type && firstException.value) {\n      return `${firstException.type}: ${firstException.value}`;\n    }\n    return firstException.type || firstException.value || eventId || '<unknown>';\n  }\n  return eventId || '<unknown>';\n}\n\n/**\n * Adds exception values, type and value to an synthetic Exception.\n * @param event The event to modify.\n * @param value Value of the exception.\n * @param type Type of the exception.\n * @hidden\n */\nexport function addExceptionTypeValue(event: Event, value?: string, type?: string): void {\n  const exception = (event.exception = event.exception || {});\n  const values = (exception.values = exception.values || []);\n  const firstException = (values[0] = values[0] || {});\n  if (!firstException.value) {\n    firstException.value = value || '';\n  }\n  if (!firstException.type) {\n    firstException.type = type || 'Error';\n  }\n}\n\n/**\n * Adds exception mechanism data to a given event. Uses defaults if the second parameter is not passed.\n *\n * @param event The event to modify.\n * @param newMechanism Mechanism data to add to the event.\n * @hidden\n */\nexport function addExceptionMechanism(event: Event, newMechanism?: Partial<Mechanism>): void {\n  const firstException = getFirstException(event);\n  if (!firstException) {\n    return;\n  }\n\n  const defaultMechanism = { type: 'generic', handled: true };\n  const currentMechanism = firstException.mechanism;\n  firstException.mechanism = { ...defaultMechanism, ...currentMechanism, ...newMechanism };\n\n  if (newMechanism && 'data' in newMechanism) {\n    const mergedData = { ...(currentMechanism && currentMechanism.data), ...newMechanism.data };\n    firstException.mechanism.data = mergedData;\n  }\n}\n\n// https://semver.org/#is-there-a-suggested-regular-expression-regex-to-check-a-semver-string\nconst SEMVER_REGEXP =\n  /^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$/;\n\n/**\n * Represents Semantic Versioning object\n */\ninterface SemVer {\n  major?: number;\n  minor?: number;\n  patch?: number;\n  prerelease?: string;\n  buildmetadata?: string;\n}\n\nfunction _parseInt(input: string | undefined): number {\n  return parseInt(input || '', 10);\n}\n\n/**\n * Parses input into a SemVer interface\n * @param input string representation of a semver version\n */\nexport function parseSemver(input: string): SemVer {\n  const match = input.match(SEMVER_REGEXP) || [];\n  const major = _parseInt(match[1]);\n  const minor = _parseInt(match[2]);\n  const patch = _parseInt(match[3]);\n  return {\n    buildmetadata: match[5],\n    major: isNaN(major) ? undefined : major,\n    minor: isNaN(minor) ? undefined : minor,\n    patch: isNaN(patch) ? undefined : patch,\n    prerelease: match[4],\n  };\n}\n\n/**\n * This function adds context (pre/post/line) lines to the provided frame\n *\n * @param lines string[] containing all lines\n * @param frame StackFrame that will be mutated\n * @param linesOfContext number of context lines we want to add pre/post\n */\nexport function addContextToFrame(lines: string[], frame: StackFrame, linesOfContext: number = 5): void {\n  // When there is no line number in the frame, attaching context is nonsensical and will even break grouping\n  if (frame.lineno === undefined) {\n    return;\n  }\n\n  const maxLines = lines.length;\n  const sourceLine = Math.max(Math.min(maxLines - 1, frame.lineno - 1), 0);\n\n  frame.pre_context = lines\n    .slice(Math.max(0, sourceLine - linesOfContext), sourceLine)\n    .map((line: string) => snipLine(line, 0));\n\n  // We guard here to ensure this is not larger than the existing number of lines\n  const lineIndex = Math.min(maxLines - 1, sourceLine);\n\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  frame.context_line = snipLine(lines[lineIndex]!, frame.colno || 0);\n\n  frame.post_context = lines\n    .slice(Math.min(sourceLine + 1, maxLines), sourceLine + 1 + linesOfContext)\n    .map((line: string) => snipLine(line, 0));\n}\n\n/**\n * Checks whether or not we've already captured the given exception (note: not an identical exception - the very object\n * in question), and marks it captured if not.\n *\n * This is useful because it's possible for an error to get captured by more than one mechanism. After we intercept and\n * record an error, we rethrow it (assuming we've intercepted it before it's reached the top-level global handlers), so\n * that we don't interfere with whatever effects the error might have had were the SDK not there. At that point, because\n * the error has been rethrown, it's possible for it to bubble up to some other code we've instrumented. If it's not\n * caught after that, it will bubble all the way up to the global handlers (which of course we also instrument). This\n * function helps us ensure that even if we encounter the same error more than once, we only record it the first time we\n * see it.\n *\n * Note: It will ignore primitives (always return `false` and not mark them as seen), as properties can't be set on\n * them. {@link: Object.objectify} can be used on exceptions to convert any that are primitives into their equivalent\n * object wrapper forms so that this check will always work. However, because we need to flag the exact object which\n * will get rethrown, and because that rethrowing happens outside of the event processing pipeline, the objectification\n * must be done before the exception captured.\n *\n * @param A thrown exception to check or flag as having been seen\n * @returns `true` if the exception has already been captured, `false` if not (with the side effect of marking it seen)\n */\nexport function checkOrSetAlreadyCaught(exception: unknown): boolean {\n  if (isAlreadyCaptured(exception)) {\n    return true;\n  }\n\n  try {\n    // set it this way rather than by assignment so that it's not ennumerable and therefore isn't recorded by the\n    // `ExtraErrorData` integration\n    addNonEnumerableProperty(exception as { [key: string]: unknown }, '__sentry_captured__', true);\n  } catch (err) {\n    // `exception` is a primitive, so we can't mark it seen\n  }\n\n  return false;\n}\n\nfunction isAlreadyCaptured(exception: unknown): boolean | void {\n  try {\n    return (exception as { __sentry_captured__?: boolean }).__sentry_captured__;\n  } catch {} // eslint-disable-line no-empty\n}\n\n/**\n * Checks whether the given input is already an array, and if it isn't, wraps it in one.\n *\n * @param maybeArray Input to turn into an array, if necessary\n * @returns The input, if already an array, or an array with the input as the only element, if not\n *\n * @deprecated This function has been deprecated and will not be replaced.\n */\nexport function arrayify<T = unknown>(maybeArray: T | T[]): T[] {\n  return Array.isArray(maybeArray) ? maybeArray : [maybeArray];\n}\n"], "names": [], "mappings": ";;;;AAiBA;AACA;AACA;AACA;AACA;AACO,SAAS,KAAK,GAAW;AAChC,EAAE,MAAM,GAAI,GAAE,UAAW;AACzB,EAAE,MAAM,SAAS,GAAG,CAAC,MAAO,IAAG,GAAG,CAAC,QAAQ;;AAE3C,EAAE,IAAI,aAAc,GAAE,MAAc,IAAI,CAAC,MAAM,EAAG,GAAE,EAAE;AACtD,EAAE,IAAI;AACN,IAAI,IAAI,MAAA,IAAU,MAAM,CAAC,UAAU,EAAE;AACrC,MAAM,OAAO,MAAM,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;AAClD;AACA,IAAI,IAAI,MAAA,IAAU,MAAM,CAAC,eAAe,EAAE;AAC1C,MAAM,aAAc,GAAE,MAAM;AAC5B;AACA;AACA;AACA;AACA,QAAQ,MAAM,UAAW,GAAE,IAAI,UAAU,CAAC,CAAC,CAAC;AAC5C,QAAQ,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC;AAC1C;AACA,QAAQ,OAAO,UAAU,CAAC,CAAC,CAAC;AAC5B,OAAO;AACP;AACA,GAAI,CAAA,OAAO,CAAC,EAAE;AACd;AACA;AACA;;AAEA;AACA;AACA,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAE,KAAwB,GAAI,GAAE,MAAM,GAAA,GAAM,IAAI,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAE;AACrF;AACA,IAAI,CAAC,CAAC,CAAA,MAA2B,CAAC,aAAa,EAAG,GAAE,EAAE,MAAM,CAAC,CAAA,KAA0B,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC;AACxG,GAAG;AACH;;AAEA,SAAS,iBAAiB,CAAC,KAAK,EAAgC;AAChE,EAAE,OAAO,KAAK,CAAC,SAAA,IAAa,KAAK,CAAC,SAAS,CAAC,MAAA,GAAS,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAA,GAAI,SAAS;AAC1F;;AAEA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAC,KAAK,EAAiB;AAC1D,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAA,EAAU,GAAE,KAAK;AAC9C,EAAE,IAAI,OAAO,EAAE;AACf,IAAI,OAAO,OAAO;AAClB;;AAEA,EAAE,MAAM,cAAe,GAAE,iBAAiB,CAAC,KAAK,CAAC;AACjD,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,IAAI,cAAc,CAAC,QAAQ,cAAc,CAAC,KAAK,EAAE;AACrD,MAAM,OAAO,CAAC,EAAA,cAAA,CAAA,IAAA,CAAA,EAAA,EAAA,cAAA,CAAA,KAAA,CAAA,CAAA;AACA;AACA,IAAA,OAAA,cAAA,CAAA,IAAA,IAAA,cAAA,CAAA,KAAA,IAAA,OAAA,IAAA,WAAA;AACA;AACA,EAAA,OAAA,OAAA,IAAA,WAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,qBAAA,CAAA,KAAA,EAAA,KAAA,EAAA,IAAA,EAAA;AACA,EAAA,MAAA,SAAA,IAAA,KAAA,CAAA,SAAA,GAAA,KAAA,CAAA,SAAA,IAAA,EAAA,CAAA;AACA,EAAA,MAAA,MAAA,IAAA,SAAA,CAAA,MAAA,GAAA,SAAA,CAAA,MAAA,IAAA,EAAA,CAAA;AACA,EAAA,MAAA,cAAA,IAAA,MAAA,CAAA,CAAA,CAAA,GAAA,MAAA,CAAA,CAAA,CAAA,IAAA,EAAA,CAAA;AACA,EAAA,IAAA,CAAA,cAAA,CAAA,KAAA,EAAA;AACA,IAAA,cAAA,CAAA,KAAA,GAAA,KAAA,IAAA,EAAA;AACA;AACA,EAAA,IAAA,CAAA,cAAA,CAAA,IAAA,EAAA;AACA,IAAA,cAAA,CAAA,IAAA,GAAA,IAAA,IAAA,OAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,qBAAA,CAAA,KAAA,EAAA,YAAA,EAAA;AACA,EAAA,MAAA,cAAA,GAAA,iBAAA,CAAA,KAAA,CAAA;AACA,EAAA,IAAA,CAAA,cAAA,EAAA;AACA,IAAA;AACA;;AAEA,EAAA,MAAA,gBAAA,GAAA,EAAA,IAAA,EAAA,SAAA,EAAA,OAAA,EAAA,IAAA,EAAA;AACA,EAAA,MAAA,gBAAA,GAAA,cAAA,CAAA,SAAA;AACA,EAAA,cAAA,CAAA,SAAA,GAAA,EAAA,GAAA,gBAAA,EAAA,GAAA,gBAAA,EAAA,GAAA,YAAA,EAAA;;AAEA,EAAA,IAAA,YAAA,IAAA,MAAA,IAAA,YAAA,EAAA;AACA,IAAA,MAAA,UAAA,GAAA,EAAA,IAAA,gBAAA,IAAA,gBAAA,CAAA,IAAA,CAAA,EAAA,GAAA,YAAA,CAAA,IAAA,EAAA;AACA,IAAA,cAAA,CAAA,SAAA,CAAA,IAAA,GAAA,UAAA;AACA;AACA;;AAEA;AACA,MAAA,aAAA;AACA,EAAA,qLAAA;;AAEA;AACA;AACA;;AASA,SAAA,SAAA,CAAA,KAAA,EAAA;AACA,EAAA,OAAA,QAAA,CAAA,KAAA,IAAA,EAAA,EAAA,EAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAA,WAAA,CAAA,KAAA,EAAA;AACA,EAAA,MAAA,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA,aAAA,CAAA,IAAA,EAAA;AACA,EAAA,MAAA,KAAA,GAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACA,EAAA,MAAA,KAAA,GAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACA,EAAA,MAAA,KAAA,GAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACA,EAAA,OAAA;AACA,IAAA,aAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AACA,IAAA,KAAA,EAAA,KAAA,CAAA,KAAA,CAAA,GAAA,SAAA,GAAA,KAAA;AACA,IAAA,KAAA,EAAA,KAAA,CAAA,KAAA,CAAA,GAAA,SAAA,GAAA,KAAA;AACA,IAAA,KAAA,EAAA,KAAA,CAAA,KAAA,CAAA,GAAA,SAAA,GAAA,KAAA;AACA,IAAA,UAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AACA,GAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,iBAAA,CAAA,KAAA,EAAA,KAAA,EAAA,cAAA,GAAA,CAAA,EAAA;AACA;AACA,EAAA,IAAA,KAAA,CAAA,MAAA,KAAA,SAAA,EAAA;AACA,IAAA;AACA;;AAEA,EAAA,MAAA,QAAA,GAAA,KAAA,CAAA,MAAA;AACA,EAAA,MAAA,UAAA,GAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,QAAA,GAAA,CAAA,EAAA,KAAA,CAAA,MAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA;;AAEA,EAAA,KAAA,CAAA,WAAA,GAAA;AACA,KAAA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,UAAA,GAAA,cAAA,CAAA,EAAA,UAAA;AACA,KAAA,GAAA,CAAA,CAAA,IAAA,KAAA,QAAA,CAAA,IAAA,EAAA,CAAA,CAAA,CAAA;;AAEA;AACA,EAAA,MAAA,SAAA,GAAA,IAAA,CAAA,GAAA,CAAA,QAAA,GAAA,CAAA,EAAA,UAAA,CAAA;;AAEA;AACA,EAAA,KAAA,CAAA,YAAA,GAAA,QAAA,CAAA,KAAA,CAAA,SAAA,CAAA,EAAA,KAAA,CAAA,KAAA,IAAA,CAAA,CAAA;;AAEA,EAAA,KAAA,CAAA,YAAA,GAAA;AACA,KAAA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA,UAAA,GAAA,CAAA,EAAA,QAAA,CAAA,EAAA,UAAA,GAAA,CAAA,GAAA,cAAA;AACA,KAAA,GAAA,CAAA,CAAA,IAAA,KAAA,QAAA,CAAA,IAAA,EAAA,CAAA,CAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,uBAAA,CAAA,SAAA,EAAA;AACA,EAAA,IAAA,iBAAA,CAAA,SAAA,CAAA,EAAA;AACA,IAAA,OAAA,IAAA;AACA;;AAEA,EAAA,IAAA;AACA;AACA;AACA,IAAA,wBAAA,CAAA,SAAA,GAAA,qBAAA,EAAA,IAAA,CAAA;AACA,GAAA,CAAA,OAAA,GAAA,EAAA;AACA;AACA;;AAEA,EAAA,OAAA,KAAA;AACA;;AAEA,SAAA,iBAAA,CAAA,SAAA,EAAA;AACA,EAAA,IAAA;AACA,IAAA,OAAA,CAAA,SAAA,GAAA,mBAAA;AACA,GAAA,CAAA,OAAA,CAAA,EAAA,EAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,QAAA,CAAA,UAAA,EAAA;AACA,EAAA,OAAA,KAAA,CAAA,OAAA,CAAA,UAAA,CAAA,GAAA,UAAA,GAAA,CAAA,UAAA,CAAA;AACA;;;;"}