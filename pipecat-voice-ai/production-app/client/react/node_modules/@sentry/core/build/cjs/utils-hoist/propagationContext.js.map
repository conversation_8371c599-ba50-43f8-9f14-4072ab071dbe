{"version": 3, "file": "propagationContext.js", "sources": ["../../../src/utils-hoist/propagationContext.ts"], "sourcesContent": ["import type { PropagationContext } from '../types-hoist';\nimport { uuid4 } from './misc';\n\n/**\n * Returns a new minimal propagation context.\n *\n * @deprecated Use `generateTraceId` and `generateSpanId` instead.\n */\nexport function generatePropagationContext(): PropagationContext {\n  return {\n    traceId: generateTraceId(),\n    spanId: generateSpanId(),\n  };\n}\n\n/**\n * Generate a random, valid trace ID.\n */\nexport function generateTraceId(): string {\n  return uuid4();\n}\n\n/**\n * Generate a random, valid span ID.\n */\nexport function generateSpanId(): string {\n  return uuid4().substring(16);\n}\n"], "names": ["uuid4"], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACO,SAAS,0BAA0B,GAAuB;AACjE,EAAE,OAAO;AACT,IAAI,OAAO,EAAE,eAAe,EAAE;AAC9B,IAAI,MAAM,EAAE,cAAc,EAAE;AAC5B,GAAG;AACH;;AAEA;AACA;AACA;AACO,SAAS,eAAe,GAAW;AAC1C,EAAE,OAAOA,UAAK,EAAE;AAChB;;AAEA;AACA;AACA;AACO,SAAS,cAAc,GAAW;AACzC,EAAE,OAAOA,UAAK,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;AAC9B;;;;;;"}