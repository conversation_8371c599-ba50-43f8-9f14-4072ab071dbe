{"version": 3, "file": "options.d.ts", "sourceRoot": "", "sources": ["../../../src/types-hoist/options.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAC/D,OAAO,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,gBAAgB,EAAE,MAAM,SAAS,CAAC;AACvE,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AACjD,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACzD,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,SAAS,CAAC;AAC9C,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AACjD,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC;AACvC,OAAO,KAAK,EAAE,eAAe,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AACjE,OAAO,KAAK,EAAE,uBAAuB,EAAE,MAAM,WAAW,CAAC;AACzD,OAAO,KAAK,EAAE,oBAAoB,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAEnE,MAAM,WAAW,aAAa,CAAC,EAAE,SAAS,oBAAoB,GAAG,oBAAoB;IACnF;;OAEG;IACH,KAAK,CAAC,EAAE,OAAO,CAAC;IAEhB;;;OAGG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB,sEAAsE;IACtE,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAE3B;;;;;;;OAOG;IACH,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAE9B;;OAEG;IACH,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAE5B;;;OAGG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IAEb;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB,uEAAuE;IACvE,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB,2CAA2C;IAC3C,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,YAAY,EAAE,WAAW,EAAE,CAAC;IAE5B;;;OAGG;IACH,SAAS,EAAE,CAAC,gBAAgB,EAAE,EAAE,KAAK,SAAS,CAAC;IAE/C;;;OAGG;IACH,WAAW,EAAE,WAAW,CAAC;IAEzB;;OAEG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;IAE/B;;;;;;;;OAQG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAE1B;;;;;;;;OAQG;IACH,aAAa,CAAC,EAAE,OAAO,CAAC;IAExB;;;;;;;;;;OAUG;IACH,0BAA0B,CAAC,EAAE,OAAO,CAAC;IAErC;;OAEG;IACH,YAAY,CAAC,EAAE,cAAc,CAAC;IAE9B;;;OAGG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;;;;OAKG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB,mFAAmF;IACnF,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;;;;;;;OAQG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;;;;;;;OAQG;IACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAE7B;;;;;;OAMG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IAEzB;;;OAGG;IACH,YAAY,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;IAEtC;;;OAGG;IACH,kBAAkB,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;IAE5C;;;;OAIG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAEhB;;;;;;;;;;;;OAYG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;IAEzB;;;OAGG;IACH,SAAS,CAAC,EAAE,WAAW,CAAC;IAExB;;OAEG;IACH,YAAY,CAAC,EAAE;QACb,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;KACpB,CAAC;IAEF;;;;;;OAMG;IACH,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;IAEnC;;;;;;OAMG;IACH,QAAQ,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;IAElC;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACH,uBAAuB,CAAC,EAAE,uBAAuB,CAAC;IAElD;;;;;;;;;;;OAWG;IACH,aAAa,CAAC,EAAE,CAAC,eAAe,EAAE,eAAe,KAAK,MAAM,GAAG,OAAO,CAAC;IAEvE;;;;;;;;;;OAUG;IACH,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,KAAK,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,UAAU,GAAG,IAAI,CAAC;IAExG;;;;;;;;;;OAUG;IACH,cAAc,CAAC,EAAE,CAAC,IAAI,EAAE,QAAQ,KAAK,QAAQ,GAAG,IAAI,CAAC;IAErD;;;;;;;;;;OAUG;IACH,qBAAqB,CAAC,EAAE,CACtB,KAAK,EAAE,gBAAgB,EACvB,IAAI,EAAE,SAAS,KACZ,WAAW,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,gBAAgB,GAAG,IAAI,CAAC;IAEpE;;;;;;;;;;OAUG;IACH,gBAAgB,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,cAAc,KAAK,UAAU,GAAG,IAAI,CAAC;CACzF;AAED,gDAAgD;AAChD,MAAM,WAAW,OAAO,CAAC,EAAE,SAAS,oBAAoB,GAAG,oBAAoB,CAC7E,SAAQ,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE,cAAc,GAAG,WAAW,GAAG,aAAa,CAAC;IACtF;;;OAGG;IACH,mBAAmB,CAAC,EAAE,KAAK,GAAG,WAAW,EAAE,CAAC;IAE5C;;;;OAIG;IACH,YAAY,CAAC,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC,YAAY,EAAE,WAAW,EAAE,KAAK,WAAW,EAAE,CAAC,CAAC;IAEhF;;;;OAIG;IACH,SAAS,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,KAAK,SAAS,CAAC;IAEhD;;;OAGG;IACH,WAAW,CAAC,EAAE,WAAW,GAAG,eAAe,EAAE,CAAC;CAC/C"}