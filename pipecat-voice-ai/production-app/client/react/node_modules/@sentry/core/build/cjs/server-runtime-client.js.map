{"version": 3, "file": "server-runtime-client.js", "sources": ["../../src/server-runtime-client.ts"], "sourcesContent": ["import type {\n  BaseTransportOptions,\n  CheckIn,\n  ClientOptions,\n  DynamicSamplingContext,\n  Event,\n  EventHint,\n  MonitorConfig,\n  ParameterizedString,\n  SerializedCheckIn,\n  SeverityLevel,\n  TraceContext,\n} from './types-hoist';\n\nimport { BaseClient } from './baseclient';\nimport { createCheckInEnvelope } from './checkin';\nimport { getIsolationScope, getTraceContextFromScope } from './currentScopes';\nimport { DEBUG_BUILD } from './debug-build';\nimport type { Scope } from './scope';\nimport { SessionFlusher } from './sessionflusher';\nimport {\n  getDynamicSamplingContextFromScope,\n  getDynamicSamplingContextFromSpan,\n  registerSpanErrorInstrumentation,\n} from './tracing';\nimport { eventFromMessage, eventFromUnknownInput } from './utils-hoist/eventbuilder';\nimport { logger } from './utils-hoist/logger';\nimport { uuid4 } from './utils-hoist/misc';\nimport { resolvedSyncPromise } from './utils-hoist/syncpromise';\nimport { _getSpanForScope } from './utils/spanOnScope';\nimport { spanToTraceContext } from './utils/spanUtils';\n\nexport interface ServerRuntimeClientOptions extends ClientOptions<BaseTransportOptions> {\n  platform?: string;\n  runtime?: { name: string; version?: string };\n  serverName?: string;\n}\n\n/**\n * The Sentry Server Runtime Client SDK.\n */\nexport class ServerRuntimeClient<\n  O extends ClientOptions & ServerRuntimeClientOptions = ServerRuntimeClientOptions,\n> extends BaseClient<O> {\n  // eslint-disable-next-line deprecation/deprecation\n  protected _sessionFlusher: SessionFlusher | undefined;\n\n  /**\n   * Creates a new Edge SDK instance.\n   * @param options Configuration options for this SDK.\n   */\n  public constructor(options: O) {\n    // Server clients always support tracing\n    registerSpanErrorInstrumentation();\n\n    super(options);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public eventFromException(exception: unknown, hint?: EventHint): PromiseLike<Event> {\n    const event = eventFromUnknownInput(this, this._options.stackParser, exception, hint);\n    event.level = 'error';\n\n    return resolvedSyncPromise(event);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public eventFromMessage(\n    message: ParameterizedString,\n    level: SeverityLevel = 'info',\n    hint?: EventHint,\n  ): PromiseLike<Event> {\n    return resolvedSyncPromise(\n      eventFromMessage(this._options.stackParser, message, level, hint, this._options.attachStacktrace),\n    );\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureException(exception: unknown, hint?: EventHint, scope?: Scope): string {\n    // Check if `_sessionFlusher` exists because it is initialized (defined) only when the `autoSessionTracking` is enabled.\n    // The expectation is that session aggregates are only sent when `autoSessionTracking` is enabled.\n    // TODO(v9): Our goal in the future is to not have the `autoSessionTracking` option and instead rely on integrations doing the creation and sending of sessions. We will not have a central kill-switch for sessions.\n    // TODO(v9): This should move into the httpIntegration.\n    // eslint-disable-next-line deprecation/deprecation\n    if (this._options.autoSessionTracking && this._sessionFlusher) {\n      // eslint-disable-next-line deprecation/deprecation\n      const requestSession = getIsolationScope().getRequestSession();\n\n      // Necessary checks to ensure this is code block is executed only within a request\n      // Should override the status only if `requestSession.status` is `Ok`, which is its initial stage\n      if (requestSession && requestSession.status === 'ok') {\n        requestSession.status = 'errored';\n      }\n    }\n\n    return super.captureException(exception, hint, scope);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureEvent(event: Event, hint?: EventHint, scope?: Scope): string {\n    // Check if `_sessionFlusher` exists because it is initialized only when the `autoSessionTracking` is enabled.\n    // The expectation is that session aggregates are only sent when `autoSessionTracking` is enabled.\n    // TODO(v9): Our goal in the future is to not have the `autoSessionTracking` option and instead rely on integrations doing the creation and sending of sessions. We will not have a central kill-switch for sessions.\n    // TODO(v9): This should move into the httpIntegration.\n    // eslint-disable-next-line deprecation/deprecation\n    if (this._options.autoSessionTracking && this._sessionFlusher) {\n      const eventType = event.type || 'exception';\n      const isException =\n        eventType === 'exception' && event.exception && event.exception.values && event.exception.values.length > 0;\n\n      // If the event is of type Exception, then a request session should be captured\n      if (isException) {\n        // eslint-disable-next-line deprecation/deprecation\n        const requestSession = getIsolationScope().getRequestSession();\n\n        // Ensure that this is happening within the bounds of a request, and make sure not to override\n        // Session Status if Errored / Crashed\n        if (requestSession && requestSession.status === 'ok') {\n          requestSession.status = 'errored';\n        }\n      }\n    }\n\n    return super.captureEvent(event, hint, scope);\n  }\n\n  /**\n   *\n   * @inheritdoc\n   */\n  public close(timeout?: number): PromiseLike<boolean> {\n    if (this._sessionFlusher) {\n      this._sessionFlusher.close();\n    }\n    return super.close(timeout);\n  }\n\n  /**\n   * Initializes an instance of SessionFlusher on the client which will aggregate and periodically flush session data.\n   *\n   * NOTICE: This method will implicitly create an interval that is periodically called.\n   * To clean up this resources, call `.close()` when you no longer intend to use the client.\n   * Not doing so will result in a memory leak.\n   */\n  public initSessionFlusher(): void {\n    const { release, environment } = this._options;\n    if (!release) {\n      DEBUG_BUILD && logger.warn('Cannot initialize an instance of SessionFlusher if no release is provided!');\n    } else {\n      // eslint-disable-next-line deprecation/deprecation\n      this._sessionFlusher = new SessionFlusher(this, {\n        release,\n        environment,\n      });\n    }\n  }\n\n  /**\n   * Create a cron monitor check in and send it to Sentry.\n   *\n   * @param checkIn An object that describes a check in.\n   * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want\n   * to create a monitor automatically when sending a check in.\n   */\n  public captureCheckIn(checkIn: CheckIn, monitorConfig?: MonitorConfig, scope?: Scope): string {\n    const id = 'checkInId' in checkIn && checkIn.checkInId ? checkIn.checkInId : uuid4();\n    if (!this._isEnabled()) {\n      DEBUG_BUILD && logger.warn('SDK not enabled, will not capture checkin.');\n      return id;\n    }\n\n    const options = this.getOptions();\n    const { release, environment, tunnel } = options;\n\n    const serializedCheckIn: SerializedCheckIn = {\n      check_in_id: id,\n      monitor_slug: checkIn.monitorSlug,\n      status: checkIn.status,\n      release,\n      environment,\n    };\n\n    if ('duration' in checkIn) {\n      serializedCheckIn.duration = checkIn.duration;\n    }\n\n    if (monitorConfig) {\n      serializedCheckIn.monitor_config = {\n        schedule: monitorConfig.schedule,\n        checkin_margin: monitorConfig.checkinMargin,\n        max_runtime: monitorConfig.maxRuntime,\n        timezone: monitorConfig.timezone,\n        failure_issue_threshold: monitorConfig.failureIssueThreshold,\n        recovery_threshold: monitorConfig.recoveryThreshold,\n      };\n    }\n\n    const [dynamicSamplingContext, traceContext] = this._getTraceInfoFromScope(scope);\n    if (traceContext) {\n      serializedCheckIn.contexts = {\n        trace: traceContext,\n      };\n    }\n\n    const envelope = createCheckInEnvelope(\n      serializedCheckIn,\n      dynamicSamplingContext,\n      this.getSdkMetadata(),\n      tunnel,\n      this.getDsn(),\n    );\n\n    DEBUG_BUILD && logger.info('Sending checkin:', checkIn.monitorSlug, checkIn.status);\n\n    // sendEnvelope should not throw\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.sendEnvelope(envelope);\n\n    return id;\n  }\n\n  /**\n   * Method responsible for capturing/ending a request session by calling `incrementSessionStatusCount` to increment\n   * appropriate session aggregates bucket\n   *\n   * @deprecated This method should not be used or extended. It's functionality will move into the `httpIntegration` and not be part of any public API.\n   */\n  protected _captureRequestSession(): void {\n    if (!this._sessionFlusher) {\n      DEBUG_BUILD && logger.warn('Discarded request mode session because autoSessionTracking option was disabled');\n    } else {\n      this._sessionFlusher.incrementSessionStatusCount();\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  protected _prepareEvent(\n    event: Event,\n    hint: EventHint,\n    scope?: Scope,\n    isolationScope?: Scope,\n  ): PromiseLike<Event | null> {\n    if (this._options.platform) {\n      event.platform = event.platform || this._options.platform;\n    }\n\n    if (this._options.runtime) {\n      event.contexts = {\n        ...event.contexts,\n        runtime: (event.contexts || {}).runtime || this._options.runtime,\n      };\n    }\n\n    if (this._options.serverName) {\n      event.server_name = event.server_name || this._options.serverName;\n    }\n\n    return super._prepareEvent(event, hint, scope, isolationScope);\n  }\n\n  /** Extract trace information from scope */\n  protected _getTraceInfoFromScope(\n    scope: Scope | undefined,\n  ): [dynamicSamplingContext: Partial<DynamicSamplingContext> | undefined, traceContext: TraceContext | undefined] {\n    if (!scope) {\n      return [undefined, undefined];\n    }\n\n    const span = _getSpanForScope(scope);\n\n    const traceContext = span ? spanToTraceContext(span) : getTraceContextFromScope(scope);\n    const dynamicSamplingContext = span\n      ? getDynamicSamplingContextFromSpan(span)\n      : getDynamicSamplingContextFromScope(this, scope);\n    return [dynamicSamplingContext, traceContext];\n  }\n}\n"], "names": ["BaseClient", "registerSpanErrorInstrumentation", "eventFromUnknownInput", "resolvedSyncPromise", "eventFromMessage", "getIsolationScope", "DEBUG_BUILD", "logger", "<PERSON><PERSON><PERSON><PERSON>", "uuid4", "createCheckInEnvelope", "_getSpanForScope", "spanToTraceContext", "getTraceContextFromScope", "dynamicSamplingContext", "getDynamicSamplingContextFromSpan", "getDynamicSamplingContextFromScope"], "mappings": ";;;;;;;;;;;;;;;;;;AAsCA;AACA;AACA;AACO,MAAM;;AAEb,SAAUA,qBAAU,CAAI;AACxB;;AAGA;AACA;AACA;AACA;AACA,GAAS,WAAW,CAAC,OAAO,EAAK;AACjC;AACA,IAAIC,uCAAgC,EAAE;;AAEtC,IAAI,KAAK,CAAC,OAAO,CAAC;AAClB;;AAEA;AACA;AACA;AACA,GAAS,kBAAkB,CAAC,SAAS,EAAW,IAAI,EAAkC;AACtF,IAAI,MAAM,KAAM,GAAEC,kCAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,SAAS,EAAE,IAAI,CAAC;AACzF,IAAI,KAAK,CAAC,KAAM,GAAE,OAAO;;AAEzB,IAAI,OAAOC,+BAAmB,CAAC,KAAK,CAAC;AACrC;;AAEA;AACA;AACA;AACA,GAAS,gBAAgB;AACzB,IAAI,OAAO;AACX,IAAI,KAAK,GAAkB,MAAM;AACjC,IAAI,IAAI;AACR,IAAwB;AACxB,IAAI,OAAOA,+BAAmB;AAC9B,MAAMC,6BAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;AACvG,KAAK;AACL;;AAEA;AACA;AACA;AACA,GAAS,gBAAgB,CAAC,SAAS,EAAW,IAAI,EAAc,KAAK,EAAkB;AACvF;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAA,IAAuB,IAAI,CAAC,eAAe,EAAE;AACnE;AACA,MAAM,MAAM,iBAAiBC,+BAAiB,EAAE,CAAC,iBAAiB,EAAE;;AAEpE;AACA;AACA,MAAM,IAAI,cAAe,IAAG,cAAc,CAAC,MAAA,KAAW,IAAI,EAAE;AAC5D,QAAQ,cAAc,CAAC,MAAO,GAAE,SAAS;AACzC;AACA;;AAEA,IAAI,OAAO,KAAK,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC;AACzD;;AAEA;AACA;AACA;AACA,GAAS,YAAY,CAAC,KAAK,EAAS,IAAI,EAAc,KAAK,EAAkB;AAC7E;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAA,IAAuB,IAAI,CAAC,eAAe,EAAE;AACnE,MAAM,MAAM,SAAU,GAAE,KAAK,CAAC,IAAA,IAAQ,WAAW;AACjD,MAAM,MAAM,WAAY;AACxB,QAAQ,SAAA,KAAc,WAAY,IAAG,KAAK,CAAC,SAAA,IAAa,KAAK,CAAC,SAAS,CAAC,MAAO,IAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,MAAO,GAAE,CAAC;;AAEnH;AACA,MAAM,IAAI,WAAW,EAAE;AACvB;AACA,QAAQ,MAAM,iBAAiBA,+BAAiB,EAAE,CAAC,iBAAiB,EAAE;;AAEtE;AACA;AACA,QAAQ,IAAI,cAAe,IAAG,cAAc,CAAC,MAAA,KAAW,IAAI,EAAE;AAC9D,UAAU,cAAc,CAAC,MAAO,GAAE,SAAS;AAC3C;AACA;AACA;;AAEA,IAAI,OAAO,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;AACjD;;AAEA;AACA;AACA;AACA;AACA,GAAS,KAAK,CAAC,OAAO,EAAiC;AACvD,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE;AAC9B,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE;AAClC;AACA,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,kBAAkB,GAAS;AACpC,IAAI,MAAM,EAAE,OAAO,EAAE,aAAc,GAAE,IAAI,CAAC,QAAQ;AAClD,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAMC,0BAAeC,aAAM,CAAC,IAAI,CAAC,4EAA4E,CAAC;AAC9G,WAAW;AACX;AACA,MAAM,IAAI,CAAC,eAAgB,GAAE,IAAIC,6BAAc,CAAC,IAAI,EAAE;AACtD,QAAQ,OAAO;AACf,QAAQ,WAAW;AACnB,OAAO,CAAC;AACR;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,cAAc,CAAC,OAAO,EAAW,aAAa,EAAkB,KAAK,EAAkB;AAChG,IAAI,MAAM,EAAG,GAAE,WAAY,IAAG,WAAW,OAAO,CAAC,SAAA,GAAY,OAAO,CAAC,YAAYC,UAAK,EAAE;AACxF,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;AAC5B,MAAMH,0BAAeC,aAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC;AAC9E,MAAM,OAAO,EAAE;AACf;;AAEA,IAAI,MAAM,OAAQ,GAAE,IAAI,CAAC,UAAU,EAAE;AACrC,IAAI,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,MAAA,EAAS,GAAE,OAAO;;AAEpD,IAAI,MAAM,iBAAiB,GAAsB;AACjD,MAAM,WAAW,EAAE,EAAE;AACrB,MAAM,YAAY,EAAE,OAAO,CAAC,WAAW;AACvC,MAAM,MAAM,EAAE,OAAO,CAAC,MAAM;AAC5B,MAAM,OAAO;AACb,MAAM,WAAW;AACjB,KAAK;;AAEL,IAAI,IAAI,UAAW,IAAG,OAAO,EAAE;AAC/B,MAAM,iBAAiB,CAAC,QAAA,GAAW,OAAO,CAAC,QAAQ;AACnD;;AAEA,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,iBAAiB,CAAC,cAAA,GAAiB;AACzC,QAAQ,QAAQ,EAAE,aAAa,CAAC,QAAQ;AACxC,QAAQ,cAAc,EAAE,aAAa,CAAC,aAAa;AACnD,QAAQ,WAAW,EAAE,aAAa,CAAC,UAAU;AAC7C,QAAQ,QAAQ,EAAE,aAAa,CAAC,QAAQ;AACxC,QAAQ,uBAAuB,EAAE,aAAa,CAAC,qBAAqB;AACpE,QAAQ,kBAAkB,EAAE,aAAa,CAAC,iBAAiB;AAC3D,OAAO;AACP;;AAEA,IAAI,MAAM,CAAC,sBAAsB,EAAE,YAAY,CAAA,GAAI,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC;AACrF,IAAI,IAAI,YAAY,EAAE;AACtB,MAAM,iBAAiB,CAAC,QAAA,GAAW;AACnC,QAAQ,KAAK,EAAE,YAAY;AAC3B,OAAO;AACP;;AAEA,IAAI,MAAM,QAAS,GAAEG,6BAAqB;AAC1C,MAAM,iBAAiB;AACvB,MAAM,sBAAsB;AAC5B,MAAM,IAAI,CAAC,cAAc,EAAE;AAC3B,MAAM,MAAM;AACZ,MAAM,IAAI,CAAC,MAAM,EAAE;AACnB,KAAK;;AAEL,IAAIJ,sBAAY,IAAGC,aAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,CAAC;;AAEvF;AACA;AACA,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;;AAE/B,IAAI,OAAO,EAAE;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAY,sBAAsB,GAAS;AAC3C,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;AAC/B,MAAMD,0BAAeC,aAAM,CAAC,IAAI,CAAC,gFAAgF,CAAC;AAClH,WAAW;AACX,MAAM,IAAI,CAAC,eAAe,CAAC,2BAA2B,EAAE;AACxD;AACA;;AAEA;AACA;AACA;AACA,GAAY,aAAa;AACzB,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,KAAK;AACT,IAAI,cAAc;AAClB,IAA+B;AAC/B,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;AAChC,MAAM,KAAK,CAAC,QAAS,GAAE,KAAK,CAAC,QAAS,IAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ;AAC/D;;AAEA,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AAC/B,MAAM,KAAK,CAAC,QAAA,GAAW;AACvB,QAAQ,GAAG,KAAK,CAAC,QAAQ;AACzB,QAAQ,OAAO,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,OAAQ,IAAG,IAAI,CAAC,QAAQ,CAAC,OAAO;AACxE,OAAO;AACP;;AAEA,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;AAClC,MAAM,KAAK,CAAC,WAAY,GAAE,KAAK,CAAC,WAAY,IAAG,IAAI,CAAC,QAAQ,CAAC,UAAU;AACvE;;AAEA,IAAI,OAAO,KAAK,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,cAAc,CAAC;AAClE;;AAEA;AACA,GAAY,sBAAsB;AAClC,IAAI,KAAK;AACT,IAAmH;AACnH,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC;AACnC;;AAEA,IAAI,MAAM,IAAK,GAAEI,4BAAgB,CAAC,KAAK,CAAC;;AAExC,IAAI,MAAM,YAAA,GAAe,IAAA,GAAOC,4BAAkB,CAAC,IAAI,CAAE,GAAEC,sCAAwB,CAAC,KAAK,CAAC;AAC1F,IAAI,MAAMC,2BAAyB;AACnC,QAAQC,wDAAiC,CAAC,IAAI;AAC9C,QAAQC,yDAAkC,CAAC,IAAI,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,CAACF,wBAAsB,EAAE,YAAY,CAAC;AACjD;AACA;;;;"}