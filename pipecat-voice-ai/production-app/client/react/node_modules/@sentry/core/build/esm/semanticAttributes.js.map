{"version": 3, "file": "semanticAttributes.js", "sources": ["../../src/semanticAttributes.ts"], "sourcesContent": ["/**\n * Use this attribute to represent the source of a span.\n * Should be one of: custom, url, route, view, component, task, unknown\n *\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_SOURCE = 'sentry.source';\n\n/**\n * Use this attribute to represent the sample rate used for a span.\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE = 'sentry.sample_rate';\n\n/**\n * Use this attribute to represent the operation of a span.\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_OP = 'sentry.op';\n\n/**\n * Use this attribute to represent the origin of a span.\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN = 'sentry.origin';\n\n/** The reason why an idle span finished. */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_IDLE_SPAN_FINISH_REASON = 'sentry.idle_span_finish_reason';\n\n/** The unit of a measurement, which may be stored as a TimedEvent. */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT = 'sentry.measurement_unit';\n\n/** The value of a measurement, which may be stored as a TimedEvent. */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE = 'sentry.measurement_value';\n\n/**\n * A custom span name set by users guaranteed to be taken over any automatically\n * inferred name. This attribute is removed before the span is sent.\n *\n * @internal only meant for internal SDK usage\n * @hidden\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME = 'sentry.custom_span_name';\n\n/**\n * The id of the profile that this span occurred in.\n */\nexport const SEMANTIC_ATTRIBUTE_PROFILE_ID = 'sentry.profile_id';\n\nexport const SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME = 'sentry.exclusive_time';\n\nexport const SEMANTIC_ATTRIBUTE_CACHE_HIT = 'cache.hit';\n\nexport const SEMANTIC_ATTRIBUTE_CACHE_KEY = 'cache.key';\n\nexport const SEMANTIC_ATTRIBUTE_CACHE_ITEM_SIZE = 'cache.item_size';\n\n/** TODO: Remove these once we update to latest semantic conventions */\nexport const SEMANTIC_ATTRIBUTE_HTTP_REQUEST_METHOD = 'http.request.method';\nexport const SEMANTIC_ATTRIBUTE_URL_FULL = 'url.full';\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACO,MAAM,gCAAiC,GAAE;;AAEhD;AACA;AACA;AACO,MAAM,qCAAsC,GAAE;;AAErD;AACA;AACA;AACO,MAAM,4BAA6B,GAAE;;AAE5C;AACA;AACA;AACO,MAAM,gCAAiC,GAAE;;AAEhD;AACO,MAAM,iDAAkD,GAAE;;AAEjE;AACO,MAAM,0CAA2C,GAAE;;AAE1D;AACO,MAAM,2CAA4C,GAAE;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,0CAA2C,GAAE;;AAE1D;AACA;AACA;AACO,MAAM,6BAA8B,GAAE;;AAEtC,MAAM,iCAAkC,GAAE;;AAE1C,MAAM,4BAA6B,GAAE;;AAErC,MAAM,4BAA6B,GAAE;;AAErC,MAAM,kCAAmC,GAAE;;AAElD;AACO,MAAM,sCAAuC,GAAE;AAC/C,MAAM,2BAA4B,GAAE;;;;"}