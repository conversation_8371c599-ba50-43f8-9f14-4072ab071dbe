{"version": 3, "file": "vercelWaitUntil.js", "sources": ["../../../src/utils-hoist/vercelWaitUntil.ts"], "sourcesContent": ["import { GLOBAL_OBJ } from './worldwide';\n\ninterface VercelRequestContextGlobal {\n  get?(): {\n    waitUntil?: (task: Promise<unknown>) => void;\n  };\n}\n\n/**\n * Function that delays closing of a Vercel lambda until the provided promise is resolved.\n *\n * Vendored from https://www.npmjs.com/package/@vercel/functions\n */\nexport function vercelWaitUntil(task: Promise<unknown>): void {\n  const vercelRequestContextGlobal: VercelRequestContextGlobal | undefined =\n    // @ts-expect-error This is not typed\n    GLOBAL_OBJ[Symbol.for('@vercel/request-context')];\n\n  const ctx =\n    vercelRequestContextGlobal && vercelRequestContextGlobal.get && vercelRequestContextGlobal.get()\n      ? vercelRequestContextGlobal.get()\n      : {};\n\n  if (ctx && ctx.waitUntil) {\n    ctx.waitUntil(task);\n  }\n}\n"], "names": ["GLOBAL_OBJ"], "mappings": ";;;;AAQA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe,CAAC,IAAI,EAA0B;AAC9D,EAAE,MAAM,0BAA0B;AAClC;AACA,IAAIA,oBAAU,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;;AAErD,EAAE,MAAM,GAAI;AACZ,IAAI,0BAAA,IAA8B,0BAA0B,CAAC,OAAO,0BAA0B,CAAC,GAAG;AAClG,QAAQ,0BAA0B,CAAC,GAAG;AACtC,QAAQ,EAAE;;AAEV,EAAE,IAAI,GAAA,IAAO,GAAG,CAAC,SAAS,EAAE;AAC5B,IAAI,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;AACvB;AACA;;;;"}