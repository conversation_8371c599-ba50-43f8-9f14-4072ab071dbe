{"version": 3, "file": "utils.js", "sources": ["../../../src/tracing/utils.ts"], "sourcesContent": ["import type { Span } from '../types-hoist';\nimport type { Scope } from '../types-hoist';\nimport { addNonEnumerableProperty } from '../utils-hoist/object';\n\nconst SCOPE_ON_START_SPAN_FIELD = '_sentryScope';\nconst ISOLATION_SCOPE_ON_START_SPAN_FIELD = '_sentryIsolationScope';\n\ntype SpanWithScopes = Span & {\n  [SCOPE_ON_START_SPAN_FIELD]?: Scope;\n  [ISOLATION_SCOPE_ON_START_SPAN_FIELD]?: Scope;\n};\n\n/** Store the scope & isolation scope for a span, which can the be used when it is finished. */\nexport function setCapturedScopesOnSpan(span: Span | undefined, scope: Scope, isolationScope: Scope): void {\n  if (span) {\n    addNonEnumerableProperty(span, ISOLATION_SCOPE_ON_START_SPAN_FIELD, isolationScope);\n    addNonEnumerableProperty(span, SCOPE_ON_START_SPAN_FIELD, scope);\n  }\n}\n\n/**\n * Grabs the scope and isolation scope off a span that were active when the span was started.\n */\nexport function getCapturedScopesOnSpan(span: Span): { scope?: Scope; isolationScope?: Scope } {\n  return {\n    scope: (span as SpanWithScopes)[SCOPE_ON_START_SPAN_FIELD],\n    isolationScope: (span as SpanWithScopes)[ISOLATION_SCOPE_ON_START_SPAN_FIELD],\n  };\n}\n"], "names": [], "mappings": ";;AAIA,MAAM,yBAAA,GAA4B,cAAc;AAChD,MAAM,mCAAA,GAAsC,uBAAuB;;AAOnE;AACO,SAAS,uBAAuB,CAAC,IAAI,EAAoB,KAAK,EAAS,cAAc,EAAe;AAC3G,EAAE,IAAI,IAAI,EAAE;AACZ,IAAI,wBAAwB,CAAC,IAAI,EAAE,mCAAmC,EAAE,cAAc,CAAC;AACvF,IAAI,wBAAwB,CAAC,IAAI,EAAE,yBAAyB,EAAE,KAAK,CAAC;AACpE;AACA;;AAEA;AACA;AACA;AACO,SAAS,uBAAuB,CAAC,IAAI,EAAmD;AAC/F,EAAE,OAAO;AACT,IAAI,KAAK,EAAE,CAAC,OAAwB,yBAAyB,CAAC;AAC9D,IAAI,cAAc,EAAE,CAAC,OAAwB,mCAAmC,CAAC;AACjF,GAAG;AACH;;;;"}