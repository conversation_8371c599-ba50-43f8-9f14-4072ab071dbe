{"version": 3, "file": "isSentryRequestUrl.js", "sources": ["../../../src/utils/isSentryRequestUrl.ts"], "sourcesContent": ["import type { Client, DsnComponents } from '../types-hoist';\n\n/**\n * Checks whether given url points to Sentry server\n *\n * @param url url to verify\n */\nexport function isSentryRequestUrl(url: string, client: Client | undefined): boolean {\n  const dsn = client && client.getDsn();\n  const tunnel = client && client.getOptions().tunnel;\n  return checkDsn(url, dsn) || checkTunnel(url, tunnel);\n}\n\nfunction checkTunnel(url: string, tunnel: string | undefined): boolean {\n  if (!tunnel) {\n    return false;\n  }\n\n  return removeTrailingSlash(url) === removeTrailingSlash(tunnel);\n}\n\nfunction checkDsn(url: string, dsn: DsnComponents | undefined): boolean {\n  return dsn ? url.includes(dsn.host) : false;\n}\n\nfunction removeTrailingSlash(str: string): string {\n  return str[str.length - 1] === '/' ? str.slice(0, -1) : str;\n}\n"], "names": [], "mappings": ";;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,GAAG,EAAU,MAAM,EAA+B;AACrF,EAAE,MAAM,MAAM,MAAA,IAAU,MAAM,CAAC,MAAM,EAAE;AACvC,EAAE,MAAM,MAAO,GAAE,MAAO,IAAG,MAAM,CAAC,UAAU,EAAE,CAAC,MAAM;AACrD,EAAE,OAAO,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAE,IAAG,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC;AACvD;;AAEA,SAAS,WAAW,CAAC,GAAG,EAAU,MAAM,EAA+B;AACvE,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,OAAO,KAAK;AAChB;;AAEA,EAAE,OAAO,mBAAmB,CAAC,GAAG,MAAM,mBAAmB,CAAC,MAAM,CAAC;AACjE;;AAEA,SAAS,QAAQ,CAAC,GAAG,EAAU,GAAG,EAAsC;AACxE,EAAE,OAAO,GAAA,GAAM,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAE,GAAE,KAAK;AAC7C;;AAEA,SAAS,mBAAmB,CAAC,GAAG,EAAkB;AAClD,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,MAAO,GAAE,CAAC,CAAA,KAAM,GAAA,GAAM,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE,GAAE,GAAG;AAC7D;;;;"}