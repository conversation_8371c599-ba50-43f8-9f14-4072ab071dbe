/**
 * Creates a cache that evicts keys in fifo order
 * @param size {Number}
 *
 * @deprecated This function is deprecated and will be removed in the next major version.
 */
export declare function makeFifoCache<Key extends string, Value>(size: number): {
    get: (key: Key) => Value | undefined;
    add: (key: Key, value: Value) => void;
    delete: (key: Key) => boolean;
    clear: () => void;
    size: () => number;
};
//# sourceMappingURL=cache.d.ts.map
