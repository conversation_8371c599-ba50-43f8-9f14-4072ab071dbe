import { ParameterizedString } from '../types-hoist';
/**
 * Tagged template function which returns parameterized representation of the message
 * For example: parameterize`This is a log statement with ${x} and ${y} params`, would return:
 * "__sentry_template_string__": 'This is a log statement with %s and %s params',
 * "__sentry_template_values__": ['first', 'second']
 * @param strings An array of string values splitted between expressions
 * @param values Expressions extracted from template string
 * @returns String with template information in __sentry_template_string__ and __sentry_template_values__ properties
 */
export declare function parameterize(strings: TemplateStringsArray, ...values: string[]): ParameterizedString;
//# sourceMappingURL=parameterize.d.ts.map
