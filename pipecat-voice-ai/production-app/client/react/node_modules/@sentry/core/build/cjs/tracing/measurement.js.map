{"version": 3, "file": "measurement.js", "sources": ["../../../src/tracing/measurement.ts"], "sourcesContent": ["import { DEBUG_BUILD } from '../debug-build';\nimport {\n  SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT,\n  SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE,\n} from '../semanticAttributes';\nimport type { MeasurementUnit, Measurements, TimedEvent } from '../types-hoist';\nimport { logger } from '../utils-hoist/logger';\nimport { getActiveSpan, getRootSpan } from '../utils/spanUtils';\n\n/**\n * Adds a measurement to the active transaction on the current global scope. You can optionally pass in a different span\n * as the 4th parameter.\n */\nexport function setMeasurement(name: string, value: number, unit: MeasurementUnit, activeSpan = getActiveSpan()): void {\n  const rootSpan = activeSpan && getRootSpan(activeSpan);\n\n  if (rootSpan) {\n    DEBUG_BUILD && logger.log(`[Measurement] Setting measurement on root span: ${name} = ${value} ${unit}`);\n    rootSpan.addEvent(name, {\n      [SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE]: value,\n      [SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT]: unit as string,\n    });\n  }\n}\n\n/**\n * Convert timed events to measurements.\n */\nexport function timedEventsToMeasurements(events: TimedEvent[]): Measurements | undefined {\n  if (!events || events.length === 0) {\n    return undefined;\n  }\n\n  const measurements: Measurements = {};\n  events.forEach(event => {\n    const attributes = event.attributes || {};\n    const unit = attributes[SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT] as MeasurementUnit | undefined;\n    const value = attributes[SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE] as number | undefined;\n\n    if (typeof unit === 'string' && typeof value === 'number') {\n      measurements[event.name] = { value, unit };\n    }\n  });\n\n  return measurements;\n}\n"], "names": ["getActiveSpan", "getRootSpan", "DEBUG_BUILD", "logger", "SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE", "SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT"], "mappings": ";;;;;;;AASA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,IAAI,EAAU,KAAK,EAAU,IAAI,EAAmB,aAAaA,uBAAa,EAAE,EAAQ;AACvH,EAAE,MAAM,WAAW,UAAA,IAAcC,qBAAW,CAAC,UAAU,CAAC;;AAExD,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAIC,0BAAeC,aAAM,CAAC,GAAG,CAAC,CAAC,gDAAgD,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA,CAAA;AACA,IAAA,QAAA,CAAA,QAAA,CAAA,IAAA,EAAA;AACA,MAAA,CAAAC,8DAAA,GAAA,KAAA;AACA,MAAA,CAAAC,6DAAA,GAAA,IAAA;AACA,KAAA,CAAA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAA,yBAAA,CAAA,MAAA,EAAA;AACA,EAAA,IAAA,CAAA,MAAA,IAAA,MAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,IAAA,OAAA,SAAA;AACA;;AAEA,EAAA,MAAA,YAAA,GAAA,EAAA;AACA,EAAA,MAAA,CAAA,OAAA,CAAA,KAAA,IAAA;AACA,IAAA,MAAA,UAAA,GAAA,KAAA,CAAA,UAAA,IAAA,EAAA;AACA,IAAA,MAAA,IAAA,GAAA,UAAA,CAAAA,6DAAA,CAAA;AACA,IAAA,MAAA,KAAA,GAAA,UAAA,CAAAD,8DAAA,CAAA;;AAEA,IAAA,IAAA,OAAA,IAAA,KAAA,QAAA,IAAA,OAAA,KAAA,KAAA,QAAA,EAAA;AACA,MAAA,YAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,EAAA,KAAA,EAAA,IAAA,EAAA;AACA;AACA,GAAA,CAAA;;AAEA,EAAA,OAAA,YAAA;AACA;;;;;"}