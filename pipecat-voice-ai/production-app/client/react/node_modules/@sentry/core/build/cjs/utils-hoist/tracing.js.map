{"version": 3, "file": "tracing.js", "sources": ["../../../src/utils-hoist/tracing.ts"], "sourcesContent": ["import type { PropagationContext, TraceparentData } from '../types-hoist';\n\nimport { baggageHeaderToDynamicSamplingContext } from './baggage';\nimport { generateSpanId, generateTraceId } from './propagationContext';\n\n// eslint-disable-next-line @sentry-internal/sdk/no-regexp-constructor -- RegExp is used for readability here\nexport const TRACEPARENT_REGEXP = new RegExp(\n  '^[ \\\\t]*' + // whitespace\n    '([0-9a-f]{32})?' + // trace_id\n    '-?([0-9a-f]{16})?' + // span_id\n    '-?([01])?' + // sampled\n    '[ \\\\t]*$', // whitespace\n);\n\n/**\n * Extract transaction context data from a `sentry-trace` header.\n *\n * @param traceparent Traceparent string\n *\n * @returns Object containing data from the header, or undefined if traceparent string is malformed\n */\nexport function extractTraceparentData(traceparent?: string): TraceparentData | undefined {\n  if (!traceparent) {\n    return undefined;\n  }\n\n  const matches = traceparent.match(TRACEPARENT_REGEXP);\n  if (!matches) {\n    return undefined;\n  }\n\n  let parentSampled: boolean | undefined;\n  if (matches[3] === '1') {\n    parentSampled = true;\n  } else if (matches[3] === '0') {\n    parentSampled = false;\n  }\n\n  return {\n    traceId: matches[1],\n    parentSampled,\n    parentSpanId: matches[2],\n  };\n}\n\n/**\n * Create a propagation context from incoming headers or\n * creates a minimal new one if the headers are undefined.\n */\nexport function propagationContextFromHeaders(\n  sentryTrace: string | undefined,\n  baggage: string | number | boolean | string[] | null | undefined,\n): PropagationContext {\n  const traceparentData = extractTraceparentData(sentryTrace);\n  const dynamicSamplingContext = baggageHeaderToDynamicSamplingContext(baggage);\n\n  if (!traceparentData || !traceparentData.traceId) {\n    return { traceId: generateTraceId(), spanId: generateSpanId() };\n  }\n\n  const { traceId, parentSpanId, parentSampled } = traceparentData;\n\n  const virtualSpanId = generateSpanId();\n\n  return {\n    traceId,\n    parentSpanId,\n    spanId: virtualSpanId,\n    sampled: parentSampled,\n    dsc: dynamicSamplingContext || {}, // If we have traceparent data but no DSC it means we are not head of trace and we must freeze it\n  };\n}\n\n/**\n * Create sentry-trace header from span context values.\n */\nexport function generateSentryTraceHeader(\n  traceId: string = generateTraceId(),\n  spanId: string = generateSpanId(),\n  sampled?: boolean,\n): string {\n  let sampledString = '';\n  if (sampled !== undefined) {\n    sampledString = sampled ? '-1' : '-0';\n  }\n  return `${traceId}-${spanId}${sampledString}`;\n}\n"], "names": ["baggage", "baggageHeaderToDynamicSamplingContext", "generateTraceId", "generateSpanId"], "mappings": ";;;;;AAKA;AACa,MAAA,kBAAA,GAAqB,IAAI,MAAM;AAC5C,EAAE,UAAW;AACb,IAAI,iBAAkB;AACtB,IAAI,mBAAoB;AACxB,IAAI,WAAY;AAChB,IAAI,UAAU;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,sBAAsB,CAAC,WAAW,EAAwC;AAC1F,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI,OAAO,SAAS;AACpB;;AAEA,EAAE,MAAM,UAAU,WAAW,CAAC,KAAK,CAAC,kBAAkB,CAAC;AACvD,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,SAAS;AACpB;;AAEA,EAAE,IAAI,aAAa;AACnB,EAAE,IAAI,OAAO,CAAC,CAAC,CAAE,KAAI,GAAG,EAAE;AAC1B,IAAI,aAAA,GAAgB,IAAI;AACxB,GAAE,MAAO,IAAI,OAAO,CAAC,CAAC,CAAA,KAAM,GAAG,EAAE;AACjC,IAAI,aAAA,GAAgB,KAAK;AACzB;;AAEA,EAAE,OAAO;AACT,IAAI,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;AACvB,IAAI,aAAa;AACjB,IAAI,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC;AAC5B,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACO,SAAS,6BAA6B;AAC7C,EAAE,WAAW;AACb,EAAEA,SAAO;AACT,EAAsB;AACtB,EAAE,MAAM,eAAgB,GAAE,sBAAsB,CAAC,WAAW,CAAC;AAC7D,EAAE,MAAM,sBAAuB,GAAEC,6CAAqC,CAACD,SAAO,CAAC;;AAE/E,EAAE,IAAI,CAAC,eAAA,IAAmB,CAAC,eAAe,CAAC,OAAO,EAAE;AACpD,IAAI,OAAO,EAAE,OAAO,EAAEE,kCAAe,EAAE,EAAE,MAAM,EAAEC,iCAAc,IAAI;AACnE;;AAEA,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,aAAA,EAAgB,GAAE,eAAe;;AAElE,EAAE,MAAM,aAAA,GAAgBA,iCAAc,EAAE;;AAExC,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,YAAY;AAChB,IAAI,MAAM,EAAE,aAAa;AACzB,IAAI,OAAO,EAAE,aAAa;AAC1B,IAAI,GAAG,EAAE,sBAAuB,IAAG,EAAE;AACrC,GAAG;AACH;;AAEA;AACA;AACA;AACO,SAAS,yBAAyB;AACzC,EAAE,OAAO,GAAWD,kCAAe,EAAE;AACrC,EAAE,MAAM,GAAWC,iCAAc,EAAE;AACnC,EAAE,OAAO;AACT,EAAU;AACV,EAAE,IAAI,aAAc,GAAE,EAAE;AACxB,EAAE,IAAI,OAAQ,KAAI,SAAS,EAAE;AAC7B,IAAI,gBAAgB,OAAA,GAAU,IAAA,GAAO,IAAI;AACzC;AACA,EAAE,OAAO,CAAC,EAAA,OAAA,CAAA,CAAA,EAAA,MAAA,CAAA,EAAA,aAAA,CAAA,CAAA;AACA;;;;;;;"}