export declare const COUNTER_METRIC_TYPE: "c";
export declare const GAUGE_METRIC_TYPE: "g";
export declare const SET_METRIC_TYPE: "s";
export declare const DISTRIBUTION_METRIC_TYPE: "d";
/**
 * This does not match spec in https://develop.sentry.dev/sdk/metrics
 * but was chosen to optimize for the most common case in browser environments.
 */
export declare const DEFAULT_BROWSER_FLUSH_INTERVAL = 5000;
/**
 * SDKs are required to bucket into 10 second intervals (rollup in seconds)
 * which is the current lower bound of metric accuracy.
 */
export declare const DEFAULT_FLUSH_INTERVAL = 10000;
/**
 * The maximum number of metrics that should be stored in memory.
 */
export declare const MAX_WEIGHT = 10000;
//# sourceMappingURL=constants.d.ts.map