/** Options for the InboundFilters integration */
export interface InboundFiltersOptions {
    allowUrls: Array<string | RegExp>;
    denyUrls: Array<string | RegExp>;
    ignoreErrors: Array<string | RegExp>;
    ignoreTransactions: Array<string | RegExp>;
    ignoreInternal: boolean;
    disableErrorDefaults: boolean;
}
export declare const inboundFiltersIntegration: (options?: Partial<InboundFiltersOptions> | undefined) => import("../types-hoist").Integration;
//# sourceMappingURL=inboundfilters.d.ts.map
