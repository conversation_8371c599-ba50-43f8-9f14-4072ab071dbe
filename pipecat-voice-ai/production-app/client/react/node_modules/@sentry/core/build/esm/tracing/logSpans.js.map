{"version": 3, "file": "logSpans.js", "sources": ["../../../src/tracing/logSpans.ts"], "sourcesContent": ["import { DEBUG_BUILD } from '../debug-build';\nimport type { Span } from '../types-hoist';\nimport { logger } from '../utils-hoist/logger';\nimport { getRootSpan, spanIsSampled, spanToJSON } from '../utils/spanUtils';\n\n/**\n * Print a log message for a started span.\n */\nexport function logSpanStart(span: Span): void {\n  if (!DEBUG_BUILD) return;\n\n  const { description = '< unknown name >', op = '< unknown op >', parent_span_id: parentSpanId } = spanToJSON(span);\n  const { spanId } = span.spanContext();\n\n  const sampled = spanIsSampled(span);\n  const rootSpan = getRootSpan(span);\n  const isRootSpan = rootSpan === span;\n\n  const header = `[Tracing] Starting ${sampled ? 'sampled' : 'unsampled'} ${isRootSpan ? 'root ' : ''}span`;\n\n  const infoParts: string[] = [`op: ${op}`, `name: ${description}`, `ID: ${spanId}`];\n\n  if (parentSpanId) {\n    infoParts.push(`parent ID: ${parentSpanId}`);\n  }\n\n  if (!isRootSpan) {\n    const { op, description } = spanToJSON(rootSpan);\n    infoParts.push(`root ID: ${rootSpan.spanContext().spanId}`);\n    if (op) {\n      infoParts.push(`root op: ${op}`);\n    }\n    if (description) {\n      infoParts.push(`root description: ${description}`);\n    }\n  }\n\n  logger.log(`${header}\n  ${infoParts.join('\\n  ')}`);\n}\n\n/**\n * Print a log message for an ended span.\n */\nexport function logSpanEnd(span: Span): void {\n  if (!DEBUG_BUILD) return;\n\n  const { description = '< unknown name >', op = '< unknown op >' } = spanToJSON(span);\n  const { spanId } = span.spanContext();\n  const rootSpan = getRootSpan(span);\n  const isRootSpan = rootSpan === span;\n\n  const msg = `[Tracing] Finishing \"${op}\" ${isRootSpan ? 'root ' : ''}span \"${description}\" with ID ${spanId}`;\n  logger.log(msg);\n}\n"], "names": [], "mappings": ";;;;AAKA;AACA;AACA;AACO,SAAS,YAAY,CAAC,IAAI,EAAc;AAC/C,EAAE,IAAI,CAAC,WAAW,EAAE;;AAEpB,EAAE,MAAM,EAAE,WAAA,GAAc,kBAAkB,EAAE,KAAK,gBAAgB,EAAE,cAAc,EAAE,YAAa,EAAA,GAAI,UAAU,CAAC,IAAI,CAAC;AACpH,EAAE,MAAM,EAAE,MAAO,EAAA,GAAI,IAAI,CAAC,WAAW,EAAE;;AAEvC,EAAE,MAAM,OAAQ,GAAE,aAAa,CAAC,IAAI,CAAC;AACrC,EAAE,MAAM,QAAS,GAAE,WAAW,CAAC,IAAI,CAAC;AACpC,EAAE,MAAM,UAAA,GAAa,QAAA,KAAa,IAAI;;AAEtC,EAAE,MAAM,SAAS,CAAC,mBAAmB,EAAE,OAAA,GAAU,SAAU,GAAE,WAAW,CAAC,CAAC,EAAE,UAAW,GAAE,UAAU,EAAE,CAAC,IAAI,CAAC;;AAE3G,EAAE,MAAM,SAAS,GAAa,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA,EAAA,CAAA,MAAA,EAAA,WAAA,CAAA,CAAA,EAAA,CAAA,IAAA,EAAA,MAAA,CAAA,CAAA,CAAA;;AAEA,EAAA,IAAA,YAAA,EAAA;AACA,IAAA,SAAA,CAAA,IAAA,CAAA,CAAA,WAAA,EAAA,YAAA,CAAA,CAAA,CAAA;AACA;;AAEA,EAAA,IAAA,CAAA,UAAA,EAAA;AACA,IAAA,MAAA,EAAA,EAAA,EAAA,WAAA,EAAA,GAAA,UAAA,CAAA,QAAA,CAAA;AACA,IAAA,SAAA,CAAA,IAAA,CAAA,CAAA,SAAA,EAAA,QAAA,CAAA,WAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AACA,IAAA,IAAA,EAAA,EAAA;AACA,MAAA,SAAA,CAAA,IAAA,CAAA,CAAA,SAAA,EAAA,EAAA,CAAA,CAAA,CAAA;AACA;AACA,IAAA,IAAA,WAAA,EAAA;AACA,MAAA,SAAA,CAAA,IAAA,CAAA,CAAA,kBAAA,EAAA,WAAA,CAAA,CAAA,CAAA;AACA;AACA;;AAEA,EAAA,MAAA,CAAA,GAAA,CAAA,CAAA,EAAA,MAAA;AACA,EAAA,EAAA,SAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA;AACA;;AAEA;AACA;AACA;AACA,SAAA,UAAA,CAAA,IAAA,EAAA;AACA,EAAA,IAAA,CAAA,WAAA,EAAA;;AAEA,EAAA,MAAA,EAAA,WAAA,GAAA,kBAAA,EAAA,EAAA,GAAA,gBAAA,EAAA,GAAA,UAAA,CAAA,IAAA,CAAA;AACA,EAAA,MAAA,EAAA,MAAA,EAAA,GAAA,IAAA,CAAA,WAAA,EAAA;AACA,EAAA,MAAA,QAAA,GAAA,WAAA,CAAA,IAAA,CAAA;AACA,EAAA,MAAA,UAAA,GAAA,QAAA,KAAA,IAAA;;AAEA,EAAA,MAAA,GAAA,GAAA,CAAA,qBAAA,EAAA,EAAA,CAAA,EAAA,EAAA,UAAA,GAAA,OAAA,GAAA,EAAA,CAAA,MAAA,EAAA,WAAA,CAAA,UAAA,EAAA,MAAA,CAAA,CAAA;AACA,EAAA,MAAA,CAAA,GAAA,CAAA,GAAA,CAAA;AACA;;;;"}