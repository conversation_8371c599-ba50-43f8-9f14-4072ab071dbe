{"version": 3, "file": "merge.js", "sources": ["../../../src/utils/merge.ts"], "sourcesContent": ["/**\n * Shallow merge two objects.\n * Does not mutate the passed in objects.\n * Undefined/empty values in the merge object will overwrite existing values.\n *\n * By default, this merges 2 levels deep.\n */\nexport function merge<T>(initialObj: T, mergeObj: T, levels = 2): T {\n  // If the merge value is not an object, or we have no merge levels left,\n  // we just set the value to the merge value\n  if (!mergeObj || typeof mergeObj !== 'object' || levels <= 0) {\n    return mergeObj;\n  }\n\n  // If the merge object is an empty object, and the initial object is not undefined, we return the initial object\n  if (initialObj && mergeObj && Object.keys(mergeObj).length === 0) {\n    return initialObj;\n  }\n\n  // Clone object\n  const output = { ...initialObj };\n\n  // Merge values into output, resursively\n  for (const key in mergeObj) {\n    if (Object.prototype.hasOwnProperty.call(mergeObj, key)) {\n      output[key] = merge(output[key], mergeObj[key], levels - 1);\n    }\n  }\n\n  return output;\n}\n"], "names": [], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,KAAK,CAAI,UAAU,EAAK,QAAQ,EAAK,MAAO,GAAE,CAAC,EAAK;AACpE;AACA;AACA,EAAE,IAAI,CAAC,QAAA,IAAY,OAAO,QAAS,KAAI,QAAS,IAAG,MAAO,IAAG,CAAC,EAAE;AAChE,IAAI,OAAO,QAAQ;AACnB;;AAEA;AACA,EAAE,IAAI,UAAA,IAAc,QAAA,IAAY,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAO,KAAI,CAAC,EAAE;AACpE,IAAI,OAAO,UAAU;AACrB;;AAEA;AACA,EAAE,MAAM,MAAO,GAAE,EAAE,GAAG,YAAY;;AAElC;AACA,EAAE,KAAK,MAAM,GAAI,IAAG,QAAQ,EAAE;AAC9B,IAAI,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;AAC7D,MAAM,MAAM,CAAC,GAAG,CAAA,GAAI,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE,MAAO,GAAE,CAAC,CAAC;AACjE;AACA;;AAEA,EAAE,OAAO,MAAM;AACf;;;;"}