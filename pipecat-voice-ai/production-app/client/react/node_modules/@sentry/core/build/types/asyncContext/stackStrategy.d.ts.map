{"version": 3, "file": "stackStrategy.d.ts", "sourceRoot": "", "sources": ["../../../src/asyncContext/stackStrategy.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,MAAM,EAAE,KAAK,IAAI,cAAc,EAAE,MAAM,gBAAgB,CAAC;AAGtE,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,SAAS,CAAC;AAEpD,UAAU,KAAK;IACb,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,KAAK,EAAE,cAAc,CAAC;CACvB;AAED;;GAEG;AACH,qBAAa,iBAAiB;IAC5B,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAsB;IAC7C,OAAO,CAAC,eAAe,CAAiB;gBAErB,KAAK,CAAC,EAAE,cAAc,EAAE,cAAc,CAAC,EAAE,cAAc;IAoB1E;;OAEG;IACI,SAAS,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,cAAc,KAAK,CAAC,GAAG,CAAC;IA6B9D;;OAEG;IACI,SAAS,CAAC,CAAC,SAAS,MAAM,KAAK,CAAC,GAAG,SAAS;IAInD;;OAEG;IACI,QAAQ,IAAI,cAAc;IAIjC;;OAEG;IACI,iBAAiB,IAAI,cAAc;IAI1C;;OAEG;IACI,WAAW,IAAI,KAAK;IAI3B;;OAEG;IACH,OAAO,CAAC,UAAU;IAUlB;;OAEG;IACH,OAAO,CAAC,SAAS;CAIlB;AA+BD;;GAEG;AACH,wBAAgB,4BAA4B,IAAI,oBAAoB,CAWnE"}