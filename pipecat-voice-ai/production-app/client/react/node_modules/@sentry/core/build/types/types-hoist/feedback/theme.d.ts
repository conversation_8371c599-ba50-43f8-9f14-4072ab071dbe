export interface FeedbackTheme {
    /**
     * Foreground color (i.e. text color)
     */
    foreground?: string;
    /**
     * Background color for surfaces
     */
    background?: string;
    /**
     * Foreground color for accented elements
     */
    accentForeground?: string;
    /**
     * Background color for accented elements
     */
    accentBackground?: string;
    /**
     * Success color
     */
    successColor?: string;
    /**
     * Error color
     */
    errorColor?: string;
    /**
     * Box shadow for floating elements
     */
    boxShadow?: string;
    /**
     * Styles for focused interactive components
     */
    outline?: string;
}
//# sourceMappingURL=theme.d.ts.map