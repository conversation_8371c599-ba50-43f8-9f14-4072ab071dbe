{"version": 3, "file": "spanUtils.d.ts", "sourceRoot": "", "sources": ["../../../src/utils/spanUtils.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AASnD,OAAO,KAAK,EACV,eAAe,EACf,SAAS,EACT,IAAI,EACJ,cAAc,EACd,QAAQ,EAER,UAAU,EACV,aAAa,EACb,YAAY,EACb,MAAM,gBAAgB,CAAC;AASxB,eAAO,MAAM,eAAe,IAAM,CAAC;AACnC,eAAO,MAAM,kBAAkB,IAAM,CAAC;AAKtC;;;;GAIG;AACH,wBAAgB,6BAA6B,CAAC,IAAI,EAAE,IAAI,GAAG,YAAY,CAatE;AAED;;GAEG;AACH,wBAAgB,kBAAkB,CAAC,IAAI,EAAE,IAAI,GAAG,YAAY,CAa3D;AAED;;GAEG;AACH,wBAAgB,iBAAiB,CAAC,IAAI,EAAE,IAAI,GAAG,MAAM,CAIpD;AAED;;GAEG;AACH,wBAAgB,sBAAsB,CAAC,KAAK,EAAE,aAAa,GAAG,SAAS,GAAG,MAAM,CAe/E;AAUD;;GAEG;AAIH,wBAAgB,UAAU,CAAC,IAAI,EAAE,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,CAoCxD;AAOD,+BAA+B;AAC/B,MAAM,WAAW,6BAA8B,SAAQ,IAAI;IACzD,UAAU,EAAE,cAAc,CAAC;IAC3B,SAAS,EAAE,aAAa,CAAC;IACzB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,UAAU,CAAC;IACnB,OAAO,EAAE,aAAa,CAAC;IACvB,YAAY,CAAC,EAAE,MAAM,CAAC;CACvB;AAUD;;;;;GAKG;AACH,wBAAgB,aAAa,CAAC,IAAI,EAAE,IAAI,GAAG,OAAO,CAKjD;AAED,yEAAyE;AACzE,wBAAgB,gBAAgB,CAAC,MAAM,EAAE,UAAU,GAAG,SAAS,GAAG,MAAM,GAAG,SAAS,CAUnF;AAED,QAAA,MAAM,iBAAiB,sBAAsB,CAAC;AAC9C,QAAA,MAAM,eAAe,oBAAoB,CAAC;AAE1C,KAAK,yBAAyB,GAAG,IAAI,GAAG;IACtC,CAAC,iBAAiB,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC;CAC1B,CAAC;AAEF;;GAEG;AACH,wBAAgB,kBAAkB,CAAC,IAAI,EAAE,yBAAyB,EAAE,SAAS,EAAE,IAAI,GAAG,IAAI,CAazF;AAED,kDAAkD;AAClD,wBAAgB,uBAAuB,CAAC,IAAI,EAAE,yBAAyB,EAAE,SAAS,EAAE,IAAI,GAAG,IAAI,CAI9F;AAED;;GAEG;AACH,wBAAgB,kBAAkB,CAAC,IAAI,EAAE,yBAAyB,GAAG,IAAI,EAAE,CAoB1E;AAED;;GAEG;AACH,wBAAgB,WAAW,CAAC,IAAI,EAAE,yBAAyB,GAAG,IAAI,CAEjE;AAED;;GAEG;AACH,wBAAgB,aAAa,IAAI,IAAI,GAAG,SAAS,CAQhD;AAED;;GAEG;AACH,wBAAgB,+BAA+B,CAC7C,UAAU,EAAE,UAAU,EACtB,aAAa,EAAE,MAAM,EACrB,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,eAAe,EACrB,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,EAC/B,SAAS,EAAE,MAAM,GAChB,IAAI,CAKN;AAED;;;;GAIG;AACH,wBAAgB,mBAAmB,IAAI,IAAI,CAU1C;AAED;;;;;;;;;;;;;;;GAeG;AACH,wBAAgB,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,IAAI,CAM7D"}