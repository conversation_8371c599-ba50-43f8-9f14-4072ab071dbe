{"version": 3, "file": "logger.js", "sources": ["../../../src/utils-hoist/logger.ts"], "sourcesContent": ["import type { ConsoleLevel } from '../types-hoist';\n\nimport { DEBUG_BUILD } from './debug-build';\nimport { GLOBAL_OBJ, getGlobalSingleton } from './worldwide';\n\n/** Prefix for logging strings */\nconst PREFIX = 'Sentry Logger ';\n\nexport const CONSOLE_LEVELS: readonly ConsoleLevel[] = [\n  'debug',\n  'info',\n  'warn',\n  'error',\n  'log',\n  'assert',\n  'trace',\n] as const;\n\ntype LoggerMethod = (...args: unknown[]) => void;\ntype LoggerConsoleMethods = Record<ConsoleLevel, LoggerMethod>;\n\n/** This may be mutated by the console instrumentation. */\nexport const originalConsoleMethods: {\n  [key in ConsoleLevel]?: (...args: unknown[]) => void;\n} = {};\n\n/** JSDoc */\nexport interface Logger extends LoggerConsoleMethods {\n  disable(): void;\n  enable(): void;\n  isEnabled(): boolean;\n}\n\n/**\n * Temporarily disable sentry console instrumentations.\n *\n * @param callback The function to run against the original `console` messages\n * @returns The results of the callback\n */\nexport function consoleSandbox<T>(callback: () => T): T {\n  if (!('console' in GLOBAL_OBJ)) {\n    return callback();\n  }\n\n  const console = GLOBAL_OBJ.console as Console;\n  const wrappedFuncs: Partial<LoggerConsoleMethods> = {};\n\n  const wrappedLevels = Object.keys(originalConsoleMethods) as ConsoleLevel[];\n\n  // Restore all wrapped console methods\n  wrappedLevels.forEach(level => {\n    const originalConsoleMethod = originalConsoleMethods[level] as LoggerMethod;\n    wrappedFuncs[level] = console[level] as LoggerMethod | undefined;\n    console[level] = originalConsoleMethod;\n  });\n\n  try {\n    return callback();\n  } finally {\n    // Revert restoration to wrapped state\n    wrappedLevels.forEach(level => {\n      console[level] = wrappedFuncs[level] as LoggerMethod;\n    });\n  }\n}\n\nfunction makeLogger(): Logger {\n  let enabled = false;\n  const logger: Partial<Logger> = {\n    enable: () => {\n      enabled = true;\n    },\n    disable: () => {\n      enabled = false;\n    },\n    isEnabled: () => enabled,\n  };\n\n  if (DEBUG_BUILD) {\n    CONSOLE_LEVELS.forEach(name => {\n      logger[name] = (...args: Parameters<(typeof GLOBAL_OBJ.console)[typeof name]>) => {\n        if (enabled) {\n          consoleSandbox(() => {\n            GLOBAL_OBJ.console[name](`${PREFIX}[${name}]:`, ...args);\n          });\n        }\n      };\n    });\n  } else {\n    CONSOLE_LEVELS.forEach(name => {\n      logger[name] = () => undefined;\n    });\n  }\n\n  return logger as Logger;\n}\n\n/**\n * This is a logger singleton which either logs things or no-ops if logging is not enabled.\n * The logger is a singleton on the carrier, to ensure that a consistent logger is used throughout the SDK.\n */\nexport const logger = getGlobalSingleton('logger', makeLogger);\n"], "names": [], "mappings": ";;;AAKA;AACA,MAAM,MAAA,GAAS,gBAAgB;;AAExB,MAAM,cAAc,GAA4B;AACvD,EAAE,OAAO;AACT,EAAE,MAAM;AACR,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAE,KAAK;AACP,EAAE,QAAQ;AACV,EAAE,OAAO;AACT,CAAE;;AAKF;MACa;;AAEb,GAAI;;AAEJ;;AAOA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAI,QAAQ,EAAc;AACxD,EAAE,IAAI,EAAE,aAAa,UAAU,CAAC,EAAE;AAClC,IAAI,OAAO,QAAQ,EAAE;AACrB;;AAEA,EAAE,MAAM,OAAA,GAAU,UAAU,CAAC,OAAQ;AACrC,EAAE,MAAM,YAAY,GAAkC,EAAE;;AAExD,EAAE,MAAM,gBAAgB,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAE;;AAE5D;AACA,EAAE,aAAa,CAAC,OAAO,CAAC,SAAS;AACjC,IAAI,MAAM,qBAAsB,GAAE,sBAAsB,CAAC,KAAK,CAAE;AAChE,IAAI,YAAY,CAAC,KAAK,CAAA,GAAI,OAAO,CAAC,KAAK,CAAE;AACzC,IAAI,OAAO,CAAC,KAAK,CAAA,GAAI,qBAAqB;AAC1C,GAAG,CAAC;;AAEJ,EAAE,IAAI;AACN,IAAI,OAAO,QAAQ,EAAE;AACrB,YAAY;AACZ;AACA,IAAI,aAAa,CAAC,OAAO,CAAC,SAAS;AACnC,MAAM,OAAO,CAAC,KAAK,CAAA,GAAI,YAAY,CAAC,KAAK,CAAE;AAC3C,KAAK,CAAC;AACN;AACA;;AAEA,SAAS,UAAU,GAAW;AAC9B,EAAE,IAAI,OAAQ,GAAE,KAAK;AACrB,EAAE,MAAM,MAAM,GAAoB;AAClC,IAAI,MAAM,EAAE,MAAM;AAClB,MAAM,OAAA,GAAU,IAAI;AACpB,KAAK;AACL,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,OAAA,GAAU,KAAK;AACrB,KAAK;AACL,IAAI,SAAS,EAAE,MAAM,OAAO;AAC5B,GAAG;;AAEH,EAAE,IAAI,WAAW,EAAE;AACnB,IAAI,cAAc,CAAC,OAAO,CAAC,QAAQ;AACnC,MAAM,MAAM,CAAC,IAAI,CAAA,GAAI,CAAC,GAAG,IAAI,KAA2D;AACxF,QAAQ,IAAI,OAAO,EAAE;AACrB,UAAU,cAAc,CAAC,MAAM;AAC/B,YAAY,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAA,MAAA,CAAA,CAAA,EAAA,IAAA,CAAA,EAAA,CAAA,EAAA,GAAA,IAAA,CAAA;AACA,WAAA,CAAA;AACA;AACA,OAAA;AACA,KAAA,CAAA;AACA,GAAA,MAAA;AACA,IAAA,cAAA,CAAA,OAAA,CAAA,IAAA,IAAA;AACA,MAAA,MAAA,CAAA,IAAA,CAAA,GAAA,MAAA,SAAA;AACA,KAAA,CAAA;AACA;;AAEA,EAAA,OAAA,MAAA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAA,MAAA,GAAA,kBAAA,CAAA,QAAA,EAAA,UAAA;;;;"}