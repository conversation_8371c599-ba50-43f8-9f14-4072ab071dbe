{"version": 3, "file": "scope.d.ts", "sourceRoot": "", "sources": ["../../../src/types-hoist/scope.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC/C,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC/C,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AACvC,OAAO,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,WAAW,CAAC;AACnD,OAAO,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,SAAS,CAAC;AAChD,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AACvD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,SAAS,CAAC;AAC7C,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC;AACxC,OAAO,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACzD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,YAAY,CAAC;AAChD,OAAO,KAAK,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AACnC,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,WAAW,CAAC;AACpD,OAAO,KAAK,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAEnC,aAAa;AACb,MAAM,MAAM,cAAc,GAAG,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK,KAAK,CAAC,CAAC;AAEvF,aAAa;AACb,MAAM,WAAW,YAAY;IAC3B,IAAI,EAAE,IAAI,CAAC;IACX,KAAK,EAAE,aAAa,CAAC;IACrB,KAAK,EAAE,MAAM,CAAC;IACd,QAAQ,EAAE,QAAQ,CAAC;IACnB,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,CAAC;IACnC,WAAW,EAAE,MAAM,EAAE,CAAC;IAEtB,cAAc,EAAE,cAAc,CAAC;IAC/B,kBAAkB,EAAE,kBAAkB,CAAC;CACxC;AAED,MAAM,WAAW,SAAS;IACxB,eAAe,EAAE,cAAc,EAAE,CAAC;IAClC,WAAW,EAAE,UAAU,EAAE,CAAC;IAC1B,IAAI,EAAE,IAAI,CAAC;IACX,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,CAAC;IACnC,KAAK,EAAE,MAAM,CAAC;IACd,QAAQ,EAAE,QAAQ,CAAC;IACnB,WAAW,EAAE,UAAU,EAAE,CAAC;IAC1B,kBAAkB,EAAE,kBAAkB,CAAC;IACvC,qBAAqB,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAA;KAAE,CAAC;IAClD,WAAW,EAAE,MAAM,EAAE,CAAC;IACtB,KAAK,CAAC,EAAE,aAAa,CAAC;IACtB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,IAAI,CAAC,EAAE,IAAI,CAAC;CACb;AAED;;GAEG;AACH,MAAM,WAAW,KAAK;IACpB;;OAEG;IACH,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI,CAAC;IAE5C;;;;OAIG;IACH,SAAS,CAAC,CAAC,SAAS,MAAM,KAAK,CAAC,GAAG,SAAS,CAAC;IAE7C;;;OAGG;IACH,cAAc,CAAC,WAAW,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI,CAAC;IAEtD;;;OAGG;IACH,WAAW,IAAI,MAAM,GAAG,SAAS,CAAC;IAElC;;;OAGG;IACH,gBAAgB,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,GAAG,IAAI,CAAC;IAEzD,2EAA2E;IAC3E,iBAAiB,CAAC,QAAQ,EAAE,cAAc,GAAG,IAAI,CAAC;IAElD,kFAAkF;IAClF,YAAY,IAAI,SAAS,CAAC;IAE1B;;;;OAIG;IACH,OAAO,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;IAEjC;;OAEG;IACH,OAAO,IAAI,IAAI,GAAG,SAAS,CAAC;IAE5B;;;OAGG;IACH,OAAO,CAAC,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,GAAG,IAAI,CAAC;IAElD;;;;;;;OAOG;IACH,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,GAAG,IAAI,CAAC;IAE5C;;;OAGG;IACH,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IAEhC;;;;OAIG;IACH,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC;IAE1C;;;OAGG;IACH,cAAc,CAAC,WAAW,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IAE5C;;;OAGG;IACH,QAAQ,CAAC,KAAK,EAAE,aAAa,GAAG,IAAI,CAAC;IAErC;;;;;;;;;;OAUG;IACH,kBAAkB,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAExC;;;;OAIG;IACH,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;IAExD;;OAEG;IACH,UAAU,IAAI,OAAO,GAAG,SAAS,CAAC;IAElC;;OAEG;IACH,UAAU,CAAC,OAAO,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAEpC;;;;OAIG;IAEH,iBAAiB,IAAI,cAAc,GAAG,SAAS,CAAC;IAEhD;;;;OAIG;IAEH,iBAAiB,CAAC,cAAc,CAAC,EAAE,cAAc,GAAG,IAAI,CAAC;IAEzD;;;;;;OAMG;IACH,MAAM,CAAC,cAAc,CAAC,EAAE,cAAc,GAAG,IAAI,CAAC;IAE9C,0DAA0D;IAC1D,KAAK,IAAI,IAAI,CAAC;IAEd;;;;OAIG;IACH,aAAa,CAAC,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAErE;;OAEG;IACH,iBAAiB,IAAI,UAAU,GAAG,SAAS,CAAC;IAE5C;;OAEG;IACH,gBAAgB,IAAI,IAAI,CAAC;IAEzB;;;OAGG;IACH,aAAa,CAAC,UAAU,EAAE,UAAU,GAAG,IAAI,CAAC;IAE5C;;OAEG;IACH,gBAAgB,IAAI,IAAI,CAAC;IAEzB;;;;OAIG;IACH,wBAAwB,CAAC,OAAO,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAA;KAAE,GAAG,IAAI,CAAC;IAEpE;;OAEG;IACH,qBAAqB,CACnB,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,QAAQ,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC,GACxF,IAAI,CAAC;IAER;;OAEG;IACH,qBAAqB,IAAI,kBAAkB,CAAC;IAE5C;;;;;;OAMG;IACH,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;IAE/D;;;;;;;OAOG;IACH,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;IAEjF;;;;;;OAMG;IACH,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC;IAErD;;OAEG;IACH,KAAK,IAAI,KAAK,CAAC;CAChB"}