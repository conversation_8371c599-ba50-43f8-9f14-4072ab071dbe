import type { InternalBaseTransportOptions, Transport, TransportMakeRequestResponse, TransportRequestExecutor } from '../types-hoist';
import { type PromiseBuffer } from '../utils-hoist/promisebuffer';
export declare const DEFAULT_TRANSPORT_BUFFER_SIZE = 64;
/**
 * Creates an instance of a Sentry `Transport`
 *
 * @param options
 * @param makeRequest
 */
export declare function createTransport(options: InternalBaseTransportOptions, makeRequest: TransportRequestExecutor, buffer?: PromiseBuffer<TransportMakeRequestResponse>): Transport;
//# sourceMappingURL=base.d.ts.map