{"version": 3, "file": "traceData.js", "sources": ["../../../src/utils/traceData.ts"], "sourcesContent": ["import { getAsyncContextStrategy } from '../asyncContext';\nimport { getMainCarrier } from '../carrier';\nimport { getClient, getCurrentScope } from '../currentScopes';\nimport { isEnabled } from '../exports';\nimport { getDynamicSamplingContextFromScope, getDynamicSamplingContextFromSpan } from '../tracing';\nimport type { Scope, SerializedTraceData, Span } from '../types-hoist';\nimport { dynamicSamplingContextToSentryBaggageHeader } from '../utils-hoist/baggage';\nimport { logger } from '../utils-hoist/logger';\nimport { TRACEPARENT_REGEXP, generateSentryTraceHeader } from '../utils-hoist/tracing';\nimport { getActiveSpan, spanToTraceHeader } from './spanUtils';\n\n/**\n * Extracts trace propagation data from the current span or from the client's scope (via transaction or propagation\n * context) and serializes it to `sentry-trace` and `baggage` values to strings. These values can be used to propagate\n * a trace via our tracing Http headers or Html `<meta>` tags.\n *\n * This function also applies some validation to the generated sentry-trace and baggage values to ensure that\n * only valid strings are returned.\n *\n * @returns an object with the tracing data values. The object keys are the name of the tracing key to be used as header\n * or meta tag name.\n */\nexport function getTraceData(options: { span?: Span } = {}): SerializedTraceData {\n  const client = getClient();\n  if (!isEnabled() || !client) {\n    return {};\n  }\n\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n  if (acs.getTraceData) {\n    return acs.getTraceData(options);\n  }\n\n  const scope = getCurrentScope();\n  const span = options.span || getActiveSpan();\n  const sentryTrace = span ? spanToTraceHeader(span) : scopeToTraceHeader(scope);\n  const dsc = span ? getDynamicSamplingContextFromSpan(span) : getDynamicSamplingContextFromScope(client, scope);\n  const baggage = dynamicSamplingContextToSentryBaggageHeader(dsc);\n\n  const isValidSentryTraceHeader = TRACEPARENT_REGEXP.test(sentryTrace);\n  if (!isValidSentryTraceHeader) {\n    logger.warn('Invalid sentry-trace data. Cannot generate trace data');\n    return {};\n  }\n\n  return {\n    'sentry-trace': sentryTrace,\n    baggage,\n  };\n}\n\n/**\n * Get a sentry-trace header value for the given scope.\n */\nfunction scopeToTraceHeader(scope: Scope): string {\n  // TODO(v9): Use generateSpanId() instead of spanId\n  // eslint-disable-next-line deprecation/deprecation\n  const { traceId, sampled, spanId } = scope.getPropagationContext();\n  return generateSentryTraceHeader(traceId, spanId, sampled);\n}\n"], "names": ["getClient", "isEnabled", "carrier", "getMainCarrier", "getAsyncContextStrategy", "getCurrentScope", "getActiveSpan", "spanToTraceHeader", "getDynamicSamplingContextFromSpan", "getDynamicSamplingContextFromScope", "baggage", "dynamicSamplingContextToSentryBaggageHeader", "TRACEPARENT_REGEXP", "logger", "generateSentryTraceHeader"], "mappings": ";;;;;;;;;;;;;;;;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,OAAO,GAAoB,EAAE,EAAuB;AACjF,EAAE,MAAM,MAAA,GAASA,uBAAS,EAAE;AAC5B,EAAE,IAAI,CAACC,mBAAS,EAAG,IAAG,CAAC,MAAM,EAAE;AAC/B,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,MAAMC,SAAA,GAAUC,sBAAc,EAAE;AAClC,EAAE,MAAM,GAAI,GAAEC,6BAAuB,CAACF,SAAO,CAAC;AAC9C,EAAE,IAAI,GAAG,CAAC,YAAY,EAAE;AACxB,IAAI,OAAO,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC;AACpC;;AAEA,EAAE,MAAM,KAAA,GAAQG,6BAAe,EAAE;AACjC,EAAE,MAAM,OAAO,OAAO,CAAC,IAAK,IAAGC,uBAAa,EAAE;AAC9C,EAAE,MAAM,WAAA,GAAc,IAAA,GAAOC,2BAAiB,CAAC,IAAI,CAAE,GAAE,kBAAkB,CAAC,KAAK,CAAC;AAChF,EAAE,MAAM,GAAA,GAAM,IAAA,GAAOC,wDAAiC,CAAC,IAAI,CAAA,GAAIC,yDAAkC,CAAC,MAAM,EAAE,KAAK,CAAC;AAChH,EAAE,MAAMC,SAAQ,GAAEC,mDAA2C,CAAC,GAAG,CAAC;;AAElE,EAAE,MAAM,2BAA2BC,0BAAkB,CAAC,IAAI,CAAC,WAAW,CAAC;AACvE,EAAE,IAAI,CAAC,wBAAwB,EAAE;AACjC,IAAIC,aAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC;AACxE,IAAI,OAAO,EAAE;AACb;;AAEA,EAAE,OAAO;AACT,IAAI,cAAc,EAAE,WAAW;AAC/B,aAAIH,SAAO;AACX,GAAG;AACH;;AAEA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,KAAK,EAAiB;AAClD;AACA;AACA,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAO,EAAA,GAAI,KAAK,CAAC,qBAAqB,EAAE;AACpE,EAAE,OAAOI,iCAAyB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;AAC5D;;;;"}