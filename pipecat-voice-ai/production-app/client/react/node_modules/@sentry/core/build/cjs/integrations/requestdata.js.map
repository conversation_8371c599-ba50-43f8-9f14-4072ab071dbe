{"version": 3, "file": "requestdata.js", "sources": ["../../../src/integrations/requestdata.ts"], "sourcesContent": ["import { defineIntegration } from '../integration';\nimport type { IntegrationFn } from '../types-hoist';\nimport {\n  type AddRequestDataToEventOptions,\n  addNormalizedRequestDataToEvent,\n  addRequestDataToEvent,\n} from '../utils-hoist/requestdata';\n\nexport type RequestDataIntegrationOptions = {\n  /**\n   * Controls what data is pulled from the request and added to the event\n   */\n  include?: {\n    cookies?: boolean;\n    data?: boolean;\n    headers?: boolean;\n    ip?: boolean;\n    query_string?: boolean;\n    url?: boolean;\n    user?:\n      | boolean\n      | {\n          id?: boolean;\n          username?: boolean;\n          email?: boolean;\n        };\n  };\n\n  /**\n   * Whether to identify transactions by parameterized path, parameterized path with method, or handler name.\n   * @deprecated This option does not do anything anymore, and will be removed in v9.\n   */\n  transactionNamingScheme?: 'path' | 'methodPath' | 'handler';\n};\n\nconst DEFAULT_OPTIONS = {\n  include: {\n    cookies: true,\n    data: true,\n    headers: true,\n    ip: false,\n    query_string: true,\n    url: true,\n    user: {\n      id: true,\n      username: true,\n      email: true,\n    },\n  },\n  transactionNamingScheme: 'methodPath' as const,\n};\n\nconst INTEGRATION_NAME = 'RequestData';\n\nconst _requestDataIntegration = ((options: RequestDataIntegrationOptions = {}) => {\n  const _options: Required<RequestDataIntegrationOptions> = {\n    ...DEFAULT_OPTIONS,\n    ...options,\n    include: {\n      ...DEFAULT_OPTIONS.include,\n      ...options.include,\n      user:\n        options.include && typeof options.include.user === 'boolean'\n          ? options.include.user\n          : {\n              ...DEFAULT_OPTIONS.include.user,\n              // Unclear why TS still thinks `options.include.user` could be a boolean at this point\n              ...((options.include || {}).user as Record<string, boolean>),\n            },\n    },\n  };\n\n  return {\n    name: INTEGRATION_NAME,\n    processEvent(event) {\n      // Note: In the long run, most of the logic here should probably move into the request data utility functions. For\n      // the moment it lives here, though, until https://github.com/getsentry/sentry-javascript/issues/5718 is addressed.\n      // (TL;DR: Those functions touch many parts of the repo in many different ways, and need to be cleaned up. Once\n      // that's happened, it will be easier to add this logic in without worrying about unexpected side effects.)\n\n      const { sdkProcessingMetadata = {} } = event;\n      const { request, normalizedRequest } = sdkProcessingMetadata;\n\n      const addRequestDataOptions = convertReqDataIntegrationOptsToAddReqDataOpts(_options);\n\n      // If this is set, it takes precedence over the plain request object\n      if (normalizedRequest) {\n        // Some other data is not available in standard HTTP requests, but can sometimes be augmented by e.g. Express or Next.js\n        const ipAddress = request ? request.ip || (request.socket && request.socket.remoteAddress) : undefined;\n        const user = request ? request.user : undefined;\n\n        addNormalizedRequestDataToEvent(event, normalizedRequest, { ipAddress, user }, addRequestDataOptions);\n        return event;\n      }\n\n      // TODO(v9): Eventually we can remove this fallback branch and only rely on the normalizedRequest above\n      if (!request) {\n        return event;\n      }\n\n      // eslint-disable-next-line deprecation/deprecation\n      return addRequestDataToEvent(event, request, addRequestDataOptions);\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Add data about a request to an event. Primarily for use in Node-based SDKs, but included in `@sentry/core`\n * so it can be used in cross-platform SDKs like `@sentry/nextjs`.\n */\nexport const requestDataIntegration = defineIntegration(_requestDataIntegration);\n\n/** Convert this integration's options to match what `addRequestDataToEvent` expects */\n/** TODO: Can possibly be deleted once https://github.com/getsentry/sentry-javascript/issues/5718 is fixed */\nfunction convertReqDataIntegrationOptsToAddReqDataOpts(\n  integrationOptions: Required<RequestDataIntegrationOptions>,\n): AddRequestDataToEventOptions {\n  const {\n    // eslint-disable-next-line deprecation/deprecation\n    transactionNamingScheme,\n    include: { ip, user, ...requestOptions },\n  } = integrationOptions;\n\n  const requestIncludeKeys: string[] = ['method'];\n  for (const [key, value] of Object.entries(requestOptions)) {\n    if (value) {\n      requestIncludeKeys.push(key);\n    }\n  }\n\n  let addReqDataUserOpt;\n  if (user === undefined) {\n    addReqDataUserOpt = true;\n  } else if (typeof user === 'boolean') {\n    addReqDataUserOpt = user;\n  } else {\n    const userIncludeKeys: string[] = [];\n    for (const [key, value] of Object.entries(user)) {\n      if (value) {\n        userIncludeKeys.push(key);\n      }\n    }\n    addReqDataUserOpt = userIncludeKeys;\n  }\n\n  return {\n    include: {\n      ip,\n      user: addReqDataUserOpt,\n      request: requestIncludeKeys.length !== 0 ? requestIncludeKeys : undefined,\n      transaction: transactionNamingScheme,\n    },\n  };\n}\n"], "names": ["addNormalizedRequestDataToEvent", "addRequestDataToEvent", "defineIntegration"], "mappings": ";;;;;AAmCA,MAAM,kBAAkB;AACxB,EAAE,OAAO,EAAE;AACX,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,EAAE,EAAE,KAAK;AACb,IAAI,YAAY,EAAE,IAAI;AACtB,IAAI,GAAG,EAAE,IAAI;AACb,IAAI,IAAI,EAAE;AACV,MAAM,EAAE,EAAE,IAAI;AACd,MAAM,QAAQ,EAAE,IAAI;AACpB,MAAM,KAAK,EAAE,IAAI;AACjB,KAAK;AACL,GAAG;AACH,EAAE,uBAAuB,EAAE,YAAa;AACxC,CAAC;;AAED,MAAM,gBAAA,GAAmB,aAAa;;AAEtC,MAAM,uBAAA,IAA2B,CAAC,OAAO,GAAkC,EAAE,KAAK;AAClF,EAAE,MAAM,QAAQ,GAA4C;AAC5D,IAAI,GAAG,eAAe;AACtB,IAAI,GAAG,OAAO;AACd,IAAI,OAAO,EAAE;AACb,MAAM,GAAG,eAAe,CAAC,OAAO;AAChC,MAAM,GAAG,OAAO,CAAC,OAAO;AACxB,MAAM,IAAI;AACV,QAAQ,OAAO,CAAC,OAAA,IAAW,OAAO,OAAO,CAAC,OAAO,CAAC,IAAA,KAAS;AAC3D,YAAY,OAAO,CAAC,OAAO,CAAC;AAC5B,YAAY;AACZ,cAAc,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI;AAC7C;AACA,cAAc,IAAI,CAAC,OAAO,CAAC,OAAA,IAAW,EAAE,EAAE,IAAA,EAAgC;AAC1E,aAAa;AACb,KAAK;AACL,GAAG;;AAEH,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,YAAY,CAAC,KAAK,EAAE;AACxB;AACA;AACA;AACA;;AAEA,MAAM,MAAM,EAAE,qBAAsB,GAAE,EAAG,EAAA,GAAI,KAAK;AAClD,MAAM,MAAM,EAAE,OAAO,EAAE,iBAAkB,EAAA,GAAI,qBAAqB;;AAElE,MAAM,MAAM,qBAAsB,GAAE,6CAA6C,CAAC,QAAQ,CAAC;;AAE3F;AACA,MAAM,IAAI,iBAAiB,EAAE;AAC7B;AACA,QAAQ,MAAM,YAAY,OAAA,GAAU,OAAO,CAAC,OAAO,OAAO,CAAC,MAAA,IAAU,OAAO,CAAC,MAAM,CAAC,aAAa,CAAE,GAAE,SAAS;AAC9G,QAAQ,MAAM,OAAO,OAAA,GAAU,OAAO,CAAC,IAAK,GAAE,SAAS;;AAEvD,QAAQA,2CAA+B,CAAC,KAAK,EAAE,iBAAiB,EAAE,EAAE,SAAS,EAAE,IAAA,EAAM,EAAE,qBAAqB,CAAC;AAC7G,QAAQ,OAAO,KAAK;AACpB;;AAEA;AACA,MAAM,IAAI,CAAC,OAAO,EAAE;AACpB,QAAQ,OAAO,KAAK;AACpB;;AAEA;AACA,MAAM,OAAOC,iCAAqB,CAAC,KAAK,EAAE,OAAO,EAAE,qBAAqB,CAAC;AACzE,KAAK;AACL,GAAG;AACH,CAAC,CAAE;;AAEH;AACA;AACA;AACA;MACa,sBAAuB,GAAEC,6BAAiB,CAAC,uBAAuB;;AAE/E;AACA;AACA,SAAS,6CAA6C;AACtD,EAAE,kBAAkB;AACpB,EAAgC;AAChC,EAAE,MAAM;AACR;AACA,IAAI,uBAAuB;AAC3B,IAAI,OAAO,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,cAAA,EAAgB;AAC5C,GAAE,GAAI,kBAAkB;;AAExB,EAAE,MAAM,kBAAkB,GAAa,CAAC,QAAQ,CAAC;AACjD,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAE,IAAG,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;AAC7D,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC;AAClC;AACA;;AAEA,EAAE,IAAI,iBAAiB;AACvB,EAAE,IAAI,IAAK,KAAI,SAAS,EAAE;AAC1B,IAAI,iBAAA,GAAoB,IAAI;AAC5B,GAAE,MAAO,IAAI,OAAO,IAAK,KAAI,SAAS,EAAE;AACxC,IAAI,iBAAA,GAAoB,IAAI;AAC5B,SAAS;AACT,IAAI,MAAM,eAAe,GAAa,EAAE;AACxC,IAAI,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAE,IAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACrD,MAAM,IAAI,KAAK,EAAE;AACjB,QAAQ,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC;AACjC;AACA;AACA,IAAI,iBAAA,GAAoB,eAAe;AACvC;;AAEA,EAAE,OAAO;AACT,IAAI,OAAO,EAAE;AACb,MAAM,EAAE;AACR,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,OAAO,EAAE,kBAAkB,CAAC,MAAA,KAAW,CAAE,GAAE,kBAAmB,GAAE,SAAS;AAC/E,MAAM,WAAW,EAAE,uBAAuB;AAC1C,KAAK;AACL,GAAG;AACH;;;;"}