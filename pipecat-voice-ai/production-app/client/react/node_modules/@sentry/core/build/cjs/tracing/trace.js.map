{"version": 3, "file": "trace.js", "sources": ["../../../src/tracing/trace.ts"], "sourcesContent": ["/* eslint-disable max-lines */\n\nimport type { AsyncContextStrategy } from '../asyncContext/types';\nimport { getMainCarrier } from '../carrier';\nimport type { ClientOptions, Scope, SentrySpanArguments, Span, SpanTimeInput, StartSpanOptions } from '../types-hoist';\n\nimport { getClient, getCurrentScope, getIsolationScope, withScope } from '../currentScopes';\n\nimport { getAsyncContextStrategy } from '../asyncContext';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE, SEMANTIC_ATTRIBUTE_SENTRY_SOURCE } from '../semanticAttributes';\nimport { logger } from '../utils-hoist/logger';\nimport { generateTraceId } from '../utils-hoist/propagationContext';\nimport { propagationContextFromHeaders } from '../utils-hoist/tracing';\nimport { handleCallbackErrors } from '../utils/handleCallbackErrors';\nimport { hasTracingEnabled } from '../utils/hasTracingEnabled';\nimport { _getSpanForScope, _setSpanForScope } from '../utils/spanOnScope';\nimport { addChildSpanToSpan, getRootSpan, spanIsSampled, spanTimeInputToSeconds, spanToJSON } from '../utils/spanUtils';\nimport { freezeDscOnSpan, getDynamicSamplingContextFromSpan } from './dynamicSamplingContext';\nimport { logSpanStart } from './logSpans';\nimport { sampleSpan } from './sampling';\nimport { SentryNonRecordingSpan } from './sentryNonRecordingSpan';\nimport { SentrySpan } from './sentrySpan';\nimport { SPAN_STATUS_ERROR } from './spanstatus';\nimport { setCapturedScopesOnSpan } from './utils';\n\nconst SUPPRESS_TRACING_KEY = '__SENTRY_SUPPRESS_TRACING__';\n\n/**\n * Wraps a function with a transaction/span and finishes the span after the function is done.\n * The created span is the active span and will be used as parent by other spans created inside the function\n * and can be accessed via `Sentry.getActiveSpan()`, as long as the function is executed while the scope is active.\n *\n * If you want to create a span that is not set as active, use {@link startInactiveSpan}.\n *\n * You'll always get a span passed to the callback,\n * it may just be a non-recording span if the span is not sampled or if tracing is disabled.\n */\nexport function startSpan<T>(options: StartSpanOptions, callback: (span: Span) => T): T {\n  const acs = getAcs();\n  if (acs.startSpan) {\n    return acs.startSpan(options, callback);\n  }\n\n  const spanArguments = parseSentrySpanArguments(options);\n  const { forceTransaction, parentSpan: customParentSpan } = options;\n\n  return withScope(options.scope, () => {\n    // If `options.parentSpan` is defined, we want to wrap the callback in `withActiveSpan`\n    const wrapper = getActiveSpanWrapper<T>(customParentSpan);\n\n    return wrapper(() => {\n      const scope = getCurrentScope();\n      const parentSpan = getParentSpan(scope);\n\n      const shouldSkipSpan = options.onlyIfParent && !parentSpan;\n      const activeSpan = shouldSkipSpan\n        ? new SentryNonRecordingSpan()\n        : createChildOrRootSpan({\n            parentSpan,\n            spanArguments,\n            forceTransaction,\n            scope,\n          });\n\n      _setSpanForScope(scope, activeSpan);\n\n      return handleCallbackErrors(\n        () => callback(activeSpan),\n        () => {\n          // Only update the span status if it hasn't been changed yet, and the span is not yet finished\n          const { status } = spanToJSON(activeSpan);\n          if (activeSpan.isRecording() && (!status || status === 'ok')) {\n            activeSpan.setStatus({ code: SPAN_STATUS_ERROR, message: 'internal_error' });\n          }\n        },\n        () => activeSpan.end(),\n      );\n    });\n  });\n}\n\n/**\n * Similar to `Sentry.startSpan`. Wraps a function with a transaction/span, but does not finish the span\n * after the function is done automatically. You'll have to call `span.end()` manually.\n *\n * The created span is the active span and will be used as parent by other spans created inside the function\n * and can be accessed via `Sentry.getActiveSpan()`, as long as the function is executed while the scope is active.\n *\n * You'll always get a span passed to the callback,\n * it may just be a non-recording span if the span is not sampled or if tracing is disabled.\n */\nexport function startSpanManual<T>(options: StartSpanOptions, callback: (span: Span, finish: () => void) => T): T {\n  const acs = getAcs();\n  if (acs.startSpanManual) {\n    return acs.startSpanManual(options, callback);\n  }\n\n  const spanArguments = parseSentrySpanArguments(options);\n  const { forceTransaction, parentSpan: customParentSpan } = options;\n\n  return withScope(options.scope, () => {\n    // If `options.parentSpan` is defined, we want to wrap the callback in `withActiveSpan`\n    const wrapper = getActiveSpanWrapper<T>(customParentSpan);\n\n    return wrapper(() => {\n      const scope = getCurrentScope();\n      const parentSpan = getParentSpan(scope);\n\n      const shouldSkipSpan = options.onlyIfParent && !parentSpan;\n      const activeSpan = shouldSkipSpan\n        ? new SentryNonRecordingSpan()\n        : createChildOrRootSpan({\n            parentSpan,\n            spanArguments,\n            forceTransaction,\n            scope,\n          });\n\n      _setSpanForScope(scope, activeSpan);\n\n      function finishAndSetSpan(): void {\n        activeSpan.end();\n      }\n\n      return handleCallbackErrors(\n        () => callback(activeSpan, finishAndSetSpan),\n        () => {\n          // Only update the span status if it hasn't been changed yet, and the span is not yet finished\n          const { status } = spanToJSON(activeSpan);\n          if (activeSpan.isRecording() && (!status || status === 'ok')) {\n            activeSpan.setStatus({ code: SPAN_STATUS_ERROR, message: 'internal_error' });\n          }\n        },\n      );\n    });\n  });\n}\n\n/**\n * Creates a span. This span is not set as active, so will not get automatic instrumentation spans\n * as children or be able to be accessed via `Sentry.getActiveSpan()`.\n *\n * If you want to create a span that is set as active, use {@link startSpan}.\n *\n * This function will always return a span,\n * it may just be a non-recording span if the span is not sampled or if tracing is disabled.\n */\nexport function startInactiveSpan(options: StartSpanOptions): Span {\n  const acs = getAcs();\n  if (acs.startInactiveSpan) {\n    return acs.startInactiveSpan(options);\n  }\n\n  const spanArguments = parseSentrySpanArguments(options);\n  const { forceTransaction, parentSpan: customParentSpan } = options;\n\n  // If `options.scope` is defined, we use this as as a wrapper,\n  // If `options.parentSpan` is defined, we want to wrap the callback in `withActiveSpan`\n  const wrapper = options.scope\n    ? (callback: () => Span) => withScope(options.scope, callback)\n    : customParentSpan !== undefined\n      ? (callback: () => Span) => withActiveSpan(customParentSpan, callback)\n      : (callback: () => Span) => callback();\n\n  return wrapper(() => {\n    const scope = getCurrentScope();\n    const parentSpan = getParentSpan(scope);\n\n    const shouldSkipSpan = options.onlyIfParent && !parentSpan;\n\n    if (shouldSkipSpan) {\n      return new SentryNonRecordingSpan();\n    }\n\n    return createChildOrRootSpan({\n      parentSpan,\n      spanArguments,\n      forceTransaction,\n      scope,\n    });\n  });\n}\n\n/**\n * Continue a trace from `sentry-trace` and `baggage` values.\n * These values can be obtained from incoming request headers, or in the browser from `<meta name=\"sentry-trace\">`\n * and `<meta name=\"baggage\">` HTML tags.\n *\n * Spans started with `startSpan`, `startSpanManual` and `startInactiveSpan`, within the callback will automatically\n * be attached to the incoming trace.\n */\nexport const continueTrace = <V>(\n  options: {\n    sentryTrace: Parameters<typeof propagationContextFromHeaders>[0];\n    baggage: Parameters<typeof propagationContextFromHeaders>[1];\n  },\n  callback: () => V,\n): V => {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n  if (acs.continueTrace) {\n    return acs.continueTrace(options, callback);\n  }\n\n  const { sentryTrace, baggage } = options;\n\n  return withScope(scope => {\n    const propagationContext = propagationContextFromHeaders(sentryTrace, baggage);\n    scope.setPropagationContext(propagationContext);\n    return callback();\n  });\n};\n\n/**\n * Forks the current scope and sets the provided span as active span in the context of the provided callback. Can be\n * passed `null` to start an entirely new span tree.\n *\n * @param span Spans started in the context of the provided callback will be children of this span. If `null` is passed,\n * spans started within the callback will not be attached to a parent span.\n * @param callback Execution context in which the provided span will be active. Is passed the newly forked scope.\n * @returns the value returned from the provided callback function.\n */\nexport function withActiveSpan<T>(span: Span | null, callback: (scope: Scope) => T): T {\n  const acs = getAcs();\n  if (acs.withActiveSpan) {\n    return acs.withActiveSpan(span, callback);\n  }\n\n  return withScope(scope => {\n    _setSpanForScope(scope, span || undefined);\n    return callback(scope);\n  });\n}\n\n/** Suppress tracing in the given callback, ensuring no spans are generated inside of it. */\nexport function suppressTracing<T>(callback: () => T): T {\n  const acs = getAcs();\n\n  if (acs.suppressTracing) {\n    return acs.suppressTracing(callback);\n  }\n\n  return withScope(scope => {\n    scope.setSDKProcessingMetadata({ [SUPPRESS_TRACING_KEY]: true });\n    return callback();\n  });\n}\n\n/**\n * Starts a new trace for the duration of the provided callback. Spans started within the\n * callback will be part of the new trace instead of a potentially previously started trace.\n *\n * Important: Only use this function if you want to override the default trace lifetime and\n * propagation mechanism of the SDK for the duration and scope of the provided callback.\n * The newly created trace will also be the root of a new distributed trace, for example if\n * you make http requests within the callback.\n * This function might be useful if the operation you want to instrument should not be part\n * of a potentially ongoing trace.\n *\n * Default behavior:\n * - Server-side: A new trace is started for each incoming request.\n * - Browser: A new trace is started for each page our route. Navigating to a new route\n *            or page will automatically create a new trace.\n */\nexport function startNewTrace<T>(callback: () => T): T {\n  return withScope(scope => {\n    scope.setPropagationContext({ traceId: generateTraceId() });\n    DEBUG_BUILD && logger.info(`Starting a new trace with id ${scope.getPropagationContext().traceId}`);\n    return withActiveSpan(null, callback);\n  });\n}\n\nfunction createChildOrRootSpan({\n  parentSpan,\n  spanArguments,\n  forceTransaction,\n  scope,\n}: {\n  parentSpan: SentrySpan | undefined;\n  spanArguments: SentrySpanArguments;\n  forceTransaction?: boolean;\n  scope: Scope;\n}): Span {\n  if (!hasTracingEnabled()) {\n    return new SentryNonRecordingSpan();\n  }\n\n  const isolationScope = getIsolationScope();\n\n  let span: Span;\n  if (parentSpan && !forceTransaction) {\n    span = _startChildSpan(parentSpan, scope, spanArguments);\n    addChildSpanToSpan(parentSpan, span);\n  } else if (parentSpan) {\n    // If we forced a transaction but have a parent span, make sure to continue from the parent span, not the scope\n    const dsc = getDynamicSamplingContextFromSpan(parentSpan);\n    const { traceId, spanId: parentSpanId } = parentSpan.spanContext();\n    const parentSampled = spanIsSampled(parentSpan);\n\n    span = _startRootSpan(\n      {\n        traceId,\n        parentSpanId,\n        ...spanArguments,\n      },\n      scope,\n      parentSampled,\n    );\n\n    freezeDscOnSpan(span, dsc);\n  } else {\n    const {\n      traceId,\n      dsc,\n      parentSpanId,\n      sampled: parentSampled,\n    } = {\n      ...isolationScope.getPropagationContext(),\n      ...scope.getPropagationContext(),\n    };\n\n    span = _startRootSpan(\n      {\n        traceId,\n        parentSpanId,\n        ...spanArguments,\n      },\n      scope,\n      parentSampled,\n    );\n\n    if (dsc) {\n      freezeDscOnSpan(span, dsc);\n    }\n  }\n\n  logSpanStart(span);\n\n  setCapturedScopesOnSpan(span, scope, isolationScope);\n\n  return span;\n}\n\n/**\n * This converts StartSpanOptions to SentrySpanArguments.\n * For the most part (for now) we accept the same options,\n * but some of them need to be transformed.\n */\nfunction parseSentrySpanArguments(options: StartSpanOptions): SentrySpanArguments {\n  const exp = options.experimental || {};\n  const initialCtx: SentrySpanArguments = {\n    isStandalone: exp.standalone,\n    ...options,\n  };\n\n  if (options.startTime) {\n    const ctx: SentrySpanArguments & { startTime?: SpanTimeInput } = { ...initialCtx };\n    ctx.startTimestamp = spanTimeInputToSeconds(options.startTime);\n    delete ctx.startTime;\n    return ctx;\n  }\n\n  return initialCtx;\n}\n\nfunction getAcs(): AsyncContextStrategy {\n  const carrier = getMainCarrier();\n  return getAsyncContextStrategy(carrier);\n}\n\nfunction _startRootSpan(spanArguments: SentrySpanArguments, scope: Scope, parentSampled?: boolean): SentrySpan {\n  const client = getClient();\n  const options: Partial<ClientOptions> = (client && client.getOptions()) || {};\n\n  const { name = '', attributes } = spanArguments;\n  const [sampled, sampleRate] = scope.getScopeData().sdkProcessingMetadata[SUPPRESS_TRACING_KEY]\n    ? [false]\n    : sampleSpan(options, {\n        name,\n        parentSampled,\n        attributes,\n        transactionContext: {\n          name,\n          parentSampled,\n        },\n      });\n\n  const rootSpan = new SentrySpan({\n    ...spanArguments,\n    attributes: {\n      [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'custom',\n      ...spanArguments.attributes,\n    },\n    sampled,\n  });\n  if (sampleRate !== undefined) {\n    rootSpan.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE, sampleRate);\n  }\n\n  if (client) {\n    client.emit('spanStart', rootSpan);\n  }\n\n  return rootSpan;\n}\n\n/**\n * Creates a new `Span` while setting the current `Span.id` as `parentSpanId`.\n * This inherits the sampling decision from the parent span.\n */\nfunction _startChildSpan(parentSpan: Span, scope: Scope, spanArguments: SentrySpanArguments): Span {\n  const { spanId, traceId } = parentSpan.spanContext();\n  const sampled = scope.getScopeData().sdkProcessingMetadata[SUPPRESS_TRACING_KEY] ? false : spanIsSampled(parentSpan);\n\n  const childSpan = sampled\n    ? new SentrySpan({\n        ...spanArguments,\n        parentSpanId: spanId,\n        traceId,\n        sampled,\n      })\n    : new SentryNonRecordingSpan({ traceId });\n\n  addChildSpanToSpan(parentSpan, childSpan);\n\n  const client = getClient();\n  if (client) {\n    client.emit('spanStart', childSpan);\n    // If it has an endTimestamp, it's already ended\n    if (spanArguments.endTimestamp) {\n      client.emit('spanEnd', childSpan);\n    }\n  }\n\n  return childSpan;\n}\n\nfunction getParentSpan(scope: Scope): SentrySpan | undefined {\n  const span = _getSpanForScope(scope) as SentrySpan | undefined;\n\n  if (!span) {\n    return undefined;\n  }\n\n  const client = getClient();\n  const options: Partial<ClientOptions> = client ? client.getOptions() : {};\n  if (options.parentSpanIsAlwaysRootSpan) {\n    return getRootSpan(span) as SentrySpan;\n  }\n\n  return span;\n}\n\nfunction getActiveSpanWrapper<T>(parentSpan: Span | undefined | null): (callback: () => T) => T {\n  return parentSpan !== undefined\n    ? (callback: () => T) => {\n        return withActiveSpan(parentSpan, callback);\n      }\n    : (callback: () => T) => callback();\n}\n"], "names": ["withScope", "getCurrentScope", "SentryNonRecordingSpan", "_setSpanForScope", "handleCallbackErrors", "spanToJSON", "SPAN_STATUS_ERROR", "carrier", "getMainCarrier", "getAsyncContextStrategy", "propagationContextFromHeaders", "generateTraceId", "DEBUG_BUILD", "logger", "hasTracingEnabled", "getIsolationScope", "addChildSpanToSpan", "getDynamicSamplingContextFromSpan", "spanIsSampled", "freezeDscOnSpan", "logSpanStart", "setCapturedScopesOnSpan", "spanTimeInputToSeconds", "getClient", "sampleSpan", "SentrySpan", "SEMANTIC_ATTRIBUTE_SENTRY_SOURCE", "SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE", "_getSpanForScope", "getRootSpan"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AA0BA,MAAM,oBAAA,GAAuB,6BAA6B;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,SAAS,CAAI,OAAO,EAAoB,QAAQ,EAAwB;AACxF,EAAE,MAAM,GAAA,GAAM,MAAM,EAAE;AACtB,EAAE,IAAI,GAAG,CAAC,SAAS,EAAE;AACrB,IAAI,OAAO,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC3C;;AAEA,EAAE,MAAM,aAAc,GAAE,wBAAwB,CAAC,OAAO,CAAC;AACzD,EAAE,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,gBAAA,EAAmB,GAAE,OAAO;;AAEpE,EAAE,OAAOA,uBAAS,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM;AACxC;AACA,IAAI,MAAM,OAAQ,GAAE,oBAAoB,CAAI,gBAAgB,CAAC;;AAE7D,IAAI,OAAO,OAAO,CAAC,MAAM;AACzB,MAAM,MAAM,KAAA,GAAQC,6BAAe,EAAE;AACrC,MAAM,MAAM,UAAW,GAAE,aAAa,CAAC,KAAK,CAAC;;AAE7C,MAAM,MAAM,iBAAiB,OAAO,CAAC,YAAa,IAAG,CAAC,UAAU;AAChE,MAAM,MAAM,aAAa;AACzB,UAAU,IAAIC,6CAAsB;AACpC,UAAU,qBAAqB,CAAC;AAChC,YAAY,UAAU;AACtB,YAAY,aAAa;AACzB,YAAY,gBAAgB;AAC5B,YAAY,KAAK;AACjB,WAAW,CAAC;;AAEZ,MAAMC,4BAAgB,CAAC,KAAK,EAAE,UAAU,CAAC;;AAEzC,MAAM,OAAOC,yCAAoB;AACjC,QAAQ,MAAM,QAAQ,CAAC,UAAU,CAAC;AAClC,QAAQ,MAAM;AACd;AACA,UAAU,MAAM,EAAE,MAAO,EAAA,GAAIC,oBAAU,CAAC,UAAU,CAAC;AACnD,UAAU,IAAI,UAAU,CAAC,WAAW,EAAG,KAAI,CAAC,UAAU,MAAA,KAAW,IAAI,CAAC,EAAE;AACxE,YAAY,UAAU,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEC,4BAAiB,EAAE,OAAO,EAAE,gBAAA,EAAkB,CAAC;AACxF;AACA,SAAS;AACT,QAAQ,MAAM,UAAU,CAAC,GAAG,EAAE;AAC9B,OAAO;AACP,KAAK,CAAC;AACN,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe,CAAI,OAAO,EAAoB,QAAQ,EAA4C;AAClH,EAAE,MAAM,GAAA,GAAM,MAAM,EAAE;AACtB,EAAE,IAAI,GAAG,CAAC,eAAe,EAAE;AAC3B,IAAI,OAAO,GAAG,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC;AACjD;;AAEA,EAAE,MAAM,aAAc,GAAE,wBAAwB,CAAC,OAAO,CAAC;AACzD,EAAE,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,gBAAA,EAAmB,GAAE,OAAO;;AAEpE,EAAE,OAAON,uBAAS,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM;AACxC;AACA,IAAI,MAAM,OAAQ,GAAE,oBAAoB,CAAI,gBAAgB,CAAC;;AAE7D,IAAI,OAAO,OAAO,CAAC,MAAM;AACzB,MAAM,MAAM,KAAA,GAAQC,6BAAe,EAAE;AACrC,MAAM,MAAM,UAAW,GAAE,aAAa,CAAC,KAAK,CAAC;;AAE7C,MAAM,MAAM,iBAAiB,OAAO,CAAC,YAAa,IAAG,CAAC,UAAU;AAChE,MAAM,MAAM,aAAa;AACzB,UAAU,IAAIC,6CAAsB;AACpC,UAAU,qBAAqB,CAAC;AAChC,YAAY,UAAU;AACtB,YAAY,aAAa;AACzB,YAAY,gBAAgB;AAC5B,YAAY,KAAK;AACjB,WAAW,CAAC;;AAEZ,MAAMC,4BAAgB,CAAC,KAAK,EAAE,UAAU,CAAC;;AAEzC,MAAM,SAAS,gBAAgB,GAAS;AACxC,QAAQ,UAAU,CAAC,GAAG,EAAE;AACxB;;AAEA,MAAM,OAAOC,yCAAoB;AACjC,QAAQ,MAAM,QAAQ,CAAC,UAAU,EAAE,gBAAgB,CAAC;AACpD,QAAQ,MAAM;AACd;AACA,UAAU,MAAM,EAAE,MAAO,EAAA,GAAIC,oBAAU,CAAC,UAAU,CAAC;AACnD,UAAU,IAAI,UAAU,CAAC,WAAW,EAAG,KAAI,CAAC,UAAU,MAAA,KAAW,IAAI,CAAC,EAAE;AACxE,YAAY,UAAU,CAAC,SAAS,CAAC,EAAE,IAAI,EAAEC,4BAAiB,EAAE,OAAO,EAAE,gBAAA,EAAkB,CAAC;AACxF;AACA,SAAS;AACT,OAAO;AACP,KAAK,CAAC;AACN,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,OAAO,EAA0B;AACnE,EAAE,MAAM,GAAA,GAAM,MAAM,EAAE;AACtB,EAAE,IAAI,GAAG,CAAC,iBAAiB,EAAE;AAC7B,IAAI,OAAO,GAAG,CAAC,iBAAiB,CAAC,OAAO,CAAC;AACzC;;AAEA,EAAE,MAAM,aAAc,GAAE,wBAAwB,CAAC,OAAO,CAAC;AACzD,EAAE,MAAM,EAAE,gBAAgB,EAAE,UAAU,EAAE,gBAAA,EAAmB,GAAE,OAAO;;AAEpE;AACA;AACA,EAAE,MAAM,OAAA,GAAU,OAAO,CAAC;AAC1B,MAAM,CAAC,QAAQ,KAAiBN,uBAAS,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ;AACjE,MAAM,qBAAqB;AAC3B,QAAQ,CAAC,QAAQ,KAAiB,cAAc,CAAC,gBAAgB,EAAE,QAAQ;AAC3E,QAAQ,CAAC,QAAQ,KAAiB,QAAQ,EAAE;;AAE5C,EAAE,OAAO,OAAO,CAAC,MAAM;AACvB,IAAI,MAAM,KAAA,GAAQC,6BAAe,EAAE;AACnC,IAAI,MAAM,UAAW,GAAE,aAAa,CAAC,KAAK,CAAC;;AAE3C,IAAI,MAAM,iBAAiB,OAAO,CAAC,YAAa,IAAG,CAAC,UAAU;;AAE9D,IAAI,IAAI,cAAc,EAAE;AACxB,MAAM,OAAO,IAAIC,6CAAsB,EAAE;AACzC;;AAEA,IAAI,OAAO,qBAAqB,CAAC;AACjC,MAAM,UAAU;AAChB,MAAM,aAAa;AACnB,MAAM,gBAAgB;AACtB,MAAM,KAAK;AACX,KAAK,CAAC;AACN,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,gBAAgB;AAC7B,EAAE;;AAGA;AACF,EAAE,QAAQ;AACV,KAAQ;AACR,EAAE,MAAMK,SAAA,GAAUC,sBAAc,EAAE;AAClC,EAAE,MAAM,GAAI,GAAEC,6BAAuB,CAACF,SAAO,CAAC;AAC9C,EAAE,IAAI,GAAG,CAAC,aAAa,EAAE;AACzB,IAAI,OAAO,GAAG,CAAC,aAAa,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC/C;;AAEA,EAAE,MAAM,EAAE,WAAW,EAAE,OAAQ,EAAA,GAAI,OAAO;;AAE1C,EAAE,OAAOP,uBAAS,CAAC,KAAA,IAAS;AAC5B,IAAI,MAAM,qBAAqBU,qCAA6B,CAAC,WAAW,EAAE,OAAO,CAAC;AAClF,IAAI,KAAK,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;AACnD,IAAI,OAAO,QAAQ,EAAE;AACrB,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAI,IAAI,EAAe,QAAQ,EAA0B;AACvF,EAAE,MAAM,GAAA,GAAM,MAAM,EAAE;AACtB,EAAE,IAAI,GAAG,CAAC,cAAc,EAAE;AAC1B,IAAI,OAAO,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC;AAC7C;;AAEA,EAAE,OAAOV,uBAAS,CAAC,KAAA,IAAS;AAC5B,IAAIG,4BAAgB,CAAC,KAAK,EAAE,IAAK,IAAG,SAAS,CAAC;AAC9C,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC;AAC1B,GAAG,CAAC;AACJ;;AAEA;AACO,SAAS,eAAe,CAAI,QAAQ,EAAc;AACzD,EAAE,MAAM,GAAA,GAAM,MAAM,EAAE;;AAEtB,EAAE,IAAI,GAAG,CAAC,eAAe,EAAE;AAC3B,IAAI,OAAO,GAAG,CAAC,eAAe,CAAC,QAAQ,CAAC;AACxC;;AAEA,EAAE,OAAOH,uBAAS,CAAC,KAAA,IAAS;AAC5B,IAAI,KAAK,CAAC,wBAAwB,CAAC,EAAE,CAAC,oBAAoB,GAAG,IAAK,EAAC,CAAC;AACpE,IAAI,OAAO,QAAQ,EAAE;AACrB,GAAG,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAI,QAAQ,EAAc;AACvD,EAAE,OAAOA,uBAAS,CAAC,KAAA,IAAS;AAC5B,IAAI,KAAK,CAAC,qBAAqB,CAAC,EAAE,OAAO,EAAEW,kCAAe,EAAG,EAAC,CAAC;AAC/D,IAAIC,0BAAeC,aAAM,CAAC,IAAI,CAAC,CAAC,6BAA6B,EAAE,KAAK,CAAC,qBAAqB,EAAE,CAAC,OAAO,CAAC,CAAA,CAAA;AACA,IAAA,OAAA,cAAA,CAAA,IAAA,EAAA,QAAA,CAAA;AACA,GAAA,CAAA;AACA;;AAEA,SAAA,qBAAA,CAAA;AACA,EAAA,UAAA;AACA,EAAA,aAAA;AACA,EAAA,gBAAA;AACA,EAAA,KAAA;AACA;;AAKA,EAAA;AACA,EAAA,IAAA,CAAAC,mCAAA,EAAA,EAAA;AACA,IAAA,OAAA,IAAAZ,6CAAA,EAAA;AACA;;AAEA,EAAA,MAAA,cAAA,GAAAa,+BAAA,EAAA;;AAEA,EAAA,IAAA,IAAA;AACA,EAAA,IAAA,UAAA,IAAA,CAAA,gBAAA,EAAA;AACA,IAAA,IAAA,GAAA,eAAA,CAAA,UAAA,EAAA,KAAA,EAAA,aAAA,CAAA;AACA,IAAAC,4BAAA,CAAA,UAAA,EAAA,IAAA,CAAA;AACA,GAAA,MAAA,IAAA,UAAA,EAAA;AACA;AACA,IAAA,MAAA,GAAA,GAAAC,wDAAA,CAAA,UAAA,CAAA;AACA,IAAA,MAAA,EAAA,OAAA,EAAA,MAAA,EAAA,YAAA,EAAA,GAAA,UAAA,CAAA,WAAA,EAAA;AACA,IAAA,MAAA,aAAA,GAAAC,uBAAA,CAAA,UAAA,CAAA;;AAEA,IAAA,IAAA,GAAA,cAAA;AACA,MAAA;AACA,QAAA,OAAA;AACA,QAAA,YAAA;AACA,QAAA,GAAA,aAAA;AACA,OAAA;AACA,MAAA,KAAA;AACA,MAAA,aAAA;AACA,KAAA;;AAEA,IAAAC,sCAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,GAAA,MAAA;AACA,IAAA,MAAA;AACA,MAAA,OAAA;AACA,MAAA,GAAA;AACA,MAAA,YAAA;AACA,MAAA,OAAA,EAAA,aAAA;AACA,KAAA,GAAA;AACA,MAAA,GAAA,cAAA,CAAA,qBAAA,EAAA;AACA,MAAA,GAAA,KAAA,CAAA,qBAAA,EAAA;AACA,KAAA;;AAEA,IAAA,IAAA,GAAA,cAAA;AACA,MAAA;AACA,QAAA,OAAA;AACA,QAAA,YAAA;AACA,QAAA,GAAA,aAAA;AACA,OAAA;AACA,MAAA,KAAA;AACA,MAAA,aAAA;AACA,KAAA;;AAEA,IAAA,IAAA,GAAA,EAAA;AACA,MAAAA,sCAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA;AACA;;AAEA,EAAAC,qBAAA,CAAA,IAAA,CAAA;;AAEA,EAAAC,6BAAA,CAAA,IAAA,EAAA,KAAA,EAAA,cAAA,CAAA;;AAEA,EAAA,OAAA,IAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAA,wBAAA,CAAA,OAAA,EAAA;AACA,EAAA,MAAA,GAAA,GAAA,OAAA,CAAA,YAAA,IAAA,EAAA;AACA,EAAA,MAAA,UAAA,GAAA;AACA,IAAA,YAAA,EAAA,GAAA,CAAA,UAAA;AACA,IAAA,GAAA,OAAA;AACA,GAAA;;AAEA,EAAA,IAAA,OAAA,CAAA,SAAA,EAAA;AACA,IAAA,MAAA,GAAA,GAAA,EAAA,GAAA,UAAA,EAAA;AACA,IAAA,GAAA,CAAA,cAAA,GAAAC,gCAAA,CAAA,OAAA,CAAA,SAAA,CAAA;AACA,IAAA,OAAA,GAAA,CAAA,SAAA;AACA,IAAA,OAAA,GAAA;AACA;;AAEA,EAAA,OAAA,UAAA;AACA;;AAEA,SAAA,MAAA,GAAA;AACA,EAAA,MAAAf,SAAA,GAAAC,sBAAA,EAAA;AACA,EAAA,OAAAC,6BAAA,CAAAF,SAAA,CAAA;AACA;;AAEA,SAAA,cAAA,CAAA,aAAA,EAAA,KAAA,EAAA,aAAA,EAAA;AACA,EAAA,MAAA,MAAA,GAAAgB,uBAAA,EAAA;AACA,EAAA,MAAA,OAAA,GAAA,CAAA,MAAA,IAAA,MAAA,CAAA,UAAA,EAAA,KAAA,EAAA;;AAEA,EAAA,MAAA,EAAA,IAAA,GAAA,EAAA,EAAA,UAAA,EAAA,GAAA,aAAA;AACA,EAAA,MAAA,CAAA,OAAA,EAAA,UAAA,CAAA,GAAA,KAAA,CAAA,YAAA,EAAA,CAAA,qBAAA,CAAA,oBAAA;AACA,MAAA,CAAA,KAAA;AACA,MAAAC,mBAAA,CAAA,OAAA,EAAA;AACA,QAAA,IAAA;AACA,QAAA,aAAA;AACA,QAAA,UAAA;AACA,QAAA,kBAAA,EAAA;AACA,UAAA,IAAA;AACA,UAAA,aAAA;AACA,SAAA;AACA,OAAA,CAAA;;AAEA,EAAA,MAAA,QAAA,GAAA,IAAAC,qBAAA,CAAA;AACA,IAAA,GAAA,aAAA;AACA,IAAA,UAAA,EAAA;AACA,MAAA,CAAAC,mDAAA,GAAA,QAAA;AACA,MAAA,GAAA,aAAA,CAAA,UAAA;AACA,KAAA;AACA,IAAA,OAAA;AACA,GAAA,CAAA;AACA,EAAA,IAAA,UAAA,KAAA,SAAA,EAAA;AACA,IAAA,QAAA,CAAA,YAAA,CAAAC,wDAAA,EAAA,UAAA,CAAA;AACA;;AAEA,EAAA,IAAA,MAAA,EAAA;AACA,IAAA,MAAA,CAAA,IAAA,CAAA,WAAA,EAAA,QAAA,CAAA;AACA;;AAEA,EAAA,OAAA,QAAA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAA,eAAA,CAAA,UAAA,EAAA,KAAA,EAAA,aAAA,EAAA;AACA,EAAA,MAAA,EAAA,MAAA,EAAA,OAAA,EAAA,GAAA,UAAA,CAAA,WAAA,EAAA;AACA,EAAA,MAAA,OAAA,GAAA,KAAA,CAAA,YAAA,EAAA,CAAA,qBAAA,CAAA,oBAAA,CAAA,GAAA,KAAA,GAAAT,uBAAA,CAAA,UAAA,CAAA;;AAEA,EAAA,MAAA,SAAA,GAAA;AACA,MAAA,IAAAO,qBAAA,CAAA;AACA,QAAA,GAAA,aAAA;AACA,QAAA,YAAA,EAAA,MAAA;AACA,QAAA,OAAA;AACA,QAAA,OAAA;AACA,OAAA;AACA,MAAA,IAAAvB,6CAAA,CAAA,EAAA,OAAA,EAAA,CAAA;;AAEA,EAAAc,4BAAA,CAAA,UAAA,EAAA,SAAA,CAAA;;AAEA,EAAA,MAAA,MAAA,GAAAO,uBAAA,EAAA;AACA,EAAA,IAAA,MAAA,EAAA;AACA,IAAA,MAAA,CAAA,IAAA,CAAA,WAAA,EAAA,SAAA,CAAA;AACA;AACA,IAAA,IAAA,aAAA,CAAA,YAAA,EAAA;AACA,MAAA,MAAA,CAAA,IAAA,CAAA,SAAA,EAAA,SAAA,CAAA;AACA;AACA;;AAEA,EAAA,OAAA,SAAA;AACA;;AAEA,SAAA,aAAA,CAAA,KAAA,EAAA;AACA,EAAA,MAAA,IAAA,GAAAK,4BAAA,CAAA,KAAA,CAAA;;AAEA,EAAA,IAAA,CAAA,IAAA,EAAA;AACA,IAAA,OAAA,SAAA;AACA;;AAEA,EAAA,MAAA,MAAA,GAAAL,uBAAA,EAAA;AACA,EAAA,MAAA,OAAA,GAAA,MAAA,GAAA,MAAA,CAAA,UAAA,EAAA,GAAA,EAAA;AACA,EAAA,IAAA,OAAA,CAAA,0BAAA,EAAA;AACA,IAAA,OAAAM,qBAAA,CAAA,IAAA,CAAA;AACA;;AAEA,EAAA,OAAA,IAAA;AACA;;AAEA,SAAA,oBAAA,CAAA,UAAA,EAAA;AACA,EAAA,OAAA,UAAA,KAAA;AACA,MAAA,CAAA,QAAA,KAAA;AACA,QAAA,OAAA,cAAA,CAAA,UAAA,EAAA,QAAA,CAAA;AACA;AACA,MAAA,CAAA,QAAA,KAAA,QAAA,EAAA;AACA;;;;;;;;;;"}