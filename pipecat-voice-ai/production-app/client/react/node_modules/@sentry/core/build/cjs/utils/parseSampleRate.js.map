{"version": 3, "file": "parseSampleRate.js", "sources": ["../../../src/utils/parseSampleRate.ts"], "sourcesContent": ["import { DEBUG_BUILD } from '../debug-build';\nimport { logger } from '../utils-hoist/logger';\n\n/**\n * Parse a sample rate from a given value.\n * This will either return a boolean or number sample rate, if the sample rate is valid (between 0 and 1).\n * If a string is passed, we try to convert it to a number.\n *\n * Any invalid sample rate will return `undefined`.\n */\nexport function parseSampleRate(sampleRate: unknown): number | undefined {\n  if (typeof sampleRate === 'boolean') {\n    return Number(sampleRate);\n  }\n\n  const rate = typeof sampleRate === 'string' ? parseFloat(sampleRate) : sampleRate;\n  if (typeof rate !== 'number' || isNaN(rate) || rate < 0 || rate > 1) {\n    DEBUG_BUILD &&\n      logger.warn(\n        `[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(\n          sampleRate,\n        )} of type ${JSON.stringify(typeof sampleRate)}.`,\n      );\n    return undefined;\n  }\n\n  return rate;\n}\n"], "names": ["DEBUG_BUILD", "logger"], "mappings": ";;;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe,CAAC,UAAU,EAA+B;AACzE,EAAE,IAAI,OAAO,UAAW,KAAI,SAAS,EAAE;AACvC,IAAI,OAAO,MAAM,CAAC,UAAU,CAAC;AAC7B;;AAEA,EAAE,MAAM,IAAA,GAAO,OAAO,UAAW,KAAI,QAAS,GAAE,UAAU,CAAC,UAAU,CAAA,GAAI,UAAU;AACnF,EAAE,IAAI,OAAO,SAAS,QAAA,IAAY,KAAK,CAAC,IAAI,CAAE,IAAG,OAAO,CAAA,IAAK,IAAK,GAAE,CAAC,EAAE;AACvE,IAAIA,sBAAY;AAChB,MAAMC,aAAM,CAAC,IAAI;AACjB,QAAQ,CAAC,uGAAuG,EAAE,IAAI,CAAC,SAAS;AAChI,UAAU,UAAU;AACpB,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC;AACzD,OAAO;AACP,IAAI,OAAO,SAAS;AACpB;;AAEA,EAAE,OAAO,IAAI;AACb;;;;"}