{"version": 3, "file": "metadata.js", "sources": ["../../../src/integrations/metadata.ts"], "sourcesContent": ["import { defineIntegration } from '../integration';\nimport type { EventItem } from '../types-hoist';\n\nimport { addMetadataToStackFrames, stripMetadataFromStackFrames } from '../metadata';\nimport { forEachEnvelopeItem } from '../utils-hoist/envelope';\n\n/**\n * Adds module metadata to stack frames.\n *\n * Metadata can be injected by the Sentry bundler plugins using the `moduleMetadata` config option.\n *\n * When this integration is added, the metadata passed to the bundler plugin is added to the stack frames of all events\n * under the `module_metadata` property. This can be used to help in tagging or routing of events from different teams\n * our sources\n */\nexport const moduleMetadataIntegration = defineIntegration(() => {\n  return {\n    name: 'ModuleMetadata',\n    setup(client) {\n      // We need to strip metadata from stack frames before sending them to Sentry since these are client side only.\n      client.on('beforeEnvelope', envelope => {\n        forEachEnvelopeItem(envelope, (item, type) => {\n          if (type === 'event') {\n            const event = Array.isArray(item) ? (item as EventItem)[1] : undefined;\n\n            if (event) {\n              stripMetadataFromStackFrames(event);\n              item[1] = event;\n            }\n          }\n        });\n      });\n\n      client.on('applyFrameMetadata', event => {\n        // Only apply stack frame metadata to error events\n        if (event.type) {\n          return;\n        }\n\n        const stackParser = client.getOptions().stackParser;\n        addMetadataToStackFrames(stackParser, event);\n      });\n    },\n  };\n});\n"], "names": ["defineIntegration", "envelope", "forEachEnvelopeItem", "stripMetadataFromStackFrames", "addMetadataToStackFrames"], "mappings": ";;;;;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACa,yBAA0B,GAAEA,6BAAiB,CAAC,MAAM;AACjE,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,KAAK,CAAC,MAAM,EAAE;AAClB;AACA,MAAM,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAEC,cAAY;AAC9C,QAAQC,4BAAmB,CAACD,UAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK;AACtD,UAAU,IAAI,IAAK,KAAI,OAAO,EAAE;AAChC,YAAY,MAAM,KAAM,GAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAE,GAAE,CAAC,IAAK,GAAc,CAAC,CAAA,GAAI,SAAS;;AAElF,YAAY,IAAI,KAAK,EAAE;AACvB,cAAcE,qCAA4B,CAAC,KAAK,CAAC;AACjD,cAAc,IAAI,CAAC,CAAC,CAAA,GAAI,KAAK;AAC7B;AACA;AACA,SAAS,CAAC;AACV,OAAO,CAAC;;AAER,MAAM,MAAM,CAAC,EAAE,CAAC,oBAAoB,EAAE,SAAS;AAC/C;AACA,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE;AACxB,UAAU;AACV;;AAEA,QAAQ,MAAM,cAAc,MAAM,CAAC,UAAU,EAAE,CAAC,WAAW;AAC3D,QAAQC,iCAAwB,CAAC,WAAW,EAAE,KAAK,CAAC;AACpD,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH,CAAC;;;;"}