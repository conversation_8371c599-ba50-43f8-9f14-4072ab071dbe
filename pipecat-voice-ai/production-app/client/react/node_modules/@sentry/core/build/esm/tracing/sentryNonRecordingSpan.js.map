{"version": 3, "file": "sentryNonRecordingSpan.js", "sources": ["../../../src/tracing/sentryNonRecordingSpan.ts"], "sourcesContent": ["import type {\n  SentrySpanArguments,\n  Span,\n  SpanAttributeValue,\n  SpanAttributes,\n  SpanContextData,\n  SpanStatus,\n  SpanTimeInput,\n} from '../types-hoist';\nimport { generateSpanId, generateTraceId } from '../utils-hoist/propagationContext';\nimport { TRACE_FLAG_NONE } from '../utils/spanUtils';\n\n/**\n * A Sentry Span that is non-recording, meaning it will not be sent to Sentry.\n */\nexport class SentryNonRecordingSpan implements Span {\n  private _traceId: string;\n  private _spanId: string;\n\n  public constructor(spanContext: SentrySpanArguments = {}) {\n    this._traceId = spanContext.traceId || generateTraceId();\n    this._spanId = spanContext.spanId || generateSpanId();\n  }\n\n  /** @inheritdoc */\n  public spanContext(): SpanContextData {\n    return {\n      spanId: this._spanId,\n      traceId: this._traceId,\n      traceFlags: TRACE_FLAG_NONE,\n    };\n  }\n\n  /** @inheritdoc */\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  public end(_timestamp?: SpanTimeInput): void {}\n\n  /** @inheritdoc */\n  public setAttribute(_key: string, _value: SpanAttributeValue | undefined): this {\n    return this;\n  }\n\n  /** @inheritdoc */\n  public setAttributes(_values: SpanAttributes): this {\n    return this;\n  }\n\n  /** @inheritdoc */\n  public setStatus(_status: SpanStatus): this {\n    return this;\n  }\n\n  /** @inheritdoc */\n  public updateName(_name: string): this {\n    return this;\n  }\n\n  /** @inheritdoc */\n  public isRecording(): boolean {\n    return false;\n  }\n\n  /** @inheritdoc */\n  public addEvent(\n    _name: string,\n    _attributesOrStartTime?: SpanAttributes | SpanTimeInput,\n    _startTime?: SpanTimeInput,\n  ): this {\n    return this;\n  }\n\n  /**\n   * This should generally not be used,\n   * but we need it for being compliant with the OTEL Span interface.\n   *\n   * @hidden\n   * @internal\n   */\n  public addLink(_link: unknown): this {\n    return this;\n  }\n\n  /**\n   * This should generally not be used,\n   * but we need it for being compliant with the OTEL Span interface.\n   *\n   * @hidden\n   * @internal\n   */\n  public addLinks(_links: unknown[]): this {\n    return this;\n  }\n\n  /**\n   * This should generally not be used,\n   * but we need it for being compliant with the OTEL Span interface.\n   *\n   * @hidden\n   * @internal\n   */\n  public recordException(_exception: unknown, _time?: number | undefined): void {\n    // noop\n  }\n}\n"], "names": [], "mappings": ";;;AAYA;AACA;AACA;AACO,MAAM,wBAAuC;;AAIpD,GAAS,WAAW,CAAC,WAAW,GAAwB,EAAE,EAAE;AAC5D,IAAI,IAAI,CAAC,QAAA,GAAW,WAAW,CAAC,OAAQ,IAAG,eAAe,EAAE;AAC5D,IAAI,IAAI,CAAC,OAAA,GAAU,WAAW,CAAC,MAAO,IAAG,cAAc,EAAE;AACzD;;AAEA;AACA,GAAS,WAAW,GAAoB;AACxC,IAAI,OAAO;AACX,MAAM,MAAM,EAAE,IAAI,CAAC,OAAO;AAC1B,MAAM,OAAO,EAAE,IAAI,CAAC,QAAQ;AAC5B,MAAM,UAAU,EAAE,eAAe;AACjC,KAAK;AACL;;AAEA;AACA;AACA,GAAS,GAAG,CAAC,UAAU,EAAwB;;AAE/C;AACA,GAAS,YAAY,CAAC,IAAI,EAAU,MAAM,EAAwC;AAClF,IAAI,OAAO,IAAI;AACf;;AAEA;AACA,GAAS,aAAa,CAAC,OAAO,EAAwB;AACtD,IAAI,OAAO,IAAI;AACf;;AAEA;AACA,GAAS,SAAS,CAAC,OAAO,EAAoB;AAC9C,IAAI,OAAO,IAAI;AACf;;AAEA;AACA,GAAS,UAAU,CAAC,KAAK,EAAgB;AACzC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA,GAAS,WAAW,GAAY;AAChC,IAAI,OAAO,KAAK;AAChB;;AAEA;AACA,GAAS,QAAQ;AACjB,IAAI,KAAK;AACT,IAAI,sBAAsB;AAC1B,IAAI,UAAU;AACd,IAAU;AACV,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,OAAO,CAAC,KAAK,EAAiB;AACvC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,QAAQ,CAAC,MAAM,EAAmB;AAC3C,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,eAAe,CAAC,UAAU,EAAW,KAAK,EAA6B;AAChF;AACA;AACA;;;;"}