{"version": 3, "file": "is.js", "sources": ["../../../src/utils-hoist/is.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport type { ParameterizedString, PolymorphicEvent, Primitive } from '../types-hoist';\n\n// eslint-disable-next-line @typescript-eslint/unbound-method\nconst objectToString = Object.prototype.toString;\n\n/**\n * Checks whether given value's type is one of a few Error or Error-like\n * {@link isError}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isError(wat: unknown): wat is Error {\n  switch (objectToString.call(wat)) {\n    case '[object Error]':\n    case '[object Exception]':\n    case '[object DOMException]':\n    case '[object WebAssembly.Exception]':\n      return true;\n    default:\n      return isInstanceOf(wat, Error);\n  }\n}\n/**\n * Checks whether given value is an instance of the given built-in class.\n *\n * @param wat The value to be checked\n * @param className\n * @returns A boolean representing the result.\n */\nfunction isBuiltin(wat: unknown, className: string): boolean {\n  return objectToString.call(wat) === `[object ${className}]`;\n}\n\n/**\n * Checks whether given value's type is ErrorEvent\n * {@link isErrorEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isErrorEvent(wat: unknown): boolean {\n  return isBuiltin(wat, 'ErrorEvent');\n}\n\n/**\n * Checks whether given value's type is DOMError\n * {@link isDOMError}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isDOMError(wat: unknown): boolean {\n  return isBuiltin(wat, 'DOMError');\n}\n\n/**\n * Checks whether given value's type is DOMException\n * {@link isDOMException}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isDOMException(wat: unknown): boolean {\n  return isBuiltin(wat, 'DOMException');\n}\n\n/**\n * Checks whether given value's type is a string\n * {@link isString}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isString(wat: unknown): wat is string {\n  return isBuiltin(wat, 'String');\n}\n\n/**\n * Checks whether given string is parameterized\n * {@link isParameterizedString}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isParameterizedString(wat: unknown): wat is ParameterizedString {\n  return (\n    typeof wat === 'object' &&\n    wat !== null &&\n    '__sentry_template_string__' in wat &&\n    '__sentry_template_values__' in wat\n  );\n}\n\n/**\n * Checks whether given value is a primitive (undefined, null, number, boolean, string, bigint, symbol)\n * {@link isPrimitive}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isPrimitive(wat: unknown): wat is Primitive {\n  return wat === null || isParameterizedString(wat) || (typeof wat !== 'object' && typeof wat !== 'function');\n}\n\n/**\n * Checks whether given value's type is an object literal, or a class instance.\n * {@link isPlainObject}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isPlainObject(wat: unknown): wat is Record<string, unknown> {\n  return isBuiltin(wat, 'Object');\n}\n\n/**\n * Checks whether given value's type is an Event instance\n * {@link isEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isEvent(wat: unknown): wat is PolymorphicEvent {\n  return typeof Event !== 'undefined' && isInstanceOf(wat, Event);\n}\n\n/**\n * Checks whether given value's type is an Element instance\n * {@link isElement}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isElement(wat: unknown): boolean {\n  return typeof Element !== 'undefined' && isInstanceOf(wat, Element);\n}\n\n/**\n * Checks whether given value's type is an regexp\n * {@link isRegExp}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isRegExp(wat: unknown): wat is RegExp {\n  return isBuiltin(wat, 'RegExp');\n}\n\n/**\n * Checks whether given value has a then function.\n * @param wat A value to be checked.\n */\nexport function isThenable(wat: any): wat is PromiseLike<any> {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  return Boolean(wat && wat.then && typeof wat.then === 'function');\n}\n\n/**\n * Checks whether given value's type is a SyntheticEvent\n * {@link isSyntheticEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isSyntheticEvent(wat: unknown): boolean {\n  return isPlainObject(wat) && 'nativeEvent' in wat && 'preventDefault' in wat && 'stopPropagation' in wat;\n}\n\n/**\n * Checks whether given value's type is an instance of provided constructor.\n * {@link isInstanceOf}.\n *\n * @param wat A value to be checked.\n * @param base A constructor to be used in a check.\n * @returns A boolean representing the result.\n */\nexport function isInstanceOf(wat: any, base: any): boolean {\n  try {\n    return wat instanceof base;\n  } catch (_e) {\n    return false;\n  }\n}\n\ninterface VueViewModel {\n  // Vue3\n  __isVue?: boolean;\n  // Vue2\n  _isVue?: boolean;\n}\n/**\n * Checks whether given value's type is a Vue ViewModel.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isVueViewModel(wat: unknown): boolean {\n  // Not using Object.prototype.toString because in Vue 3 it would read the instance's Symbol(Symbol.toStringTag) property.\n  return !!(typeof wat === 'object' && wat !== null && ((wat as VueViewModel).__isVue || (wat as VueViewModel)._isVue));\n}\n"], "names": [], "mappings": ";;AAIA;AACA,MAAM,iBAAiB,MAAM,CAAC,SAAS,CAAC,QAAQ;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,GAAG,EAAyB;AACpD,EAAE,QAAQ,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC;AAClC,IAAI,KAAK,gBAAgB;AACzB,IAAI,KAAK,oBAAoB;AAC7B,IAAI,KAAK,uBAAuB;AAChC,IAAI,KAAK,gCAAgC;AACzC,MAAM,OAAO,IAAI;AACjB,IAAI;AACJ,MAAM,OAAO,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,CAAC,GAAG,EAAW,SAAS,EAAmB;AAC7D,EAAE,OAAO,cAAc,CAAC,IAAI,CAAC,GAAG,CAAA,KAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC;AAC7D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,GAAG,EAAoB;AACpD,EAAE,OAAO,SAAS,CAAC,GAAG,EAAE,YAAY,CAAC;AACrC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU,CAAC,GAAG,EAAoB;AAClD,EAAE,OAAO,SAAS,CAAC,GAAG,EAAE,UAAU,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,GAAG,EAAoB;AACtD,EAAE,OAAO,SAAS,CAAC,GAAG,EAAE,cAAc,CAAC;AACvC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,GAAG,EAA0B;AACtD,EAAE,OAAO,SAAS,CAAC,GAAG,EAAE,QAAQ,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,qBAAqB,CAAC,GAAG,EAAuC;AAChF,EAAE;AACF,IAAI,OAAO,GAAI,KAAI,QAAS;AAC5B,IAAI,GAAA,KAAQ,IAAK;AACjB,IAAI,4BAAA,IAAgC,GAAI;AACxC,IAAI,gCAAgC;AACpC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW,CAAC,GAAG,EAA6B;AAC5D,EAAE,OAAO,QAAQ,IAAA,IAAQ,qBAAqB,CAAC,GAAG,CAAE,KAAI,OAAO,GAAA,KAAQ,QAAS,IAAG,OAAO,GAAI,KAAI,UAAU,CAAC;AAC7G;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAC,GAAG,EAA2C;AAC5E,EAAE,OAAO,SAAS,CAAC,GAAG,EAAE,QAAQ,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,GAAG,EAAoC;AAC/D,EAAE,OAAO,OAAO,KAAA,KAAU,WAAA,IAAe,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC;AACjE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,SAAS,CAAC,GAAG,EAAoB;AACjD,EAAE,OAAO,OAAO,OAAA,KAAY,WAAA,IAAe,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC;AACrE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,GAAG,EAA0B;AACtD,EAAE,OAAO,SAAS,CAAC,GAAG,EAAE,QAAQ,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACO,SAAS,UAAU,CAAC,GAAG,EAAgC;AAC9D;AACA,EAAE,OAAO,OAAO,CAAC,GAAA,IAAO,GAAG,CAAC,IAAK,IAAG,OAAO,GAAG,CAAC,IAAK,KAAI,UAAU,CAAC;AACnE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,gBAAgB,CAAC,GAAG,EAAoB;AACxD,EAAE,OAAO,aAAa,CAAC,GAAG,CAAA,IAAK,aAAc,IAAG,GAAI,IAAG,oBAAoB,GAAA,IAAO,iBAAA,IAAqB,GAAG;AAC1G;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,GAAG,EAAO,IAAI,EAAgB;AAC3D,EAAE,IAAI;AACN,IAAI,OAAO,GAAI,YAAW,IAAI;AAC9B,GAAI,CAAA,OAAO,EAAE,EAAE;AACf,IAAI,OAAO,KAAK;AAChB;AACA;;AAQA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,GAAG,EAAoB;AACtD;AACA,EAAE,OAAO,CAAC,EAAE,OAAO,GAAI,KAAI,QAAS,IAAG,GAAI,KAAI,IAAK,KAAI,CAAC,GAAA,GAAqB,OAAQ,IAAG,CAAC,GAAA,GAAqB,MAAM,CAAC,CAAC;AACvH;;;;;;;;;;;;;;;;;;"}