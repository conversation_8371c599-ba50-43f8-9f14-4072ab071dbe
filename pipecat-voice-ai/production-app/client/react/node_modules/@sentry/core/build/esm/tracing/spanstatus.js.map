{"version": 3, "file": "spanstatus.js", "sources": ["../../../src/tracing/spanstatus.ts"], "sourcesContent": ["import type { Span, SpanStatus } from '../types-hoist';\n\nexport const SPAN_STATUS_UNSET = 0;\nexport const SPAN_STATUS_OK = 1;\nexport const SPAN_STATUS_ERROR = 2;\n\n/**\n * Converts a HTTP status code into a sentry status with a message.\n *\n * @param httpStatus The HTTP response status code.\n * @returns The span status or unknown_error.\n */\n// https://develop.sentry.dev/sdk/event-payloads/span/\nexport function getSpanStatusFromHttpCode(httpStatus: number): SpanStatus {\n  if (httpStatus < 400 && httpStatus >= 100) {\n    return { code: SPAN_STATUS_OK };\n  }\n\n  if (httpStatus >= 400 && httpStatus < 500) {\n    switch (httpStatus) {\n      case 401:\n        return { code: SPAN_STATUS_ERROR, message: 'unauthenticated' };\n      case 403:\n        return { code: SPAN_STATUS_ERROR, message: 'permission_denied' };\n      case 404:\n        return { code: SPAN_STATUS_ERROR, message: 'not_found' };\n      case 409:\n        return { code: SPAN_STATUS_ERROR, message: 'already_exists' };\n      case 413:\n        return { code: SPAN_STATUS_ERROR, message: 'failed_precondition' };\n      case 429:\n        return { code: SPAN_STATUS_ERROR, message: 'resource_exhausted' };\n      case 499:\n        return { code: SPAN_STATUS_ERROR, message: 'cancelled' };\n      default:\n        return { code: SPAN_STATUS_ERROR, message: 'invalid_argument' };\n    }\n  }\n\n  if (httpStatus >= 500 && httpStatus < 600) {\n    switch (httpStatus) {\n      case 501:\n        return { code: SPAN_STATUS_ERROR, message: 'unimplemented' };\n      case 503:\n        return { code: SPAN_STATUS_ERROR, message: 'unavailable' };\n      case 504:\n        return { code: SPAN_STATUS_ERROR, message: 'deadline_exceeded' };\n      default:\n        return { code: SPAN_STATUS_ERROR, message: 'internal_error' };\n    }\n  }\n\n  return { code: SPAN_STATUS_ERROR, message: 'unknown_error' };\n}\n\n/**\n * Sets the Http status attributes on the current span based on the http code.\n * Additionally, the span's status is updated, depending on the http code.\n */\nexport function setHttpStatus(span: Span, httpStatus: number): void {\n  span.setAttribute('http.response.status_code', httpStatus);\n\n  const spanStatus = getSpanStatusFromHttpCode(httpStatus);\n  if (spanStatus.message !== 'unknown_error') {\n    span.setStatus(spanStatus);\n  }\n}\n"], "names": [], "mappings": "AAEO,MAAM,iBAAkB,GAAE;AAC1B,MAAM,cAAe,GAAE;AACvB,MAAM,iBAAkB,GAAE;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,yBAAyB,CAAC,UAAU,EAAsB;AAC1E,EAAE,IAAI,UAAW,GAAE,OAAO,UAAA,IAAc,GAAG,EAAE;AAC7C,IAAI,OAAO,EAAE,IAAI,EAAE,gBAAgB;AACnC;;AAEA,EAAE,IAAI,UAAW,IAAG,OAAO,UAAA,GAAa,GAAG,EAAE;AAC7C,IAAI,QAAQ,UAAU;AACtB,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,iBAAA,EAAmB;AACtE,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,mBAAA,EAAqB;AACxE,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,WAAA,EAAa;AAChE,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,gBAAA,EAAkB;AACrE,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,qBAAA,EAAuB;AAC1E,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,oBAAA,EAAsB;AACzE,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,WAAA,EAAa;AAChE,MAAM;AACN,QAAQ,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,kBAAA,EAAoB;AACvE;AACA;;AAEA,EAAE,IAAI,UAAW,IAAG,OAAO,UAAA,GAAa,GAAG,EAAE;AAC7C,IAAI,QAAQ,UAAU;AACtB,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,eAAA,EAAiB;AACpE,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,aAAA,EAAe;AAClE,MAAM,KAAK,GAAG;AACd,QAAQ,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,mBAAA,EAAqB;AACxE,MAAM;AACN,QAAQ,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,gBAAA,EAAkB;AACrE;AACA;;AAEA,EAAE,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,eAAA,EAAiB;AAC9D;;AAEA;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAC,IAAI,EAAQ,UAAU,EAAgB;AACpE,EAAE,IAAI,CAAC,YAAY,CAAC,2BAA2B,EAAE,UAAU,CAAC;;AAE5D,EAAE,MAAM,UAAW,GAAE,yBAAyB,CAAC,UAAU,CAAC;AAC1D,EAAE,IAAI,UAAU,CAAC,OAAQ,KAAI,eAAe,EAAE;AAC9C,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;AAC9B;AACA;;;;"}