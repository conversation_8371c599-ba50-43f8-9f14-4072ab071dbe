{"version": 3, "file": "scope.js", "sources": ["../../src/scope.ts"], "sourcesContent": ["/* eslint-disable max-lines */\nimport type {\n  Attachment,\n  Breadcrumb,\n  CaptureContext,\n  Client,\n  Context,\n  Contexts,\n  Event,\n  EventHint,\n  EventProcessor,\n  Extra,\n  Extras,\n  Primitive,\n  PropagationContext,\n  RequestSession,\n  Scope as ScopeInterface,\n  ScopeContext,\n  ScopeData,\n  Session,\n  SeverityLevel,\n  User,\n} from './types-hoist';\n\nimport { updateSession } from './session';\nimport { isPlainObject } from './utils-hoist/is';\nimport { logger } from './utils-hoist/logger';\nimport { uuid4 } from './utils-hoist/misc';\nimport { generateSpanId, generateTraceId } from './utils-hoist/propagationContext';\nimport { dateTimestampInSeconds } from './utils-hoist/time';\nimport { merge } from './utils/merge';\nimport { _getSpanForScope, _setSpanForScope } from './utils/spanOnScope';\n\n/**\n * Default value for maximum number of breadcrumbs added to an event.\n */\nconst DEFAULT_MAX_BREADCRUMBS = 100;\n\n/**\n * Holds additional event information.\n */\nclass ScopeClass implements ScopeInterface {\n  /** Flag if notifying is happening. */\n  protected _notifyingListeners: boolean;\n\n  /** Callback for client to receive scope changes. */\n  protected _scopeListeners: Array<(scope: Scope) => void>;\n\n  /** Callback list that will be called during event processing. */\n  protected _eventProcessors: EventProcessor[];\n\n  /** Array of breadcrumbs. */\n  protected _breadcrumbs: Breadcrumb[];\n\n  /** User */\n  protected _user: User;\n\n  /** Tags */\n  protected _tags: { [key: string]: Primitive };\n\n  /** Extra */\n  protected _extra: Extras;\n\n  /** Contexts */\n  protected _contexts: Contexts;\n\n  /** Attachments */\n  protected _attachments: Attachment[];\n\n  /** Propagation Context for distributed tracing */\n  protected _propagationContext: PropagationContext;\n\n  /**\n   * A place to stash data which is needed at some point in the SDK's event processing pipeline but which shouldn't get\n   * sent to Sentry\n   */\n  protected _sdkProcessingMetadata: { [key: string]: unknown };\n\n  /** Fingerprint */\n  protected _fingerprint?: string[];\n\n  /** Severity */\n  protected _level?: SeverityLevel;\n\n  /**\n   * Transaction Name\n   *\n   * IMPORTANT: The transaction name on the scope has nothing to do with root spans/transaction objects.\n   * It's purpose is to assign a transaction to the scope that's added to non-transaction events.\n   */\n  protected _transactionName?: string;\n\n  /** Session */\n  protected _session?: Session;\n\n  /** Request Mode Session Status */\n  // eslint-disable-next-line deprecation/deprecation\n  protected _requestSession?: RequestSession;\n\n  /** The client on this scope */\n  protected _client?: Client;\n\n  /** Contains the last event id of a captured event.  */\n  protected _lastEventId?: string;\n\n  // NOTE: Any field which gets added here should get added not only to the constructor but also to the `clone` method.\n\n  public constructor() {\n    this._notifyingListeners = false;\n    this._scopeListeners = [];\n    this._eventProcessors = [];\n    this._breadcrumbs = [];\n    this._attachments = [];\n    this._user = {};\n    this._tags = {};\n    this._extra = {};\n    this._contexts = {};\n    this._sdkProcessingMetadata = {};\n    this._propagationContext = {\n      traceId: generateTraceId(),\n      spanId: generateSpanId(),\n    };\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public clone(): ScopeClass {\n    const newScope = new ScopeClass();\n    newScope._breadcrumbs = [...this._breadcrumbs];\n    newScope._tags = { ...this._tags };\n    newScope._extra = { ...this._extra };\n    newScope._contexts = { ...this._contexts };\n    if (this._contexts.flags) {\n      // We need to copy the `values` array so insertions on a cloned scope\n      // won't affect the original array.\n      newScope._contexts.flags = {\n        values: [...this._contexts.flags.values],\n      };\n    }\n\n    newScope._user = this._user;\n    newScope._level = this._level;\n    newScope._session = this._session;\n    newScope._transactionName = this._transactionName;\n    newScope._fingerprint = this._fingerprint;\n    newScope._eventProcessors = [...this._eventProcessors];\n    newScope._requestSession = this._requestSession;\n    newScope._attachments = [...this._attachments];\n    newScope._sdkProcessingMetadata = { ...this._sdkProcessingMetadata };\n    newScope._propagationContext = { ...this._propagationContext };\n    newScope._client = this._client;\n    newScope._lastEventId = this._lastEventId;\n\n    _setSpanForScope(newScope, _getSpanForScope(this));\n\n    return newScope;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setClient(client: Client | undefined): void {\n    this._client = client;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setLastEventId(lastEventId: string | undefined): void {\n    this._lastEventId = lastEventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getClient<C extends Client>(): C | undefined {\n    return this._client as C | undefined;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public lastEventId(): string | undefined {\n    return this._lastEventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addScopeListener(callback: (scope: Scope) => void): void {\n    this._scopeListeners.push(callback);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addEventProcessor(callback: EventProcessor): this {\n    this._eventProcessors.push(callback);\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setUser(user: User | null): this {\n    // If null is passed we want to unset everything, but still define keys,\n    // so that later down in the pipeline any existing values are cleared.\n    this._user = user || {\n      email: undefined,\n      id: undefined,\n      ip_address: undefined,\n      username: undefined,\n    };\n\n    if (this._session) {\n      updateSession(this._session, { user });\n    }\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getUser(): User | undefined {\n    return this._user;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  public getRequestSession(): RequestSession | undefined {\n    return this._requestSession;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  public setRequestSession(requestSession?: RequestSession): this {\n    this._requestSession = requestSession;\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTags(tags: { [key: string]: Primitive }): this {\n    this._tags = {\n      ...this._tags,\n      ...tags,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTag(key: string, value: Primitive): this {\n    this._tags = { ...this._tags, [key]: value };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtras(extras: Extras): this {\n    this._extra = {\n      ...this._extra,\n      ...extras,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtra(key: string, extra: Extra): this {\n    this._extra = { ...this._extra, [key]: extra };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setFingerprint(fingerprint: string[]): this {\n    this._fingerprint = fingerprint;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setLevel(level: SeverityLevel): this {\n    this._level = level;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets the transaction name on the scope so that the name of e.g. taken server route or\n   * the page location is attached to future events.\n   *\n   * IMPORTANT: Calling this function does NOT change the name of the currently active\n   * root span. If you want to change the name of the active root span, use\n   * `Sentry.updateSpanName(rootSpan, 'new name')` instead.\n   *\n   * By default, the SDK updates the scope's transaction name automatically on sensible\n   * occasions, such as a page navigation or when handling a new request on the server.\n   */\n  public setTransactionName(name?: string): this {\n    this._transactionName = name;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setContext(key: string, context: Context | null): this {\n    if (context === null) {\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete this._contexts[key];\n    } else {\n      this._contexts[key] = context;\n    }\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setSession(session?: Session): this {\n    if (!session) {\n      delete this._session;\n    } else {\n      this._session = session;\n    }\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getSession(): Session | undefined {\n    return this._session;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public update(captureContext?: CaptureContext): this {\n    if (!captureContext) {\n      return this;\n    }\n\n    const scopeToMerge = typeof captureContext === 'function' ? captureContext(this) : captureContext;\n\n    const [scopeInstance, requestSession] =\n      scopeToMerge instanceof Scope\n        ? // eslint-disable-next-line deprecation/deprecation\n          [scopeToMerge.getScopeData(), scopeToMerge.getRequestSession()]\n        : isPlainObject(scopeToMerge)\n          ? [captureContext as ScopeContext, (captureContext as ScopeContext).requestSession]\n          : [];\n\n    const { tags, extra, user, contexts, level, fingerprint = [], propagationContext } = scopeInstance || {};\n\n    this._tags = { ...this._tags, ...tags };\n    this._extra = { ...this._extra, ...extra };\n    this._contexts = { ...this._contexts, ...contexts };\n\n    if (user && Object.keys(user).length) {\n      this._user = user;\n    }\n\n    if (level) {\n      this._level = level;\n    }\n\n    if (fingerprint.length) {\n      this._fingerprint = fingerprint;\n    }\n\n    if (propagationContext) {\n      this._propagationContext = propagationContext;\n    }\n\n    if (requestSession) {\n      this._requestSession = requestSession;\n    }\n\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public clear(): this {\n    // client is not cleared here on purpose!\n    this._breadcrumbs = [];\n    this._tags = {};\n    this._extra = {};\n    this._user = {};\n    this._contexts = {};\n    this._level = undefined;\n    this._transactionName = undefined;\n    this._fingerprint = undefined;\n    this._requestSession = undefined;\n    this._session = undefined;\n    _setSpanForScope(this, undefined);\n    this._attachments = [];\n    this.setPropagationContext({ traceId: generateTraceId() });\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addBreadcrumb(breadcrumb: Breadcrumb, maxBreadcrumbs?: number): this {\n    const maxCrumbs = typeof maxBreadcrumbs === 'number' ? maxBreadcrumbs : DEFAULT_MAX_BREADCRUMBS;\n\n    // No data has been changed, so don't notify scope listeners\n    if (maxCrumbs <= 0) {\n      return this;\n    }\n\n    const mergedBreadcrumb = {\n      timestamp: dateTimestampInSeconds(),\n      ...breadcrumb,\n    };\n\n    this._breadcrumbs.push(mergedBreadcrumb);\n    if (this._breadcrumbs.length > maxCrumbs) {\n      this._breadcrumbs = this._breadcrumbs.slice(-maxCrumbs);\n      if (this._client) {\n        this._client.recordDroppedEvent('buffer_overflow', 'log_item');\n      }\n    }\n\n    this._notifyScopeListeners();\n\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getLastBreadcrumb(): Breadcrumb | undefined {\n    return this._breadcrumbs[this._breadcrumbs.length - 1];\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public clearBreadcrumbs(): this {\n    this._breadcrumbs = [];\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addAttachment(attachment: Attachment): this {\n    this._attachments.push(attachment);\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public clearAttachments(): this {\n    this._attachments = [];\n    return this;\n  }\n\n  /** @inheritDoc */\n  public getScopeData(): ScopeData {\n    return {\n      breadcrumbs: this._breadcrumbs,\n      attachments: this._attachments,\n      contexts: this._contexts,\n      tags: this._tags,\n      extra: this._extra,\n      user: this._user,\n      level: this._level,\n      fingerprint: this._fingerprint || [],\n      eventProcessors: this._eventProcessors,\n      propagationContext: this._propagationContext,\n      sdkProcessingMetadata: this._sdkProcessingMetadata,\n      transactionName: this._transactionName,\n      span: _getSpanForScope(this),\n    };\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setSDKProcessingMetadata(newData: { [key: string]: unknown }): this {\n    this._sdkProcessingMetadata = merge(this._sdkProcessingMetadata, newData, 2);\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setPropagationContext(\n    context: Omit<PropagationContext, 'spanId'> & Partial<Pick<PropagationContext, 'spanId'>>,\n  ): this {\n    this._propagationContext = {\n      // eslint-disable-next-line deprecation/deprecation\n      spanId: generateSpanId(),\n      ...context,\n    };\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getPropagationContext(): PropagationContext {\n    return this._propagationContext;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureException(exception: unknown, hint?: EventHint): string {\n    const eventId = hint && hint.event_id ? hint.event_id : uuid4();\n\n    if (!this._client) {\n      logger.warn('No client configured on scope - will not capture exception!');\n      return eventId;\n    }\n\n    const syntheticException = new Error('Sentry syntheticException');\n\n    this._client.captureException(\n      exception,\n      {\n        originalException: exception,\n        syntheticException,\n        ...hint,\n        event_id: eventId,\n      },\n      this,\n    );\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureMessage(message: string, level?: SeverityLevel, hint?: EventHint): string {\n    const eventId = hint && hint.event_id ? hint.event_id : uuid4();\n\n    if (!this._client) {\n      logger.warn('No client configured on scope - will not capture message!');\n      return eventId;\n    }\n\n    const syntheticException = new Error(message);\n\n    this._client.captureMessage(\n      message,\n      level,\n      {\n        originalException: message,\n        syntheticException,\n        ...hint,\n        event_id: eventId,\n      },\n      this,\n    );\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureEvent(event: Event, hint?: EventHint): string {\n    const eventId = hint && hint.event_id ? hint.event_id : uuid4();\n\n    if (!this._client) {\n      logger.warn('No client configured on scope - will not capture event!');\n      return eventId;\n    }\n\n    this._client.captureEvent(event, { ...hint, event_id: eventId }, this);\n\n    return eventId;\n  }\n\n  /**\n   * This will be called on every set call.\n   */\n  protected _notifyScopeListeners(): void {\n    // We need this check for this._notifyingListeners to be able to work on scope during updates\n    // If this check is not here we'll produce endless recursion when something is done with the scope\n    // during the callback.\n    if (!this._notifyingListeners) {\n      this._notifyingListeners = true;\n      this._scopeListeners.forEach(callback => {\n        callback(this);\n      });\n      this._notifyingListeners = false;\n    }\n  }\n}\n\n/**\n * Holds additional event information.\n */\nexport const Scope = ScopeClass;\n\n/**\n * Holds additional event information.\n */\nexport type Scope = ScopeInterface;\n"], "names": ["generateTraceId", "generateSpanId", "_setSpanForScope", "_getSpanForScope", "updateSession", "isPlainObject", "dateTimestampInSeconds", "merge", "uuid4", "logger"], "mappings": ";;;;;;;;;;;AAiCA;AACA;AACA;AACA,MAAM,uBAAA,GAA0B,GAAG;;AAEnC;AACA;AACA;AACA,MAAM,YAAqC;AAC3C;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;;AAGA;AACA;AACA;AACA;;AAGA;;AAGA;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAGA;;AAGA;AACA;;AAGA;;AAGA;;AAGA;;AAEA,GAAS,WAAW,GAAG;AACvB,IAAI,IAAI,CAAC,mBAAoB,GAAE,KAAK;AACpC,IAAI,IAAI,CAAC,eAAgB,GAAE,EAAE;AAC7B,IAAI,IAAI,CAAC,gBAAiB,GAAE,EAAE;AAC9B,IAAI,IAAI,CAAC,YAAa,GAAE,EAAE;AAC1B,IAAI,IAAI,CAAC,YAAa,GAAE,EAAE;AAC1B,IAAI,IAAI,CAAC,KAAM,GAAE,EAAE;AACnB,IAAI,IAAI,CAAC,KAAM,GAAE,EAAE;AACnB,IAAI,IAAI,CAAC,MAAO,GAAE,EAAE;AACpB,IAAI,IAAI,CAAC,SAAU,GAAE,EAAE;AACvB,IAAI,IAAI,CAAC,sBAAuB,GAAE,EAAE;AACpC,IAAI,IAAI,CAAC,mBAAA,GAAsB;AAC/B,MAAM,OAAO,EAAEA,kCAAe,EAAE;AAChC,MAAM,MAAM,EAAEC,iCAAc,EAAE;AAC9B,KAAK;AACL;;AAEA;AACA;AACA;AACA,GAAS,KAAK,GAAe;AAC7B,IAAI,MAAM,QAAS,GAAE,IAAI,UAAU,EAAE;AACrC,IAAI,QAAQ,CAAC,YAAa,GAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AAClD,IAAI,QAAQ,CAAC,KAAM,GAAE,EAAE,GAAG,IAAI,CAAC,KAAA,EAAO;AACtC,IAAI,QAAQ,CAAC,MAAO,GAAE,EAAE,GAAG,IAAI,CAAC,MAAA,EAAQ;AACxC,IAAI,QAAQ,CAAC,SAAU,GAAE,EAAE,GAAG,IAAI,CAAC,SAAA,EAAW;AAC9C,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;AAC9B;AACA;AACA,MAAM,QAAQ,CAAC,SAAS,CAAC,QAAQ;AACjC,QAAQ,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC;AAChD,OAAO;AACP;;AAEA,IAAI,QAAQ,CAAC,KAAA,GAAQ,IAAI,CAAC,KAAK;AAC/B,IAAI,QAAQ,CAAC,MAAA,GAAS,IAAI,CAAC,MAAM;AACjC,IAAI,QAAQ,CAAC,QAAA,GAAW,IAAI,CAAC,QAAQ;AACrC,IAAI,QAAQ,CAAC,gBAAA,GAAmB,IAAI,CAAC,gBAAgB;AACrD,IAAI,QAAQ,CAAC,YAAA,GAAe,IAAI,CAAC,YAAY;AAC7C,IAAI,QAAQ,CAAC,gBAAiB,GAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAC1D,IAAI,QAAQ,CAAC,eAAA,GAAkB,IAAI,CAAC,eAAe;AACnD,IAAI,QAAQ,CAAC,YAAa,GAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AAClD,IAAI,QAAQ,CAAC,sBAAuB,GAAE,EAAE,GAAG,IAAI,CAAC,sBAAA,EAAwB;AACxE,IAAI,QAAQ,CAAC,mBAAoB,GAAE,EAAE,GAAG,IAAI,CAAC,mBAAA,EAAqB;AAClE,IAAI,QAAQ,CAAC,OAAA,GAAU,IAAI,CAAC,OAAO;AACnC,IAAI,QAAQ,CAAC,YAAA,GAAe,IAAI,CAAC,YAAY;;AAE7C,IAAIC,4BAAgB,CAAC,QAAQ,EAAEC,4BAAgB,CAAC,IAAI,CAAC,CAAC;;AAEtD,IAAI,OAAO,QAAQ;AACnB;;AAEA;AACA;AACA;AACA,GAAS,SAAS,CAAC,MAAM,EAA4B;AACrD,IAAI,IAAI,CAAC,OAAQ,GAAE,MAAM;AACzB;;AAEA;AACA;AACA;AACA,GAAS,cAAc,CAAC,WAAW,EAA4B;AAC/D,IAAI,IAAI,CAAC,YAAa,GAAE,WAAW;AACnC;;AAEA;AACA;AACA;AACA,GAAS,SAAS,GAAoC;AACtD,IAAI,OAAO,IAAI,CAAC,OAAQ;AACxB;;AAEA;AACA;AACA;AACA,GAAS,WAAW,GAAuB;AAC3C,IAAI,OAAO,IAAI,CAAC,YAAY;AAC5B;;AAEA;AACA;AACA;AACA,GAAS,gBAAgB,CAAC,QAAQ,EAAgC;AAClE,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC;AACvC;;AAEA;AACA;AACA;AACA,GAAS,iBAAiB,CAAC,QAAQ,EAAwB;AAC3D,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC;AACxC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,OAAO,CAAC,IAAI,EAAqB;AAC1C;AACA;AACA,IAAI,IAAI,CAAC,KAAM,GAAE,QAAQ;AACzB,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,EAAE,EAAE,SAAS;AACnB,MAAM,UAAU,EAAE,SAAS;AAC3B,MAAM,QAAQ,EAAE,SAAS;AACzB,KAAK;;AAEL,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AACvB,MAAMC,qBAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAK,EAAC,CAAC;AAC5C;;AAEA,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,OAAO,GAAqB;AACrC,IAAI,OAAO,IAAI,CAAC,KAAK;AACrB;;AAEA;AACA;AACA;AACA;AACA,GAAS,iBAAiB,GAA+B;AACzD,IAAI,OAAO,IAAI,CAAC,eAAe;AAC/B;;AAEA;AACA;AACA;AACA;AACA,GAAS,iBAAiB,CAAC,cAAc,EAAyB;AAClE,IAAI,IAAI,CAAC,eAAgB,GAAE,cAAc;AACzC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,OAAO,CAAC,IAAI,EAAsC;AAC3D,IAAI,IAAI,CAAC,KAAA,GAAQ;AACjB,MAAM,GAAG,IAAI,CAAC,KAAK;AACnB,MAAM,GAAG,IAAI;AACb,KAAK;AACL,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,MAAM,CAAC,GAAG,EAAU,KAAK,EAAmB;AACrD,IAAI,IAAI,CAAC,KAAM,GAAE,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,OAAO;AAChD,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,SAAS,CAAC,MAAM,EAAgB;AACzC,IAAI,IAAI,CAAC,MAAA,GAAS;AAClB,MAAM,GAAG,IAAI,CAAC,MAAM;AACpB,MAAM,GAAG,MAAM;AACf,KAAK;AACL,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,QAAQ,CAAC,GAAG,EAAU,KAAK,EAAe;AACnD,IAAI,IAAI,CAAC,MAAO,GAAE,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,OAAO;AAClD,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,cAAc,CAAC,WAAW,EAAkB;AACrD,IAAI,IAAI,CAAC,YAAa,GAAE,WAAW;AACnC,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,QAAQ,CAAC,KAAK,EAAuB;AAC9C,IAAI,IAAI,CAAC,MAAO,GAAE,KAAK;AACvB,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,kBAAkB,CAAC,IAAI,EAAiB;AACjD,IAAI,IAAI,CAAC,gBAAiB,GAAE,IAAI;AAChC,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,UAAU,CAAC,GAAG,EAAU,OAAO,EAAwB;AAChE,IAAI,IAAI,OAAQ,KAAI,IAAI,EAAE;AAC1B;AACA,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;AAChC,WAAW;AACX,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAA,GAAI,OAAO;AACnC;;AAEA,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,UAAU,CAAC,OAAO,EAAkB;AAC7C,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,OAAO,IAAI,CAAC,QAAQ;AAC1B,WAAW;AACX,MAAM,IAAI,CAAC,QAAS,GAAE,OAAO;AAC7B;AACA,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,UAAU,GAAwB;AAC3C,IAAI,OAAO,IAAI,CAAC,QAAQ;AACxB;;AAEA;AACA;AACA;AACA,GAAS,MAAM,CAAC,cAAc,EAAyB;AACvD,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,OAAO,IAAI;AACjB;;AAEA,IAAI,MAAM,YAAA,GAAe,OAAO,cAAe,KAAI,UAAW,GAAE,cAAc,CAAC,IAAI,CAAA,GAAI,cAAc;;AAErG,IAAI,MAAM,CAAC,aAAa,EAAE,cAAc,CAAE;AAC1C,MAAM,wBAAwB;AAC9B;AACA,UAAU,CAAC,YAAY,CAAC,YAAY,EAAE,EAAE,YAAY,CAAC,iBAAiB,EAAE;AACxE,UAAUC,gBAAa,CAAC,YAAY;AACpC,YAAY,CAAC,cAAe,GAAiB,CAAC,cAAe,GAAiB,cAAc;AAC5F,YAAY,EAAE;;AAEd,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAY,GAAE,EAAE,EAAE,kBAAA,KAAuB,aAAA,IAAiB,EAAE;;AAE5G,IAAI,IAAI,CAAC,KAAM,GAAE,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAA,EAAM;AAC3C,IAAI,IAAI,CAAC,MAAO,GAAE,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,KAAA,EAAO;AAC9C,IAAI,IAAI,CAAC,SAAU,GAAE,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,QAAA,EAAU;;AAEvD,IAAI,IAAI,IAAK,IAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;AAC1C,MAAM,IAAI,CAAC,KAAM,GAAE,IAAI;AACvB;;AAEA,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,IAAI,CAAC,MAAO,GAAE,KAAK;AACzB;;AAEA,IAAI,IAAI,WAAW,CAAC,MAAM,EAAE;AAC5B,MAAM,IAAI,CAAC,YAAa,GAAE,WAAW;AACrC;;AAEA,IAAI,IAAI,kBAAkB,EAAE;AAC5B,MAAM,IAAI,CAAC,mBAAoB,GAAE,kBAAkB;AACnD;;AAEA,IAAI,IAAI,cAAc,EAAE;AACxB,MAAM,IAAI,CAAC,eAAgB,GAAE,cAAc;AAC3C;;AAEA,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,KAAK,GAAS;AACvB;AACA,IAAI,IAAI,CAAC,YAAa,GAAE,EAAE;AAC1B,IAAI,IAAI,CAAC,KAAM,GAAE,EAAE;AACnB,IAAI,IAAI,CAAC,MAAO,GAAE,EAAE;AACpB,IAAI,IAAI,CAAC,KAAM,GAAE,EAAE;AACnB,IAAI,IAAI,CAAC,SAAU,GAAE,EAAE;AACvB,IAAI,IAAI,CAAC,MAAO,GAAE,SAAS;AAC3B,IAAI,IAAI,CAAC,gBAAiB,GAAE,SAAS;AACrC,IAAI,IAAI,CAAC,YAAa,GAAE,SAAS;AACjC,IAAI,IAAI,CAAC,eAAgB,GAAE,SAAS;AACpC,IAAI,IAAI,CAAC,QAAS,GAAE,SAAS;AAC7B,IAAIH,4BAAgB,CAAC,IAAI,EAAE,SAAS,CAAC;AACrC,IAAI,IAAI,CAAC,YAAa,GAAE,EAAE;AAC1B,IAAI,IAAI,CAAC,qBAAqB,CAAC,EAAE,OAAO,EAAEF,kCAAe,EAAG,EAAC,CAAC;;AAE9D,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,aAAa,CAAC,UAAU,EAAc,cAAc,EAAiB;AAC9E,IAAI,MAAM,SAAU,GAAE,OAAO,cAAA,KAAmB,QAAS,GAAE,cAAe,GAAE,uBAAuB;;AAEnG;AACA,IAAI,IAAI,SAAU,IAAG,CAAC,EAAE;AACxB,MAAM,OAAO,IAAI;AACjB;;AAEA,IAAI,MAAM,mBAAmB;AAC7B,MAAM,SAAS,EAAEM,2BAAsB,EAAE;AACzC,MAAM,GAAG,UAAU;AACnB,KAAK;;AAEL,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC;AAC5C,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,MAAA,GAAS,SAAS,EAAE;AAC9C,MAAM,IAAI,CAAC,YAAa,GAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC;AAC7D,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;AACxB,QAAQ,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,UAAU,CAAC;AACtE;AACA;;AAEA,IAAI,IAAI,CAAC,qBAAqB,EAAE;;AAEhC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,iBAAiB,GAA2B;AACrD,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAA,GAAS,CAAC,CAAC;AAC1D;;AAEA;AACA;AACA;AACA,GAAS,gBAAgB,GAAS;AAClC,IAAI,IAAI,CAAC,YAAa,GAAE,EAAE;AAC1B,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,aAAa,CAAC,UAAU,EAAoB;AACrD,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC;AACtC,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,gBAAgB,GAAS;AAClC,IAAI,IAAI,CAAC,YAAa,GAAE,EAAE;AAC1B,IAAI,OAAO,IAAI;AACf;;AAEA;AACA,GAAS,YAAY,GAAc;AACnC,IAAI,OAAO;AACX,MAAM,WAAW,EAAE,IAAI,CAAC,YAAY;AACpC,MAAM,WAAW,EAAE,IAAI,CAAC,YAAY;AACpC,MAAM,QAAQ,EAAE,IAAI,CAAC,SAAS;AAC9B,MAAM,IAAI,EAAE,IAAI,CAAC,KAAK;AACtB,MAAM,KAAK,EAAE,IAAI,CAAC,MAAM;AACxB,MAAM,IAAI,EAAE,IAAI,CAAC,KAAK;AACtB,MAAM,KAAK,EAAE,IAAI,CAAC,MAAM;AACxB,MAAM,WAAW,EAAE,IAAI,CAAC,YAAa,IAAG,EAAE;AAC1C,MAAM,eAAe,EAAE,IAAI,CAAC,gBAAgB;AAC5C,MAAM,kBAAkB,EAAE,IAAI,CAAC,mBAAmB;AAClD,MAAM,qBAAqB,EAAE,IAAI,CAAC,sBAAsB;AACxD,MAAM,eAAe,EAAE,IAAI,CAAC,gBAAgB;AAC5C,MAAM,IAAI,EAAEH,4BAAgB,CAAC,IAAI,CAAC;AAClC,KAAK;AACL;;AAEA;AACA;AACA;AACA,GAAS,wBAAwB,CAAC,OAAO,EAAoC;AAC7E,IAAI,IAAI,CAAC,sBAAuB,GAAEI,WAAK,CAAC,IAAI,CAAC,sBAAsB,EAAE,OAAO,EAAE,CAAC,CAAC;AAChF,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,qBAAqB;AAC9B,IAAI,OAAO;AACX,IAAU;AACV,IAAI,IAAI,CAAC,mBAAA,GAAsB;AAC/B;AACA,MAAM,MAAM,EAAEN,iCAAc,EAAE;AAC9B,MAAM,GAAG,OAAO;AAChB,KAAK;AACL,IAAI,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA,GAAS,qBAAqB,GAAuB;AACrD,IAAI,OAAO,IAAI,CAAC,mBAAmB;AACnC;;AAEA;AACA;AACA;AACA,GAAS,gBAAgB,CAAC,SAAS,EAAW,IAAI,EAAsB;AACxE,IAAI,MAAM,OAAA,GAAU,IAAA,IAAQ,IAAI,CAAC,QAAS,GAAE,IAAI,CAAC,QAAA,GAAWO,UAAK,EAAE;;AAEnE,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAMC,aAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC;AAChF,MAAM,OAAO,OAAO;AACpB;;AAEA,IAAI,MAAM,kBAAmB,GAAE,IAAI,KAAK,CAAC,2BAA2B,CAAC;;AAErE,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB;AACjC,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,iBAAiB,EAAE,SAAS;AACpC,QAAQ,kBAAkB;AAC1B,QAAQ,GAAG,IAAI;AACf,QAAQ,QAAQ,EAAE,OAAO;AACzB,OAAO;AACP,MAAM,IAAI;AACV,KAAK;;AAEL,IAAI,OAAO,OAAO;AAClB;;AAEA;AACA;AACA;AACA,GAAS,cAAc,CAAC,OAAO,EAAU,KAAK,EAAkB,IAAI,EAAsB;AAC1F,IAAI,MAAM,OAAA,GAAU,IAAA,IAAQ,IAAI,CAAC,QAAS,GAAE,IAAI,CAAC,QAAA,GAAWD,UAAK,EAAE;;AAEnE,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAMC,aAAM,CAAC,IAAI,CAAC,2DAA2D,CAAC;AAC9E,MAAM,OAAO,OAAO;AACpB;;AAEA,IAAI,MAAM,kBAAmB,GAAE,IAAI,KAAK,CAAC,OAAO,CAAC;;AAEjD,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc;AAC/B,MAAM,OAAO;AACb,MAAM,KAAK;AACX,MAAM;AACN,QAAQ,iBAAiB,EAAE,OAAO;AAClC,QAAQ,kBAAkB;AAC1B,QAAQ,GAAG,IAAI;AACf,QAAQ,QAAQ,EAAE,OAAO;AACzB,OAAO;AACP,MAAM,IAAI;AACV,KAAK;;AAEL,IAAI,OAAO,OAAO;AAClB;;AAEA;AACA;AACA;AACA,GAAS,YAAY,CAAC,KAAK,EAAS,IAAI,EAAsB;AAC9D,IAAI,MAAM,OAAA,GAAU,IAAA,IAAQ,IAAI,CAAC,QAAS,GAAE,IAAI,CAAC,QAAA,GAAWD,UAAK,EAAE;;AAEnE,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAMC,aAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC;AAC5E,MAAM,OAAO,OAAO;AACpB;;AAEA,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,GAAG,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC;;AAE1E,IAAI,OAAO,OAAO;AAClB;;AAEA;AACA;AACA;AACA,GAAY,qBAAqB,GAAS;AAC1C;AACA;AACA;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;AACnC,MAAM,IAAI,CAAC,mBAAoB,GAAE,IAAI;AACrC,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,YAAY;AAC/C,QAAQ,QAAQ,CAAC,IAAI,CAAC;AACtB,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,mBAAoB,GAAE,KAAK;AACtC;AACA;AACA;;AAEA;AACA;AACA;AACO,MAAM,KAAM,GAAE;;AAErB;AACA;AACA;;;;"}