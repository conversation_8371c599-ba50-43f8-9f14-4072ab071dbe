(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))s(a);new MutationObserver(a=>{for(const c of a)if(c.type==="childList")for(const d of c.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&s(d)}).observe(document,{childList:!0,subtree:!0});function i(a){const c={};return a.integrity&&(c.integrity=a.integrity),a.referrerPolicy&&(c.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?c.credentials="include":a.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function s(a){if(a.ep)return;a.ep=!0;const c=i(a);fetch(a.href,c)}})();function ju(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var xc={exports:{}},ws={},Ac={exports:{}},Me={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jp;function O_(){if(Jp)return Me;Jp=1;var n=Symbol.for("react.element"),e=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),d=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),_=Symbol.for("react.memo"),w=Symbol.for("react.lazy"),E=Symbol.iterator;function x(O){return O===null||typeof O!="object"?null:(O=E&&O[E]||O["@@iterator"],typeof O=="function"?O:null)}var T={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},P=Object.assign,L={};function F(O,V,u){this.props=O,this.context=V,this.refs=L,this.updater=u||T}F.prototype.isReactComponent={},F.prototype.setState=function(O,V){if(typeof O!="object"&&typeof O!="function"&&O!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,O,V,"setState")},F.prototype.forceUpdate=function(O){this.updater.enqueueForceUpdate(this,O,"forceUpdate")};function Y(){}Y.prototype=F.prototype;function ne(O,V,u){this.props=O,this.context=V,this.refs=L,this.updater=u||T}var re=ne.prototype=new Y;re.constructor=ne,P(re,F.prototype),re.isPureReactComponent=!0;var B=Array.isArray,I=Object.prototype.hasOwnProperty,U={current:null},R={key:!0,ref:!0,__self:!0,__source:!0};function z(O,V,u){var g,v={},S=null,k=null;if(V!=null)for(g in V.ref!==void 0&&(k=V.ref),V.key!==void 0&&(S=""+V.key),V)I.call(V,g)&&!R.hasOwnProperty(g)&&(v[g]=V[g]);var N=arguments.length-2;if(N===1)v.children=u;else if(1<N){for(var J=Array(N),K=0;K<N;K++)J[K]=arguments[K+2];v.children=J}if(O&&O.defaultProps)for(g in N=O.defaultProps,N)v[g]===void 0&&(v[g]=N[g]);return{$$typeof:n,type:O,key:S,ref:k,props:v,_owner:U.current}}function X(O,V){return{$$typeof:n,type:O.type,key:V,ref:O.ref,props:O.props,_owner:O._owner}}function $(O){return typeof O=="object"&&O!==null&&O.$$typeof===n}function te(O){var V={"=":"=0",":":"=2"};return"$"+O.replace(/[=:]/g,function(u){return V[u]})}var se=/\/+/g;function pe(O,V){return typeof O=="object"&&O!==null&&O.key!=null?te(""+O.key):V.toString(36)}function _e(O,V,u,g,v){var S=typeof O;(S==="undefined"||S==="boolean")&&(O=null);var k=!1;if(O===null)k=!0;else switch(S){case"string":case"number":k=!0;break;case"object":switch(O.$$typeof){case n:case e:k=!0}}if(k)return k=O,v=v(k),O=g===""?"."+pe(k,0):g,B(v)?(u="",O!=null&&(u=O.replace(se,"$&/")+"/"),_e(v,V,u,"",function(K){return K})):v!=null&&($(v)&&(v=X(v,u+(!v.key||k&&k.key===v.key?"":(""+v.key).replace(se,"$&/")+"/")+O)),V.push(v)),1;if(k=0,g=g===""?".":g+":",B(O))for(var N=0;N<O.length;N++){S=O[N];var J=g+pe(S,N);k+=_e(S,V,u,J,v)}else if(J=x(O),typeof J=="function")for(O=J.call(O),N=0;!(S=O.next()).done;)S=S.value,J=g+pe(S,N++),k+=_e(S,V,u,J,v);else if(S==="object")throw V=String(O),Error("Objects are not valid as a React child (found: "+(V==="[object Object]"?"object with keys {"+Object.keys(O).join(", ")+"}":V)+"). If you meant to render a collection of children, use an array instead.");return k}function Oe(O,V,u){if(O==null)return O;var g=[],v=0;return _e(O,g,"","",function(S){return V.call(u,S,v++)}),g}function Re(O){if(O._status===-1){var V=O._result;V=V(),V.then(function(u){(O._status===0||O._status===-1)&&(O._status=1,O._result=u)},function(u){(O._status===0||O._status===-1)&&(O._status=2,O._result=u)}),O._status===-1&&(O._status=0,O._result=V)}if(O._status===1)return O._result.default;throw O._result}var he={current:null},Z={transition:null},ae={ReactCurrentDispatcher:he,ReactCurrentBatchConfig:Z,ReactCurrentOwner:U};function ie(){throw Error("act(...) is not supported in production builds of React.")}return Me.Children={map:Oe,forEach:function(O,V,u){Oe(O,function(){V.apply(this,arguments)},u)},count:function(O){var V=0;return Oe(O,function(){V++}),V},toArray:function(O){return Oe(O,function(V){return V})||[]},only:function(O){if(!$(O))throw Error("React.Children.only expected to receive a single React element child.");return O}},Me.Component=F,Me.Fragment=i,Me.Profiler=a,Me.PureComponent=ne,Me.StrictMode=s,Me.Suspense=m,Me.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ae,Me.act=ie,Me.cloneElement=function(O,V,u){if(O==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+O+".");var g=P({},O.props),v=O.key,S=O.ref,k=O._owner;if(V!=null){if(V.ref!==void 0&&(S=V.ref,k=U.current),V.key!==void 0&&(v=""+V.key),O.type&&O.type.defaultProps)var N=O.type.defaultProps;for(J in V)I.call(V,J)&&!R.hasOwnProperty(J)&&(g[J]=V[J]===void 0&&N!==void 0?N[J]:V[J])}var J=arguments.length-2;if(J===1)g.children=u;else if(1<J){N=Array(J);for(var K=0;K<J;K++)N[K]=arguments[K+2];g.children=N}return{$$typeof:n,type:O.type,key:v,ref:S,props:g,_owner:k}},Me.createContext=function(O){return O={$$typeof:d,_currentValue:O,_currentValue2:O,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},O.Provider={$$typeof:c,_context:O},O.Consumer=O},Me.createElement=z,Me.createFactory=function(O){var V=z.bind(null,O);return V.type=O,V},Me.createRef=function(){return{current:null}},Me.forwardRef=function(O){return{$$typeof:h,render:O}},Me.isValidElement=$,Me.lazy=function(O){return{$$typeof:w,_payload:{_status:-1,_result:O},_init:Re}},Me.memo=function(O,V){return{$$typeof:_,type:O,compare:V===void 0?null:V}},Me.startTransition=function(O){var V=Z.transition;Z.transition={};try{O()}finally{Z.transition=V}},Me.unstable_act=ie,Me.useCallback=function(O,V){return he.current.useCallback(O,V)},Me.useContext=function(O){return he.current.useContext(O)},Me.useDebugValue=function(){},Me.useDeferredValue=function(O){return he.current.useDeferredValue(O)},Me.useEffect=function(O,V){return he.current.useEffect(O,V)},Me.useId=function(){return he.current.useId()},Me.useImperativeHandle=function(O,V,u){return he.current.useImperativeHandle(O,V,u)},Me.useInsertionEffect=function(O,V){return he.current.useInsertionEffect(O,V)},Me.useLayoutEffect=function(O,V){return he.current.useLayoutEffect(O,V)},Me.useMemo=function(O,V){return he.current.useMemo(O,V)},Me.useReducer=function(O,V,u){return he.current.useReducer(O,V,u)},Me.useRef=function(O){return he.current.useRef(O)},Me.useState=function(O){return he.current.useState(O)},Me.useSyncExternalStore=function(O,V,u){return he.current.useSyncExternalStore(O,V,u)},Me.useTransition=function(){return he.current.useTransition()},Me.version="18.3.1",Me}var Hp;function Ru(){return Hp||(Hp=1,Ac.exports=O_()),Ac.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qp;function x_(){if(Qp)return ws;Qp=1;var n=Ru(),e=Symbol.for("react.element"),i=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,a=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function d(h,m,_){var w,E={},x=null,T=null;_!==void 0&&(x=""+_),m.key!==void 0&&(x=""+m.key),m.ref!==void 0&&(T=m.ref);for(w in m)s.call(m,w)&&!c.hasOwnProperty(w)&&(E[w]=m[w]);if(h&&h.defaultProps)for(w in m=h.defaultProps,m)E[w]===void 0&&(E[w]=m[w]);return{$$typeof:e,type:h,key:x,ref:T,props:E,_owner:a.current}}return ws.Fragment=i,ws.jsx=d,ws.jsxs=d,ws}var Gp;function A_(){return Gp||(Gp=1,xc.exports=x_()),xc.exports}var Le=A_(),ke=Ru();const Pa=ju(ke);var Ho={},Lc={exports:{}},Nt={},Nc={exports:{}},jc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Kp;function L_(){return Kp||(Kp=1,function(n){function e(Z,ae){var ie=Z.length;Z.push(ae);e:for(;0<ie;){var O=ie-1>>>1,V=Z[O];if(0<a(V,ae))Z[O]=ae,Z[ie]=V,ie=O;else break e}}function i(Z){return Z.length===0?null:Z[0]}function s(Z){if(Z.length===0)return null;var ae=Z[0],ie=Z.pop();if(ie!==ae){Z[0]=ie;e:for(var O=0,V=Z.length,u=V>>>1;O<u;){var g=2*(O+1)-1,v=Z[g],S=g+1,k=Z[S];if(0>a(v,ie))S<V&&0>a(k,v)?(Z[O]=k,Z[S]=ie,O=S):(Z[O]=v,Z[g]=ie,O=g);else if(S<V&&0>a(k,ie))Z[O]=k,Z[S]=ie,O=S;else break e}}return ae}function a(Z,ae){var ie=Z.sortIndex-ae.sortIndex;return ie!==0?ie:Z.id-ae.id}if(typeof performance=="object"&&typeof performance.now=="function"){var c=performance;n.unstable_now=function(){return c.now()}}else{var d=Date,h=d.now();n.unstable_now=function(){return d.now()-h}}var m=[],_=[],w=1,E=null,x=3,T=!1,P=!1,L=!1,F=typeof setTimeout=="function"?setTimeout:null,Y=typeof clearTimeout=="function"?clearTimeout:null,ne=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function re(Z){for(var ae=i(_);ae!==null;){if(ae.callback===null)s(_);else if(ae.startTime<=Z)s(_),ae.sortIndex=ae.expirationTime,e(m,ae);else break;ae=i(_)}}function B(Z){if(L=!1,re(Z),!P)if(i(m)!==null)P=!0,Re(I);else{var ae=i(_);ae!==null&&he(B,ae.startTime-Z)}}function I(Z,ae){P=!1,L&&(L=!1,Y(z),z=-1),T=!0;var ie=x;try{for(re(ae),E=i(m);E!==null&&(!(E.expirationTime>ae)||Z&&!te());){var O=E.callback;if(typeof O=="function"){E.callback=null,x=E.priorityLevel;var V=O(E.expirationTime<=ae);ae=n.unstable_now(),typeof V=="function"?E.callback=V:E===i(m)&&s(m),re(ae)}else s(m);E=i(m)}if(E!==null)var u=!0;else{var g=i(_);g!==null&&he(B,g.startTime-ae),u=!1}return u}finally{E=null,x=ie,T=!1}}var U=!1,R=null,z=-1,X=5,$=-1;function te(){return!(n.unstable_now()-$<X)}function se(){if(R!==null){var Z=n.unstable_now();$=Z;var ae=!0;try{ae=R(!0,Z)}finally{ae?pe():(U=!1,R=null)}}else U=!1}var pe;if(typeof ne=="function")pe=function(){ne(se)};else if(typeof MessageChannel<"u"){var _e=new MessageChannel,Oe=_e.port2;_e.port1.onmessage=se,pe=function(){Oe.postMessage(null)}}else pe=function(){F(se,0)};function Re(Z){R=Z,U||(U=!0,pe())}function he(Z,ae){z=F(function(){Z(n.unstable_now())},ae)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(Z){Z.callback=null},n.unstable_continueExecution=function(){P||T||(P=!0,Re(I))},n.unstable_forceFrameRate=function(Z){0>Z||125<Z?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):X=0<Z?Math.floor(1e3/Z):5},n.unstable_getCurrentPriorityLevel=function(){return x},n.unstable_getFirstCallbackNode=function(){return i(m)},n.unstable_next=function(Z){switch(x){case 1:case 2:case 3:var ae=3;break;default:ae=x}var ie=x;x=ae;try{return Z()}finally{x=ie}},n.unstable_pauseExecution=function(){},n.unstable_requestPaint=function(){},n.unstable_runWithPriority=function(Z,ae){switch(Z){case 1:case 2:case 3:case 4:case 5:break;default:Z=3}var ie=x;x=Z;try{return ae()}finally{x=ie}},n.unstable_scheduleCallback=function(Z,ae,ie){var O=n.unstable_now();switch(typeof ie=="object"&&ie!==null?(ie=ie.delay,ie=typeof ie=="number"&&0<ie?O+ie:O):ie=O,Z){case 1:var V=-1;break;case 2:V=250;break;case 5:V=**********;break;case 4:V=1e4;break;default:V=5e3}return V=ie+V,Z={id:w++,callback:ae,priorityLevel:Z,startTime:ie,expirationTime:V,sortIndex:-1},ie>O?(Z.sortIndex=ie,e(_,Z),i(m)===null&&Z===i(_)&&(L?(Y(z),z=-1):L=!0,he(B,ie-O))):(Z.sortIndex=V,e(m,Z),P||T||(P=!0,Re(I))),Z},n.unstable_shouldYield=te,n.unstable_wrapCallback=function(Z){var ae=x;return function(){var ie=x;x=ae;try{return Z.apply(this,arguments)}finally{x=ie}}}}(jc)),jc}var Yp;function N_(){return Yp||(Yp=1,Nc.exports=L_()),Nc.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xp;function j_(){if(Xp)return Nt;Xp=1;var n=Ru(),e=N_();function i(t){for(var r="https://reactjs.org/docs/error-decoder.html?invariant="+t,o=1;o<arguments.length;o++)r+="&args[]="+encodeURIComponent(arguments[o]);return"Minified React error #"+t+"; visit "+r+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var s=new Set,a={};function c(t,r){d(t,r),d(t+"Capture",r)}function d(t,r){for(a[t]=r,t=0;t<r.length;t++)s.add(r[t])}var h=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),m=Object.prototype.hasOwnProperty,_=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,w={},E={};function x(t){return m.call(E,t)?!0:m.call(w,t)?!1:_.test(t)?E[t]=!0:(w[t]=!0,!1)}function T(t,r,o,l){if(o!==null&&o.type===0)return!1;switch(typeof r){case"function":case"symbol":return!0;case"boolean":return l?!1:o!==null?!o.acceptsBooleans:(t=t.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-");default:return!1}}function P(t,r,o,l){if(r===null||typeof r>"u"||T(t,r,o,l))return!0;if(l)return!1;if(o!==null)switch(o.type){case 3:return!r;case 4:return r===!1;case 5:return isNaN(r);case 6:return isNaN(r)||1>r}return!1}function L(t,r,o,l,f,p,y){this.acceptsBooleans=r===2||r===3||r===4,this.attributeName=l,this.attributeNamespace=f,this.mustUseProperty=o,this.propertyName=t,this.type=r,this.sanitizeURL=p,this.removeEmptyString=y}var F={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(t){F[t]=new L(t,0,!1,t,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(t){var r=t[0];F[r]=new L(r,1,!1,t[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(t){F[t]=new L(t,2,!1,t.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(t){F[t]=new L(t,2,!1,t,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(t){F[t]=new L(t,3,!1,t.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(t){F[t]=new L(t,3,!0,t,null,!1,!1)}),["capture","download"].forEach(function(t){F[t]=new L(t,4,!1,t,null,!1,!1)}),["cols","rows","size","span"].forEach(function(t){F[t]=new L(t,6,!1,t,null,!1,!1)}),["rowSpan","start"].forEach(function(t){F[t]=new L(t,5,!1,t.toLowerCase(),null,!1,!1)});var Y=/[\-:]([a-z])/g;function ne(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(t){var r=t.replace(Y,ne);F[r]=new L(r,1,!1,t,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(t){var r=t.replace(Y,ne);F[r]=new L(r,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(t){var r=t.replace(Y,ne);F[r]=new L(r,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(t){F[t]=new L(t,1,!1,t.toLowerCase(),null,!1,!1)}),F.xlinkHref=new L("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(t){F[t]=new L(t,1,!1,t.toLowerCase(),null,!0,!0)});function re(t,r,o,l){var f=F.hasOwnProperty(r)?F[r]:null;(f!==null?f.type!==0:l||!(2<r.length)||r[0]!=="o"&&r[0]!=="O"||r[1]!=="n"&&r[1]!=="N")&&(P(r,o,f,l)&&(o=null),l||f===null?x(r)&&(o===null?t.removeAttribute(r):t.setAttribute(r,""+o)):f.mustUseProperty?t[f.propertyName]=o===null?f.type===3?!1:"":o:(r=f.attributeName,l=f.attributeNamespace,o===null?t.removeAttribute(r):(f=f.type,o=f===3||f===4&&o===!0?"":""+o,l?t.setAttributeNS(l,r,o):t.setAttribute(r,o))))}var B=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,I=Symbol.for("react.element"),U=Symbol.for("react.portal"),R=Symbol.for("react.fragment"),z=Symbol.for("react.strict_mode"),X=Symbol.for("react.profiler"),$=Symbol.for("react.provider"),te=Symbol.for("react.context"),se=Symbol.for("react.forward_ref"),pe=Symbol.for("react.suspense"),_e=Symbol.for("react.suspense_list"),Oe=Symbol.for("react.memo"),Re=Symbol.for("react.lazy"),he=Symbol.for("react.offscreen"),Z=Symbol.iterator;function ae(t){return t===null||typeof t!="object"?null:(t=Z&&t[Z]||t["@@iterator"],typeof t=="function"?t:null)}var ie=Object.assign,O;function V(t){if(O===void 0)try{throw Error()}catch(o){var r=o.stack.trim().match(/\n( *(at )?)/);O=r&&r[1]||""}return`
`+O+t}var u=!1;function g(t,r){if(!t||u)return"";u=!0;var o=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(r)if(r=function(){throw Error()},Object.defineProperty(r.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(r,[])}catch(D){var l=D}Reflect.construct(t,[],r)}else{try{r.call()}catch(D){l=D}t.call(r.prototype)}else{try{throw Error()}catch(D){l=D}t()}}catch(D){if(D&&l&&typeof D.stack=="string"){for(var f=D.stack.split(`
`),p=l.stack.split(`
`),y=f.length-1,b=p.length-1;1<=y&&0<=b&&f[y]!==p[b];)b--;for(;1<=y&&0<=b;y--,b--)if(f[y]!==p[b]){if(y!==1||b!==1)do if(y--,b--,0>b||f[y]!==p[b]){var C=`
`+f[y].replace(" at new "," at ");return t.displayName&&C.includes("<anonymous>")&&(C=C.replace("<anonymous>",t.displayName)),C}while(1<=y&&0<=b);break}}}finally{u=!1,Error.prepareStackTrace=o}return(t=t?t.displayName||t.name:"")?V(t):""}function v(t){switch(t.tag){case 5:return V(t.type);case 16:return V("Lazy");case 13:return V("Suspense");case 19:return V("SuspenseList");case 0:case 2:case 15:return t=g(t.type,!1),t;case 11:return t=g(t.type.render,!1),t;case 1:return t=g(t.type,!0),t;default:return""}}function S(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case R:return"Fragment";case U:return"Portal";case X:return"Profiler";case z:return"StrictMode";case pe:return"Suspense";case _e:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case te:return(t.displayName||"Context")+".Consumer";case $:return(t._context.displayName||"Context")+".Provider";case se:var r=t.render;return t=t.displayName,t||(t=r.displayName||r.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Oe:return r=t.displayName||null,r!==null?r:S(t.type)||"Memo";case Re:r=t._payload,t=t._init;try{return S(t(r))}catch{}}return null}function k(t){var r=t.type;switch(t.tag){case 24:return"Cache";case 9:return(r.displayName||"Context")+".Consumer";case 10:return(r._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=r.render,t=t.displayName||t.name||"",r.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return r;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return S(r);case 8:return r===z?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof r=="function")return r.displayName||r.name||null;if(typeof r=="string")return r}return null}function N(t){switch(typeof t){case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function J(t){var r=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(r==="checkbox"||r==="radio")}function K(t){var r=J(t)?"checked":"value",o=Object.getOwnPropertyDescriptor(t.constructor.prototype,r),l=""+t[r];if(!t.hasOwnProperty(r)&&typeof o<"u"&&typeof o.get=="function"&&typeof o.set=="function"){var f=o.get,p=o.set;return Object.defineProperty(t,r,{configurable:!0,get:function(){return f.call(this)},set:function(y){l=""+y,p.call(this,y)}}),Object.defineProperty(t,r,{enumerable:o.enumerable}),{getValue:function(){return l},setValue:function(y){l=""+y},stopTracking:function(){t._valueTracker=null,delete t[r]}}}}function ce(t){t._valueTracker||(t._valueTracker=K(t))}function Ee(t){if(!t)return!1;var r=t._valueTracker;if(!r)return!0;var o=r.getValue(),l="";return t&&(l=J(t)?t.checked?"true":"false":t.value),t=l,t!==o?(r.setValue(t),!0):!1}function Ce(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}function Tt(t,r){var o=r.checked;return ie({},r,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:o??t._wrapperState.initialChecked})}function yn(t,r){var o=r.defaultValue==null?"":r.defaultValue,l=r.checked!=null?r.checked:r.defaultChecked;o=N(r.value!=null?r.value:o),t._wrapperState={initialChecked:l,initialValue:o,controlled:r.type==="checkbox"||r.type==="radio"?r.checked!=null:r.value!=null}}function Un(t,r){r=r.checked,r!=null&&re(t,"checked",r,!1)}function Ke(t,r){Un(t,r);var o=N(r.value),l=r.type;if(o!=null)l==="number"?(o===0&&t.value===""||t.value!=o)&&(t.value=""+o):t.value!==""+o&&(t.value=""+o);else if(l==="submit"||l==="reset"){t.removeAttribute("value");return}r.hasOwnProperty("value")?tn(t,r.type,o):r.hasOwnProperty("defaultValue")&&tn(t,r.type,N(r.defaultValue)),r.checked==null&&r.defaultChecked!=null&&(t.defaultChecked=!!r.defaultChecked)}function _t(t,r,o){if(r.hasOwnProperty("value")||r.hasOwnProperty("defaultValue")){var l=r.type;if(!(l!=="submit"&&l!=="reset"||r.value!==void 0&&r.value!==null))return;r=""+t._wrapperState.initialValue,o||r===t.value||(t.value=r),t.defaultValue=r}o=t.name,o!==""&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,o!==""&&(t.name=o)}function tn(t,r,o){(r!=="number"||Ce(t.ownerDocument)!==t)&&(o==null?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+o&&(t.defaultValue=""+o))}var nn=Array.isArray;function rn(t,r,o,l){if(t=t.options,r){r={};for(var f=0;f<o.length;f++)r["$"+o[f]]=!0;for(o=0;o<t.length;o++)f=r.hasOwnProperty("$"+t[o].value),t[o].selected!==f&&(t[o].selected=f),f&&l&&(t[o].defaultSelected=!0)}else{for(o=""+N(o),r=null,f=0;f<t.length;f++){if(t[f].value===o){t[f].selected=!0,l&&(t[f].defaultSelected=!0);return}r!==null||t[f].disabled||(r=t[f])}r!==null&&(r.selected=!0)}}function Tn(t,r){if(r.dangerouslySetInnerHTML!=null)throw Error(i(91));return ie({},r,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function zn(t,r){var o=r.value;if(o==null){if(o=r.children,r=r.defaultValue,o!=null){if(r!=null)throw Error(i(92));if(nn(o)){if(1<o.length)throw Error(i(93));o=o[0]}r=o}r==null&&(r=""),o=r}t._wrapperState={initialValue:N(o)}}function Ri(t,r){var o=N(r.value),l=N(r.defaultValue);o!=null&&(o=""+o,o!==t.value&&(t.value=o),r.defaultValue==null&&t.defaultValue!==o&&(t.defaultValue=o)),l!=null&&(t.defaultValue=""+l)}function Ds(t){var r=t.textContent;r===t._wrapperState.initialValue&&r!==""&&r!==null&&(t.value=r)}function Is(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ua(t,r){return t==null||t==="http://www.w3.org/1999/xhtml"?Is(r):t==="http://www.w3.org/2000/svg"&&r==="foreignObject"?"http://www.w3.org/1999/xhtml":t}var Fs,sd=function(t){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(r,o,l,f){MSApp.execUnsafeLocalFunction(function(){return t(r,o,l,f)})}:t}(function(t,r){if(t.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in t)t.innerHTML=r;else{for(Fs=Fs||document.createElement("div"),Fs.innerHTML="<svg>"+r.valueOf().toString()+"</svg>",r=Fs.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;r.firstChild;)t.appendChild(r.firstChild)}});function Di(t,r){if(r){var o=t.firstChild;if(o&&o===t.lastChild&&o.nodeType===3){o.nodeValue=r;return}}t.textContent=r}var Ii={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Ng=["Webkit","ms","Moz","O"];Object.keys(Ii).forEach(function(t){Ng.forEach(function(r){r=r+t.charAt(0).toUpperCase()+t.substring(1),Ii[r]=Ii[t]})});function od(t,r,o){return r==null||typeof r=="boolean"||r===""?"":o||typeof r!="number"||r===0||Ii.hasOwnProperty(t)&&Ii[t]?(""+r).trim():r+"px"}function ad(t,r){t=t.style;for(var o in r)if(r.hasOwnProperty(o)){var l=o.indexOf("--")===0,f=od(o,r[o],l);o==="float"&&(o="cssFloat"),l?t.setProperty(o,f):t[o]=f}}var jg=ie({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function za(t,r){if(r){if(jg[t]&&(r.children!=null||r.dangerouslySetInnerHTML!=null))throw Error(i(137,t));if(r.dangerouslySetInnerHTML!=null){if(r.children!=null)throw Error(i(60));if(typeof r.dangerouslySetInnerHTML!="object"||!("__html"in r.dangerouslySetInnerHTML))throw Error(i(61))}if(r.style!=null&&typeof r.style!="object")throw Error(i(62))}}function Va(t,r){if(t.indexOf("-")===-1)return typeof r.is=="string";switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Wa=null;function qa(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Ja=null,Gr=null,Kr=null;function ld(t){if(t=ss(t)){if(typeof Ja!="function")throw Error(i(280));var r=t.stateNode;r&&(r=ao(r),Ja(t.stateNode,t.type,r))}}function cd(t){Gr?Kr?Kr.push(t):Kr=[t]:Gr=t}function ud(){if(Gr){var t=Gr,r=Kr;if(Kr=Gr=null,ld(t),r)for(t=0;t<r.length;t++)ld(r[t])}}function dd(t,r){return t(r)}function fd(){}var Ha=!1;function pd(t,r,o){if(Ha)return t(r,o);Ha=!0;try{return dd(t,r,o)}finally{Ha=!1,(Gr!==null||Kr!==null)&&(fd(),ud())}}function Fi(t,r){var o=t.stateNode;if(o===null)return null;var l=ao(o);if(l===null)return null;o=l[r];e:switch(r){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break e;default:t=!1}if(t)return null;if(o&&typeof o!="function")throw Error(i(231,r,typeof o));return o}var Qa=!1;if(h)try{var Bi={};Object.defineProperty(Bi,"passive",{get:function(){Qa=!0}}),window.addEventListener("test",Bi,Bi),window.removeEventListener("test",Bi,Bi)}catch{Qa=!1}function Rg(t,r,o,l,f,p,y,b,C){var D=Array.prototype.slice.call(arguments,3);try{r.apply(o,D)}catch(Q){this.onError(Q)}}var $i=!1,Bs=null,$s=!1,Ga=null,Dg={onError:function(t){$i=!0,Bs=t}};function Ig(t,r,o,l,f,p,y,b,C){$i=!1,Bs=null,Rg.apply(Dg,arguments)}function Fg(t,r,o,l,f,p,y,b,C){if(Ig.apply(this,arguments),$i){if($i){var D=Bs;$i=!1,Bs=null}else throw Error(i(198));$s||($s=!0,Ga=D)}}function _r(t){var r=t,o=t;if(t.alternate)for(;r.return;)r=r.return;else{t=r;do r=t,(r.flags&4098)!==0&&(o=r.return),t=r.return;while(t)}return r.tag===3?o:null}function hd(t){if(t.tag===13){var r=t.memoizedState;if(r===null&&(t=t.alternate,t!==null&&(r=t.memoizedState)),r!==null)return r.dehydrated}return null}function md(t){if(_r(t)!==t)throw Error(i(188))}function Bg(t){var r=t.alternate;if(!r){if(r=_r(t),r===null)throw Error(i(188));return r!==t?null:t}for(var o=t,l=r;;){var f=o.return;if(f===null)break;var p=f.alternate;if(p===null){if(l=f.return,l!==null){o=l;continue}break}if(f.child===p.child){for(p=f.child;p;){if(p===o)return md(f),t;if(p===l)return md(f),r;p=p.sibling}throw Error(i(188))}if(o.return!==l.return)o=f,l=p;else{for(var y=!1,b=f.child;b;){if(b===o){y=!0,o=f,l=p;break}if(b===l){y=!0,l=f,o=p;break}b=b.sibling}if(!y){for(b=p.child;b;){if(b===o){y=!0,o=p,l=f;break}if(b===l){y=!0,l=p,o=f;break}b=b.sibling}if(!y)throw Error(i(189))}}if(o.alternate!==l)throw Error(i(190))}if(o.tag!==3)throw Error(i(188));return o.stateNode.current===o?t:r}function vd(t){return t=Bg(t),t!==null?gd(t):null}function gd(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var r=gd(t);if(r!==null)return r;t=t.sibling}return null}var yd=e.unstable_scheduleCallback,_d=e.unstable_cancelCallback,$g=e.unstable_shouldYield,Ug=e.unstable_requestPaint,Xe=e.unstable_now,zg=e.unstable_getCurrentPriorityLevel,Ka=e.unstable_ImmediatePriority,wd=e.unstable_UserBlockingPriority,Us=e.unstable_NormalPriority,Vg=e.unstable_LowPriority,Sd=e.unstable_IdlePriority,zs=null,_n=null;function Wg(t){if(_n&&typeof _n.onCommitFiberRoot=="function")try{_n.onCommitFiberRoot(zs,t,void 0,(t.current.flags&128)===128)}catch{}}var sn=Math.clz32?Math.clz32:Hg,qg=Math.log,Jg=Math.LN2;function Hg(t){return t>>>=0,t===0?32:31-(qg(t)/Jg|0)|0}var Vs=64,Ws=4194304;function Ui(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function qs(t,r){var o=t.pendingLanes;if(o===0)return 0;var l=0,f=t.suspendedLanes,p=t.pingedLanes,y=o&268435455;if(y!==0){var b=y&~f;b!==0?l=Ui(b):(p&=y,p!==0&&(l=Ui(p)))}else y=o&~f,y!==0?l=Ui(y):p!==0&&(l=Ui(p));if(l===0)return 0;if(r!==0&&r!==l&&(r&f)===0&&(f=l&-l,p=r&-r,f>=p||f===16&&(p&4194240)!==0))return r;if((l&4)!==0&&(l|=o&16),r=t.entangledLanes,r!==0)for(t=t.entanglements,r&=l;0<r;)o=31-sn(r),f=1<<o,l|=t[o],r&=~f;return l}function Qg(t,r){switch(t){case 1:case 2:case 4:return r+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return r+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Gg(t,r){for(var o=t.suspendedLanes,l=t.pingedLanes,f=t.expirationTimes,p=t.pendingLanes;0<p;){var y=31-sn(p),b=1<<y,C=f[y];C===-1?((b&o)===0||(b&l)!==0)&&(f[y]=Qg(b,r)):C<=r&&(t.expiredLanes|=b),p&=~b}}function Ya(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function kd(){var t=Vs;return Vs<<=1,(Vs&4194240)===0&&(Vs=64),t}function Xa(t){for(var r=[],o=0;31>o;o++)r.push(t);return r}function zi(t,r,o){t.pendingLanes|=r,r!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,r=31-sn(r),t[r]=o}function Kg(t,r){var o=t.pendingLanes&~r;t.pendingLanes=r,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=r,t.mutableReadLanes&=r,t.entangledLanes&=r,r=t.entanglements;var l=t.eventTimes;for(t=t.expirationTimes;0<o;){var f=31-sn(o),p=1<<f;r[f]=0,l[f]=-1,t[f]=-1,o&=~p}}function Za(t,r){var o=t.entangledLanes|=r;for(t=t.entanglements;o;){var l=31-sn(o),f=1<<l;f&r|t[l]&r&&(t[l]|=r),o&=~f}}var Ie=0;function bd(t){return t&=-t,1<t?4<t?(t&268435455)!==0?16:536870912:4:1}var Ed,el,Cd,Md,Td,tl=!1,Js=[],Vn=null,Wn=null,qn=null,Vi=new Map,Wi=new Map,Jn=[],Yg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Pd(t,r){switch(t){case"focusin":case"focusout":Vn=null;break;case"dragenter":case"dragleave":Wn=null;break;case"mouseover":case"mouseout":qn=null;break;case"pointerover":case"pointerout":Vi.delete(r.pointerId);break;case"gotpointercapture":case"lostpointercapture":Wi.delete(r.pointerId)}}function qi(t,r,o,l,f,p){return t===null||t.nativeEvent!==p?(t={blockedOn:r,domEventName:o,eventSystemFlags:l,nativeEvent:p,targetContainers:[f]},r!==null&&(r=ss(r),r!==null&&el(r)),t):(t.eventSystemFlags|=l,r=t.targetContainers,f!==null&&r.indexOf(f)===-1&&r.push(f),t)}function Xg(t,r,o,l,f){switch(r){case"focusin":return Vn=qi(Vn,t,r,o,l,f),!0;case"dragenter":return Wn=qi(Wn,t,r,o,l,f),!0;case"mouseover":return qn=qi(qn,t,r,o,l,f),!0;case"pointerover":var p=f.pointerId;return Vi.set(p,qi(Vi.get(p)||null,t,r,o,l,f)),!0;case"gotpointercapture":return p=f.pointerId,Wi.set(p,qi(Wi.get(p)||null,t,r,o,l,f)),!0}return!1}function Od(t){var r=wr(t.target);if(r!==null){var o=_r(r);if(o!==null){if(r=o.tag,r===13){if(r=hd(o),r!==null){t.blockedOn=r,Td(t.priority,function(){Cd(o)});return}}else if(r===3&&o.stateNode.current.memoizedState.isDehydrated){t.blockedOn=o.tag===3?o.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Hs(t){if(t.blockedOn!==null)return!1;for(var r=t.targetContainers;0<r.length;){var o=rl(t.domEventName,t.eventSystemFlags,r[0],t.nativeEvent);if(o===null){o=t.nativeEvent;var l=new o.constructor(o.type,o);Wa=l,o.target.dispatchEvent(l),Wa=null}else return r=ss(o),r!==null&&el(r),t.blockedOn=o,!1;r.shift()}return!0}function xd(t,r,o){Hs(t)&&o.delete(r)}function Zg(){tl=!1,Vn!==null&&Hs(Vn)&&(Vn=null),Wn!==null&&Hs(Wn)&&(Wn=null),qn!==null&&Hs(qn)&&(qn=null),Vi.forEach(xd),Wi.forEach(xd)}function Ji(t,r){t.blockedOn===r&&(t.blockedOn=null,tl||(tl=!0,e.unstable_scheduleCallback(e.unstable_NormalPriority,Zg)))}function Hi(t){function r(f){return Ji(f,t)}if(0<Js.length){Ji(Js[0],t);for(var o=1;o<Js.length;o++){var l=Js[o];l.blockedOn===t&&(l.blockedOn=null)}}for(Vn!==null&&Ji(Vn,t),Wn!==null&&Ji(Wn,t),qn!==null&&Ji(qn,t),Vi.forEach(r),Wi.forEach(r),o=0;o<Jn.length;o++)l=Jn[o],l.blockedOn===t&&(l.blockedOn=null);for(;0<Jn.length&&(o=Jn[0],o.blockedOn===null);)Od(o),o.blockedOn===null&&Jn.shift()}var Yr=B.ReactCurrentBatchConfig,Qs=!0;function ey(t,r,o,l){var f=Ie,p=Yr.transition;Yr.transition=null;try{Ie=1,nl(t,r,o,l)}finally{Ie=f,Yr.transition=p}}function ty(t,r,o,l){var f=Ie,p=Yr.transition;Yr.transition=null;try{Ie=4,nl(t,r,o,l)}finally{Ie=f,Yr.transition=p}}function nl(t,r,o,l){if(Qs){var f=rl(t,r,o,l);if(f===null)wl(t,r,l,Gs,o),Pd(t,l);else if(Xg(f,t,r,o,l))l.stopPropagation();else if(Pd(t,l),r&4&&-1<Yg.indexOf(t)){for(;f!==null;){var p=ss(f);if(p!==null&&Ed(p),p=rl(t,r,o,l),p===null&&wl(t,r,l,Gs,o),p===f)break;f=p}f!==null&&l.stopPropagation()}else wl(t,r,l,null,o)}}var Gs=null;function rl(t,r,o,l){if(Gs=null,t=qa(l),t=wr(t),t!==null)if(r=_r(t),r===null)t=null;else if(o=r.tag,o===13){if(t=hd(r),t!==null)return t;t=null}else if(o===3){if(r.stateNode.current.memoizedState.isDehydrated)return r.tag===3?r.stateNode.containerInfo:null;t=null}else r!==t&&(t=null);return Gs=t,null}function Ad(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(zg()){case Ka:return 1;case wd:return 4;case Us:case Vg:return 16;case Sd:return 536870912;default:return 16}default:return 16}}var Hn=null,il=null,Ks=null;function Ld(){if(Ks)return Ks;var t,r=il,o=r.length,l,f="value"in Hn?Hn.value:Hn.textContent,p=f.length;for(t=0;t<o&&r[t]===f[t];t++);var y=o-t;for(l=1;l<=y&&r[o-l]===f[p-l];l++);return Ks=f.slice(t,1<l?1-l:void 0)}function Ys(t){var r=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&r===13&&(t=13)):t=r,t===10&&(t=13),32<=t||t===13?t:0}function Xs(){return!0}function Nd(){return!1}function Rt(t){function r(o,l,f,p,y){this._reactName=o,this._targetInst=f,this.type=l,this.nativeEvent=p,this.target=y,this.currentTarget=null;for(var b in t)t.hasOwnProperty(b)&&(o=t[b],this[b]=o?o(p):p[b]);return this.isDefaultPrevented=(p.defaultPrevented!=null?p.defaultPrevented:p.returnValue===!1)?Xs:Nd,this.isPropagationStopped=Nd,this}return ie(r.prototype,{preventDefault:function(){this.defaultPrevented=!0;var o=this.nativeEvent;o&&(o.preventDefault?o.preventDefault():typeof o.returnValue!="unknown"&&(o.returnValue=!1),this.isDefaultPrevented=Xs)},stopPropagation:function(){var o=this.nativeEvent;o&&(o.stopPropagation?o.stopPropagation():typeof o.cancelBubble!="unknown"&&(o.cancelBubble=!0),this.isPropagationStopped=Xs)},persist:function(){},isPersistent:Xs}),r}var Xr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},sl=Rt(Xr),Qi=ie({},Xr,{view:0,detail:0}),ny=Rt(Qi),ol,al,Gi,Zs=ie({},Qi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:cl,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Gi&&(Gi&&t.type==="mousemove"?(ol=t.screenX-Gi.screenX,al=t.screenY-Gi.screenY):al=ol=0,Gi=t),ol)},movementY:function(t){return"movementY"in t?t.movementY:al}}),jd=Rt(Zs),ry=ie({},Zs,{dataTransfer:0}),iy=Rt(ry),sy=ie({},Qi,{relatedTarget:0}),ll=Rt(sy),oy=ie({},Xr,{animationName:0,elapsedTime:0,pseudoElement:0}),ay=Rt(oy),ly=ie({},Xr,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),cy=Rt(ly),uy=ie({},Xr,{data:0}),Rd=Rt(uy),dy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},fy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},py={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function hy(t){var r=this.nativeEvent;return r.getModifierState?r.getModifierState(t):(t=py[t])?!!r[t]:!1}function cl(){return hy}var my=ie({},Qi,{key:function(t){if(t.key){var r=dy[t.key]||t.key;if(r!=="Unidentified")return r}return t.type==="keypress"?(t=Ys(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?fy[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:cl,charCode:function(t){return t.type==="keypress"?Ys(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Ys(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),vy=Rt(my),gy=ie({},Zs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Dd=Rt(gy),yy=ie({},Qi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:cl}),_y=Rt(yy),wy=ie({},Xr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Sy=Rt(wy),ky=ie({},Zs,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),by=Rt(ky),Ey=[9,13,27,32],ul=h&&"CompositionEvent"in window,Ki=null;h&&"documentMode"in document&&(Ki=document.documentMode);var Cy=h&&"TextEvent"in window&&!Ki,Id=h&&(!ul||Ki&&8<Ki&&11>=Ki),Fd=" ",Bd=!1;function $d(t,r){switch(t){case"keyup":return Ey.indexOf(r.keyCode)!==-1;case"keydown":return r.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ud(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Zr=!1;function My(t,r){switch(t){case"compositionend":return Ud(r);case"keypress":return r.which!==32?null:(Bd=!0,Fd);case"textInput":return t=r.data,t===Fd&&Bd?null:t;default:return null}}function Ty(t,r){if(Zr)return t==="compositionend"||!ul&&$d(t,r)?(t=Ld(),Ks=il=Hn=null,Zr=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(r.ctrlKey||r.altKey||r.metaKey)||r.ctrlKey&&r.altKey){if(r.char&&1<r.char.length)return r.char;if(r.which)return String.fromCharCode(r.which)}return null;case"compositionend":return Id&&r.locale!=="ko"?null:r.data;default:return null}}var Py={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function zd(t){var r=t&&t.nodeName&&t.nodeName.toLowerCase();return r==="input"?!!Py[t.type]:r==="textarea"}function Vd(t,r,o,l){cd(l),r=io(r,"onChange"),0<r.length&&(o=new sl("onChange","change",null,o,l),t.push({event:o,listeners:r}))}var Yi=null,Xi=null;function Oy(t){lf(t,0)}function eo(t){var r=ii(t);if(Ee(r))return t}function xy(t,r){if(t==="change")return r}var Wd=!1;if(h){var dl;if(h){var fl="oninput"in document;if(!fl){var qd=document.createElement("div");qd.setAttribute("oninput","return;"),fl=typeof qd.oninput=="function"}dl=fl}else dl=!1;Wd=dl&&(!document.documentMode||9<document.documentMode)}function Jd(){Yi&&(Yi.detachEvent("onpropertychange",Hd),Xi=Yi=null)}function Hd(t){if(t.propertyName==="value"&&eo(Xi)){var r=[];Vd(r,Xi,t,qa(t)),pd(Oy,r)}}function Ay(t,r,o){t==="focusin"?(Jd(),Yi=r,Xi=o,Yi.attachEvent("onpropertychange",Hd)):t==="focusout"&&Jd()}function Ly(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return eo(Xi)}function Ny(t,r){if(t==="click")return eo(r)}function jy(t,r){if(t==="input"||t==="change")return eo(r)}function Ry(t,r){return t===r&&(t!==0||1/t===1/r)||t!==t&&r!==r}var on=typeof Object.is=="function"?Object.is:Ry;function Zi(t,r){if(on(t,r))return!0;if(typeof t!="object"||t===null||typeof r!="object"||r===null)return!1;var o=Object.keys(t),l=Object.keys(r);if(o.length!==l.length)return!1;for(l=0;l<o.length;l++){var f=o[l];if(!m.call(r,f)||!on(t[f],r[f]))return!1}return!0}function Qd(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Gd(t,r){var o=Qd(t);t=0;for(var l;o;){if(o.nodeType===3){if(l=t+o.textContent.length,t<=r&&l>=r)return{node:o,offset:r-t};t=l}e:{for(;o;){if(o.nextSibling){o=o.nextSibling;break e}o=o.parentNode}o=void 0}o=Qd(o)}}function Kd(t,r){return t&&r?t===r?!0:t&&t.nodeType===3?!1:r&&r.nodeType===3?Kd(t,r.parentNode):"contains"in t?t.contains(r):t.compareDocumentPosition?!!(t.compareDocumentPosition(r)&16):!1:!1}function Yd(){for(var t=window,r=Ce();r instanceof t.HTMLIFrameElement;){try{var o=typeof r.contentWindow.location.href=="string"}catch{o=!1}if(o)t=r.contentWindow;else break;r=Ce(t.document)}return r}function pl(t){var r=t&&t.nodeName&&t.nodeName.toLowerCase();return r&&(r==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||r==="textarea"||t.contentEditable==="true")}function Dy(t){var r=Yd(),o=t.focusedElem,l=t.selectionRange;if(r!==o&&o&&o.ownerDocument&&Kd(o.ownerDocument.documentElement,o)){if(l!==null&&pl(o)){if(r=l.start,t=l.end,t===void 0&&(t=r),"selectionStart"in o)o.selectionStart=r,o.selectionEnd=Math.min(t,o.value.length);else if(t=(r=o.ownerDocument||document)&&r.defaultView||window,t.getSelection){t=t.getSelection();var f=o.textContent.length,p=Math.min(l.start,f);l=l.end===void 0?p:Math.min(l.end,f),!t.extend&&p>l&&(f=l,l=p,p=f),f=Gd(o,p);var y=Gd(o,l);f&&y&&(t.rangeCount!==1||t.anchorNode!==f.node||t.anchorOffset!==f.offset||t.focusNode!==y.node||t.focusOffset!==y.offset)&&(r=r.createRange(),r.setStart(f.node,f.offset),t.removeAllRanges(),p>l?(t.addRange(r),t.extend(y.node,y.offset)):(r.setEnd(y.node,y.offset),t.addRange(r)))}}for(r=[],t=o;t=t.parentNode;)t.nodeType===1&&r.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof o.focus=="function"&&o.focus(),o=0;o<r.length;o++)t=r[o],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var Iy=h&&"documentMode"in document&&11>=document.documentMode,ei=null,hl=null,es=null,ml=!1;function Xd(t,r,o){var l=o.window===o?o.document:o.nodeType===9?o:o.ownerDocument;ml||ei==null||ei!==Ce(l)||(l=ei,"selectionStart"in l&&pl(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),es&&Zi(es,l)||(es=l,l=io(hl,"onSelect"),0<l.length&&(r=new sl("onSelect","select",null,r,o),t.push({event:r,listeners:l}),r.target=ei)))}function to(t,r){var o={};return o[t.toLowerCase()]=r.toLowerCase(),o["Webkit"+t]="webkit"+r,o["Moz"+t]="moz"+r,o}var ti={animationend:to("Animation","AnimationEnd"),animationiteration:to("Animation","AnimationIteration"),animationstart:to("Animation","AnimationStart"),transitionend:to("Transition","TransitionEnd")},vl={},Zd={};h&&(Zd=document.createElement("div").style,"AnimationEvent"in window||(delete ti.animationend.animation,delete ti.animationiteration.animation,delete ti.animationstart.animation),"TransitionEvent"in window||delete ti.transitionend.transition);function no(t){if(vl[t])return vl[t];if(!ti[t])return t;var r=ti[t],o;for(o in r)if(r.hasOwnProperty(o)&&o in Zd)return vl[t]=r[o];return t}var ef=no("animationend"),tf=no("animationiteration"),nf=no("animationstart"),rf=no("transitionend"),sf=new Map,of="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Qn(t,r){sf.set(t,r),c(r,[t])}for(var gl=0;gl<of.length;gl++){var yl=of[gl],Fy=yl.toLowerCase(),By=yl[0].toUpperCase()+yl.slice(1);Qn(Fy,"on"+By)}Qn(ef,"onAnimationEnd"),Qn(tf,"onAnimationIteration"),Qn(nf,"onAnimationStart"),Qn("dblclick","onDoubleClick"),Qn("focusin","onFocus"),Qn("focusout","onBlur"),Qn(rf,"onTransitionEnd"),d("onMouseEnter",["mouseout","mouseover"]),d("onMouseLeave",["mouseout","mouseover"]),d("onPointerEnter",["pointerout","pointerover"]),d("onPointerLeave",["pointerout","pointerover"]),c("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),c("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),c("onBeforeInput",["compositionend","keypress","textInput","paste"]),c("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),c("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),c("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ts="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),$y=new Set("cancel close invalid load scroll toggle".split(" ").concat(ts));function af(t,r,o){var l=t.type||"unknown-event";t.currentTarget=o,Fg(l,r,void 0,t),t.currentTarget=null}function lf(t,r){r=(r&4)!==0;for(var o=0;o<t.length;o++){var l=t[o],f=l.event;l=l.listeners;e:{var p=void 0;if(r)for(var y=l.length-1;0<=y;y--){var b=l[y],C=b.instance,D=b.currentTarget;if(b=b.listener,C!==p&&f.isPropagationStopped())break e;af(f,b,D),p=C}else for(y=0;y<l.length;y++){if(b=l[y],C=b.instance,D=b.currentTarget,b=b.listener,C!==p&&f.isPropagationStopped())break e;af(f,b,D),p=C}}}if($s)throw t=Ga,$s=!1,Ga=null,t}function qe(t,r){var o=r[Ml];o===void 0&&(o=r[Ml]=new Set);var l=t+"__bubble";o.has(l)||(cf(r,t,2,!1),o.add(l))}function _l(t,r,o){var l=0;r&&(l|=4),cf(o,t,l,r)}var ro="_reactListening"+Math.random().toString(36).slice(2);function ns(t){if(!t[ro]){t[ro]=!0,s.forEach(function(o){o!=="selectionchange"&&($y.has(o)||_l(o,!1,t),_l(o,!0,t))});var r=t.nodeType===9?t:t.ownerDocument;r===null||r[ro]||(r[ro]=!0,_l("selectionchange",!1,r))}}function cf(t,r,o,l){switch(Ad(r)){case 1:var f=ey;break;case 4:f=ty;break;default:f=nl}o=f.bind(null,r,o,t),f=void 0,!Qa||r!=="touchstart"&&r!=="touchmove"&&r!=="wheel"||(f=!0),l?f!==void 0?t.addEventListener(r,o,{capture:!0,passive:f}):t.addEventListener(r,o,!0):f!==void 0?t.addEventListener(r,o,{passive:f}):t.addEventListener(r,o,!1)}function wl(t,r,o,l,f){var p=l;if((r&1)===0&&(r&2)===0&&l!==null)e:for(;;){if(l===null)return;var y=l.tag;if(y===3||y===4){var b=l.stateNode.containerInfo;if(b===f||b.nodeType===8&&b.parentNode===f)break;if(y===4)for(y=l.return;y!==null;){var C=y.tag;if((C===3||C===4)&&(C=y.stateNode.containerInfo,C===f||C.nodeType===8&&C.parentNode===f))return;y=y.return}for(;b!==null;){if(y=wr(b),y===null)return;if(C=y.tag,C===5||C===6){l=p=y;continue e}b=b.parentNode}}l=l.return}pd(function(){var D=p,Q=qa(o),G=[];e:{var H=sf.get(t);if(H!==void 0){var oe=sl,ue=t;switch(t){case"keypress":if(Ys(o)===0)break e;case"keydown":case"keyup":oe=vy;break;case"focusin":ue="focus",oe=ll;break;case"focusout":ue="blur",oe=ll;break;case"beforeblur":case"afterblur":oe=ll;break;case"click":if(o.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":oe=jd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":oe=iy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":oe=_y;break;case ef:case tf:case nf:oe=ay;break;case rf:oe=Sy;break;case"scroll":oe=ny;break;case"wheel":oe=by;break;case"copy":case"cut":case"paste":oe=cy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":oe=Dd}var de=(r&4)!==0,Ze=!de&&t==="scroll",A=de?H!==null?H+"Capture":null:H;de=[];for(var M=D,j;M!==null;){j=M;var ee=j.stateNode;if(j.tag===5&&ee!==null&&(j=ee,A!==null&&(ee=Fi(M,A),ee!=null&&de.push(rs(M,ee,j)))),Ze)break;M=M.return}0<de.length&&(H=new oe(H,ue,null,o,Q),G.push({event:H,listeners:de}))}}if((r&7)===0){e:{if(H=t==="mouseover"||t==="pointerover",oe=t==="mouseout"||t==="pointerout",H&&o!==Wa&&(ue=o.relatedTarget||o.fromElement)&&(wr(ue)||ue[Pn]))break e;if((oe||H)&&(H=Q.window===Q?Q:(H=Q.ownerDocument)?H.defaultView||H.parentWindow:window,oe?(ue=o.relatedTarget||o.toElement,oe=D,ue=ue?wr(ue):null,ue!==null&&(Ze=_r(ue),ue!==Ze||ue.tag!==5&&ue.tag!==6)&&(ue=null)):(oe=null,ue=D),oe!==ue)){if(de=jd,ee="onMouseLeave",A="onMouseEnter",M="mouse",(t==="pointerout"||t==="pointerover")&&(de=Dd,ee="onPointerLeave",A="onPointerEnter",M="pointer"),Ze=oe==null?H:ii(oe),j=ue==null?H:ii(ue),H=new de(ee,M+"leave",oe,o,Q),H.target=Ze,H.relatedTarget=j,ee=null,wr(Q)===D&&(de=new de(A,M+"enter",ue,o,Q),de.target=j,de.relatedTarget=Ze,ee=de),Ze=ee,oe&&ue)t:{for(de=oe,A=ue,M=0,j=de;j;j=ni(j))M++;for(j=0,ee=A;ee;ee=ni(ee))j++;for(;0<M-j;)de=ni(de),M--;for(;0<j-M;)A=ni(A),j--;for(;M--;){if(de===A||A!==null&&de===A.alternate)break t;de=ni(de),A=ni(A)}de=null}else de=null;oe!==null&&uf(G,H,oe,de,!1),ue!==null&&Ze!==null&&uf(G,Ze,ue,de,!0)}}e:{if(H=D?ii(D):window,oe=H.nodeName&&H.nodeName.toLowerCase(),oe==="select"||oe==="input"&&H.type==="file")var fe=xy;else if(zd(H))if(Wd)fe=jy;else{fe=Ly;var ge=Ay}else(oe=H.nodeName)&&oe.toLowerCase()==="input"&&(H.type==="checkbox"||H.type==="radio")&&(fe=Ny);if(fe&&(fe=fe(t,D))){Vd(G,fe,o,Q);break e}ge&&ge(t,H,D),t==="focusout"&&(ge=H._wrapperState)&&ge.controlled&&H.type==="number"&&tn(H,"number",H.value)}switch(ge=D?ii(D):window,t){case"focusin":(zd(ge)||ge.contentEditable==="true")&&(ei=ge,hl=D,es=null);break;case"focusout":es=hl=ei=null;break;case"mousedown":ml=!0;break;case"contextmenu":case"mouseup":case"dragend":ml=!1,Xd(G,o,Q);break;case"selectionchange":if(Iy)break;case"keydown":case"keyup":Xd(G,o,Q)}var ye;if(ul)e:{switch(t){case"compositionstart":var we="onCompositionStart";break e;case"compositionend":we="onCompositionEnd";break e;case"compositionupdate":we="onCompositionUpdate";break e}we=void 0}else Zr?$d(t,o)&&(we="onCompositionEnd"):t==="keydown"&&o.keyCode===229&&(we="onCompositionStart");we&&(Id&&o.locale!=="ko"&&(Zr||we!=="onCompositionStart"?we==="onCompositionEnd"&&Zr&&(ye=Ld()):(Hn=Q,il="value"in Hn?Hn.value:Hn.textContent,Zr=!0)),ge=io(D,we),0<ge.length&&(we=new Rd(we,t,null,o,Q),G.push({event:we,listeners:ge}),ye?we.data=ye:(ye=Ud(o),ye!==null&&(we.data=ye)))),(ye=Cy?My(t,o):Ty(t,o))&&(D=io(D,"onBeforeInput"),0<D.length&&(Q=new Rd("onBeforeInput","beforeinput",null,o,Q),G.push({event:Q,listeners:D}),Q.data=ye))}lf(G,r)})}function rs(t,r,o){return{instance:t,listener:r,currentTarget:o}}function io(t,r){for(var o=r+"Capture",l=[];t!==null;){var f=t,p=f.stateNode;f.tag===5&&p!==null&&(f=p,p=Fi(t,o),p!=null&&l.unshift(rs(t,p,f)),p=Fi(t,r),p!=null&&l.push(rs(t,p,f))),t=t.return}return l}function ni(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5);return t||null}function uf(t,r,o,l,f){for(var p=r._reactName,y=[];o!==null&&o!==l;){var b=o,C=b.alternate,D=b.stateNode;if(C!==null&&C===l)break;b.tag===5&&D!==null&&(b=D,f?(C=Fi(o,p),C!=null&&y.unshift(rs(o,C,b))):f||(C=Fi(o,p),C!=null&&y.push(rs(o,C,b)))),o=o.return}y.length!==0&&t.push({event:r,listeners:y})}var Uy=/\r\n?/g,zy=/\u0000|\uFFFD/g;function df(t){return(typeof t=="string"?t:""+t).replace(Uy,`
`).replace(zy,"")}function so(t,r,o){if(r=df(r),df(t)!==r&&o)throw Error(i(425))}function oo(){}var Sl=null,kl=null;function bl(t,r){return t==="textarea"||t==="noscript"||typeof r.children=="string"||typeof r.children=="number"||typeof r.dangerouslySetInnerHTML=="object"&&r.dangerouslySetInnerHTML!==null&&r.dangerouslySetInnerHTML.__html!=null}var El=typeof setTimeout=="function"?setTimeout:void 0,Vy=typeof clearTimeout=="function"?clearTimeout:void 0,ff=typeof Promise=="function"?Promise:void 0,Wy=typeof queueMicrotask=="function"?queueMicrotask:typeof ff<"u"?function(t){return ff.resolve(null).then(t).catch(qy)}:El;function qy(t){setTimeout(function(){throw t})}function Cl(t,r){var o=r,l=0;do{var f=o.nextSibling;if(t.removeChild(o),f&&f.nodeType===8)if(o=f.data,o==="/$"){if(l===0){t.removeChild(f),Hi(r);return}l--}else o!=="$"&&o!=="$?"&&o!=="$!"||l++;o=f}while(o);Hi(r)}function Gn(t){for(;t!=null;t=t.nextSibling){var r=t.nodeType;if(r===1||r===3)break;if(r===8){if(r=t.data,r==="$"||r==="$!"||r==="$?")break;if(r==="/$")return null}}return t}function pf(t){t=t.previousSibling;for(var r=0;t;){if(t.nodeType===8){var o=t.data;if(o==="$"||o==="$!"||o==="$?"){if(r===0)return t;r--}else o==="/$"&&r++}t=t.previousSibling}return null}var ri=Math.random().toString(36).slice(2),wn="__reactFiber$"+ri,is="__reactProps$"+ri,Pn="__reactContainer$"+ri,Ml="__reactEvents$"+ri,Jy="__reactListeners$"+ri,Hy="__reactHandles$"+ri;function wr(t){var r=t[wn];if(r)return r;for(var o=t.parentNode;o;){if(r=o[Pn]||o[wn]){if(o=r.alternate,r.child!==null||o!==null&&o.child!==null)for(t=pf(t);t!==null;){if(o=t[wn])return o;t=pf(t)}return r}t=o,o=t.parentNode}return null}function ss(t){return t=t[wn]||t[Pn],!t||t.tag!==5&&t.tag!==6&&t.tag!==13&&t.tag!==3?null:t}function ii(t){if(t.tag===5||t.tag===6)return t.stateNode;throw Error(i(33))}function ao(t){return t[is]||null}var Tl=[],si=-1;function Kn(t){return{current:t}}function Je(t){0>si||(t.current=Tl[si],Tl[si]=null,si--)}function Ve(t,r){si++,Tl[si]=t.current,t.current=r}var Yn={},ht=Kn(Yn),Pt=Kn(!1),Sr=Yn;function oi(t,r){var o=t.type.contextTypes;if(!o)return Yn;var l=t.stateNode;if(l&&l.__reactInternalMemoizedUnmaskedChildContext===r)return l.__reactInternalMemoizedMaskedChildContext;var f={},p;for(p in o)f[p]=r[p];return l&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=r,t.__reactInternalMemoizedMaskedChildContext=f),f}function Ot(t){return t=t.childContextTypes,t!=null}function lo(){Je(Pt),Je(ht)}function hf(t,r,o){if(ht.current!==Yn)throw Error(i(168));Ve(ht,r),Ve(Pt,o)}function mf(t,r,o){var l=t.stateNode;if(r=r.childContextTypes,typeof l.getChildContext!="function")return o;l=l.getChildContext();for(var f in l)if(!(f in r))throw Error(i(108,k(t)||"Unknown",f));return ie({},o,l)}function co(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||Yn,Sr=ht.current,Ve(ht,t),Ve(Pt,Pt.current),!0}function vf(t,r,o){var l=t.stateNode;if(!l)throw Error(i(169));o?(t=mf(t,r,Sr),l.__reactInternalMemoizedMergedChildContext=t,Je(Pt),Je(ht),Ve(ht,t)):Je(Pt),Ve(Pt,o)}var On=null,uo=!1,Pl=!1;function gf(t){On===null?On=[t]:On.push(t)}function Qy(t){uo=!0,gf(t)}function Xn(){if(!Pl&&On!==null){Pl=!0;var t=0,r=Ie;try{var o=On;for(Ie=1;t<o.length;t++){var l=o[t];do l=l(!0);while(l!==null)}On=null,uo=!1}catch(f){throw On!==null&&(On=On.slice(t+1)),yd(Ka,Xn),f}finally{Ie=r,Pl=!1}}return null}var ai=[],li=0,fo=null,po=0,Vt=[],Wt=0,kr=null,xn=1,An="";function br(t,r){ai[li++]=po,ai[li++]=fo,fo=t,po=r}function yf(t,r,o){Vt[Wt++]=xn,Vt[Wt++]=An,Vt[Wt++]=kr,kr=t;var l=xn;t=An;var f=32-sn(l)-1;l&=~(1<<f),o+=1;var p=32-sn(r)+f;if(30<p){var y=f-f%5;p=(l&(1<<y)-1).toString(32),l>>=y,f-=y,xn=1<<32-sn(r)+f|o<<f|l,An=p+t}else xn=1<<p|o<<f|l,An=t}function Ol(t){t.return!==null&&(br(t,1),yf(t,1,0))}function xl(t){for(;t===fo;)fo=ai[--li],ai[li]=null,po=ai[--li],ai[li]=null;for(;t===kr;)kr=Vt[--Wt],Vt[Wt]=null,An=Vt[--Wt],Vt[Wt]=null,xn=Vt[--Wt],Vt[Wt]=null}var Dt=null,It=null,He=!1,an=null;function _f(t,r){var o=Qt(5,null,null,0);o.elementType="DELETED",o.stateNode=r,o.return=t,r=t.deletions,r===null?(t.deletions=[o],t.flags|=16):r.push(o)}function wf(t,r){switch(t.tag){case 5:var o=t.type;return r=r.nodeType!==1||o.toLowerCase()!==r.nodeName.toLowerCase()?null:r,r!==null?(t.stateNode=r,Dt=t,It=Gn(r.firstChild),!0):!1;case 6:return r=t.pendingProps===""||r.nodeType!==3?null:r,r!==null?(t.stateNode=r,Dt=t,It=null,!0):!1;case 13:return r=r.nodeType!==8?null:r,r!==null?(o=kr!==null?{id:xn,overflow:An}:null,t.memoizedState={dehydrated:r,treeContext:o,retryLane:1073741824},o=Qt(18,null,null,0),o.stateNode=r,o.return=t,t.child=o,Dt=t,It=null,!0):!1;default:return!1}}function Al(t){return(t.mode&1)!==0&&(t.flags&128)===0}function Ll(t){if(He){var r=It;if(r){var o=r;if(!wf(t,r)){if(Al(t))throw Error(i(418));r=Gn(o.nextSibling);var l=Dt;r&&wf(t,r)?_f(l,o):(t.flags=t.flags&-4097|2,He=!1,Dt=t)}}else{if(Al(t))throw Error(i(418));t.flags=t.flags&-4097|2,He=!1,Dt=t}}}function Sf(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;Dt=t}function ho(t){if(t!==Dt)return!1;if(!He)return Sf(t),He=!0,!1;var r;if((r=t.tag!==3)&&!(r=t.tag!==5)&&(r=t.type,r=r!=="head"&&r!=="body"&&!bl(t.type,t.memoizedProps)),r&&(r=It)){if(Al(t))throw kf(),Error(i(418));for(;r;)_f(t,r),r=Gn(r.nextSibling)}if(Sf(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(i(317));e:{for(t=t.nextSibling,r=0;t;){if(t.nodeType===8){var o=t.data;if(o==="/$"){if(r===0){It=Gn(t.nextSibling);break e}r--}else o!=="$"&&o!=="$!"&&o!=="$?"||r++}t=t.nextSibling}It=null}}else It=Dt?Gn(t.stateNode.nextSibling):null;return!0}function kf(){for(var t=It;t;)t=Gn(t.nextSibling)}function ci(){It=Dt=null,He=!1}function Nl(t){an===null?an=[t]:an.push(t)}var Gy=B.ReactCurrentBatchConfig;function os(t,r,o){if(t=o.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(o._owner){if(o=o._owner,o){if(o.tag!==1)throw Error(i(309));var l=o.stateNode}if(!l)throw Error(i(147,t));var f=l,p=""+t;return r!==null&&r.ref!==null&&typeof r.ref=="function"&&r.ref._stringRef===p?r.ref:(r=function(y){var b=f.refs;y===null?delete b[p]:b[p]=y},r._stringRef=p,r)}if(typeof t!="string")throw Error(i(284));if(!o._owner)throw Error(i(290,t))}return t}function mo(t,r){throw t=Object.prototype.toString.call(r),Error(i(31,t==="[object Object]"?"object with keys {"+Object.keys(r).join(", ")+"}":t))}function bf(t){var r=t._init;return r(t._payload)}function Ef(t){function r(A,M){if(t){var j=A.deletions;j===null?(A.deletions=[M],A.flags|=16):j.push(M)}}function o(A,M){if(!t)return null;for(;M!==null;)r(A,M),M=M.sibling;return null}function l(A,M){for(A=new Map;M!==null;)M.key!==null?A.set(M.key,M):A.set(M.index,M),M=M.sibling;return A}function f(A,M){return A=or(A,M),A.index=0,A.sibling=null,A}function p(A,M,j){return A.index=j,t?(j=A.alternate,j!==null?(j=j.index,j<M?(A.flags|=2,M):j):(A.flags|=2,M)):(A.flags|=1048576,M)}function y(A){return t&&A.alternate===null&&(A.flags|=2),A}function b(A,M,j,ee){return M===null||M.tag!==6?(M=Ec(j,A.mode,ee),M.return=A,M):(M=f(M,j),M.return=A,M)}function C(A,M,j,ee){var fe=j.type;return fe===R?Q(A,M,j.props.children,ee,j.key):M!==null&&(M.elementType===fe||typeof fe=="object"&&fe!==null&&fe.$$typeof===Re&&bf(fe)===M.type)?(ee=f(M,j.props),ee.ref=os(A,M,j),ee.return=A,ee):(ee=Bo(j.type,j.key,j.props,null,A.mode,ee),ee.ref=os(A,M,j),ee.return=A,ee)}function D(A,M,j,ee){return M===null||M.tag!==4||M.stateNode.containerInfo!==j.containerInfo||M.stateNode.implementation!==j.implementation?(M=Cc(j,A.mode,ee),M.return=A,M):(M=f(M,j.children||[]),M.return=A,M)}function Q(A,M,j,ee,fe){return M===null||M.tag!==7?(M=Ar(j,A.mode,ee,fe),M.return=A,M):(M=f(M,j),M.return=A,M)}function G(A,M,j){if(typeof M=="string"&&M!==""||typeof M=="number")return M=Ec(""+M,A.mode,j),M.return=A,M;if(typeof M=="object"&&M!==null){switch(M.$$typeof){case I:return j=Bo(M.type,M.key,M.props,null,A.mode,j),j.ref=os(A,null,M),j.return=A,j;case U:return M=Cc(M,A.mode,j),M.return=A,M;case Re:var ee=M._init;return G(A,ee(M._payload),j)}if(nn(M)||ae(M))return M=Ar(M,A.mode,j,null),M.return=A,M;mo(A,M)}return null}function H(A,M,j,ee){var fe=M!==null?M.key:null;if(typeof j=="string"&&j!==""||typeof j=="number")return fe!==null?null:b(A,M,""+j,ee);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case I:return j.key===fe?C(A,M,j,ee):null;case U:return j.key===fe?D(A,M,j,ee):null;case Re:return fe=j._init,H(A,M,fe(j._payload),ee)}if(nn(j)||ae(j))return fe!==null?null:Q(A,M,j,ee,null);mo(A,j)}return null}function oe(A,M,j,ee,fe){if(typeof ee=="string"&&ee!==""||typeof ee=="number")return A=A.get(j)||null,b(M,A,""+ee,fe);if(typeof ee=="object"&&ee!==null){switch(ee.$$typeof){case I:return A=A.get(ee.key===null?j:ee.key)||null,C(M,A,ee,fe);case U:return A=A.get(ee.key===null?j:ee.key)||null,D(M,A,ee,fe);case Re:var ge=ee._init;return oe(A,M,j,ge(ee._payload),fe)}if(nn(ee)||ae(ee))return A=A.get(j)||null,Q(M,A,ee,fe,null);mo(M,ee)}return null}function ue(A,M,j,ee){for(var fe=null,ge=null,ye=M,we=M=0,at=null;ye!==null&&we<j.length;we++){ye.index>we?(at=ye,ye=null):at=ye.sibling;var je=H(A,ye,j[we],ee);if(je===null){ye===null&&(ye=at);break}t&&ye&&je.alternate===null&&r(A,ye),M=p(je,M,we),ge===null?fe=je:ge.sibling=je,ge=je,ye=at}if(we===j.length)return o(A,ye),He&&br(A,we),fe;if(ye===null){for(;we<j.length;we++)ye=G(A,j[we],ee),ye!==null&&(M=p(ye,M,we),ge===null?fe=ye:ge.sibling=ye,ge=ye);return He&&br(A,we),fe}for(ye=l(A,ye);we<j.length;we++)at=oe(ye,A,we,j[we],ee),at!==null&&(t&&at.alternate!==null&&ye.delete(at.key===null?we:at.key),M=p(at,M,we),ge===null?fe=at:ge.sibling=at,ge=at);return t&&ye.forEach(function(ar){return r(A,ar)}),He&&br(A,we),fe}function de(A,M,j,ee){var fe=ae(j);if(typeof fe!="function")throw Error(i(150));if(j=fe.call(j),j==null)throw Error(i(151));for(var ge=fe=null,ye=M,we=M=0,at=null,je=j.next();ye!==null&&!je.done;we++,je=j.next()){ye.index>we?(at=ye,ye=null):at=ye.sibling;var ar=H(A,ye,je.value,ee);if(ar===null){ye===null&&(ye=at);break}t&&ye&&ar.alternate===null&&r(A,ye),M=p(ar,M,we),ge===null?fe=ar:ge.sibling=ar,ge=ar,ye=at}if(je.done)return o(A,ye),He&&br(A,we),fe;if(ye===null){for(;!je.done;we++,je=j.next())je=G(A,je.value,ee),je!==null&&(M=p(je,M,we),ge===null?fe=je:ge.sibling=je,ge=je);return He&&br(A,we),fe}for(ye=l(A,ye);!je.done;we++,je=j.next())je=oe(ye,A,we,je.value,ee),je!==null&&(t&&je.alternate!==null&&ye.delete(je.key===null?we:je.key),M=p(je,M,we),ge===null?fe=je:ge.sibling=je,ge=je);return t&&ye.forEach(function(P_){return r(A,P_)}),He&&br(A,we),fe}function Ze(A,M,j,ee){if(typeof j=="object"&&j!==null&&j.type===R&&j.key===null&&(j=j.props.children),typeof j=="object"&&j!==null){switch(j.$$typeof){case I:e:{for(var fe=j.key,ge=M;ge!==null;){if(ge.key===fe){if(fe=j.type,fe===R){if(ge.tag===7){o(A,ge.sibling),M=f(ge,j.props.children),M.return=A,A=M;break e}}else if(ge.elementType===fe||typeof fe=="object"&&fe!==null&&fe.$$typeof===Re&&bf(fe)===ge.type){o(A,ge.sibling),M=f(ge,j.props),M.ref=os(A,ge,j),M.return=A,A=M;break e}o(A,ge);break}else r(A,ge);ge=ge.sibling}j.type===R?(M=Ar(j.props.children,A.mode,ee,j.key),M.return=A,A=M):(ee=Bo(j.type,j.key,j.props,null,A.mode,ee),ee.ref=os(A,M,j),ee.return=A,A=ee)}return y(A);case U:e:{for(ge=j.key;M!==null;){if(M.key===ge)if(M.tag===4&&M.stateNode.containerInfo===j.containerInfo&&M.stateNode.implementation===j.implementation){o(A,M.sibling),M=f(M,j.children||[]),M.return=A,A=M;break e}else{o(A,M);break}else r(A,M);M=M.sibling}M=Cc(j,A.mode,ee),M.return=A,A=M}return y(A);case Re:return ge=j._init,Ze(A,M,ge(j._payload),ee)}if(nn(j))return ue(A,M,j,ee);if(ae(j))return de(A,M,j,ee);mo(A,j)}return typeof j=="string"&&j!==""||typeof j=="number"?(j=""+j,M!==null&&M.tag===6?(o(A,M.sibling),M=f(M,j),M.return=A,A=M):(o(A,M),M=Ec(j,A.mode,ee),M.return=A,A=M),y(A)):o(A,M)}return Ze}var ui=Ef(!0),Cf=Ef(!1),vo=Kn(null),go=null,di=null,jl=null;function Rl(){jl=di=go=null}function Dl(t){var r=vo.current;Je(vo),t._currentValue=r}function Il(t,r,o){for(;t!==null;){var l=t.alternate;if((t.childLanes&r)!==r?(t.childLanes|=r,l!==null&&(l.childLanes|=r)):l!==null&&(l.childLanes&r)!==r&&(l.childLanes|=r),t===o)break;t=t.return}}function fi(t,r){go=t,jl=di=null,t=t.dependencies,t!==null&&t.firstContext!==null&&((t.lanes&r)!==0&&(xt=!0),t.firstContext=null)}function qt(t){var r=t._currentValue;if(jl!==t)if(t={context:t,memoizedValue:r,next:null},di===null){if(go===null)throw Error(i(308));di=t,go.dependencies={lanes:0,firstContext:t}}else di=di.next=t;return r}var Er=null;function Fl(t){Er===null?Er=[t]:Er.push(t)}function Mf(t,r,o,l){var f=r.interleaved;return f===null?(o.next=o,Fl(r)):(o.next=f.next,f.next=o),r.interleaved=o,Ln(t,l)}function Ln(t,r){t.lanes|=r;var o=t.alternate;for(o!==null&&(o.lanes|=r),o=t,t=t.return;t!==null;)t.childLanes|=r,o=t.alternate,o!==null&&(o.childLanes|=r),o=t,t=t.return;return o.tag===3?o.stateNode:null}var Zn=!1;function Bl(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Tf(t,r){t=t.updateQueue,r.updateQueue===t&&(r.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function Nn(t,r){return{eventTime:t,lane:r,tag:0,payload:null,callback:null,next:null}}function er(t,r,o){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,(Ae&2)!==0){var f=l.pending;return f===null?r.next=r:(r.next=f.next,f.next=r),l.pending=r,Ln(t,o)}return f=l.interleaved,f===null?(r.next=r,Fl(l)):(r.next=f.next,f.next=r),l.interleaved=r,Ln(t,o)}function yo(t,r,o){if(r=r.updateQueue,r!==null&&(r=r.shared,(o&4194240)!==0)){var l=r.lanes;l&=t.pendingLanes,o|=l,r.lanes=o,Za(t,o)}}function Pf(t,r){var o=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,o===l)){var f=null,p=null;if(o=o.firstBaseUpdate,o!==null){do{var y={eventTime:o.eventTime,lane:o.lane,tag:o.tag,payload:o.payload,callback:o.callback,next:null};p===null?f=p=y:p=p.next=y,o=o.next}while(o!==null);p===null?f=p=r:p=p.next=r}else f=p=r;o={baseState:l.baseState,firstBaseUpdate:f,lastBaseUpdate:p,shared:l.shared,effects:l.effects},t.updateQueue=o;return}t=o.lastBaseUpdate,t===null?o.firstBaseUpdate=r:t.next=r,o.lastBaseUpdate=r}function _o(t,r,o,l){var f=t.updateQueue;Zn=!1;var p=f.firstBaseUpdate,y=f.lastBaseUpdate,b=f.shared.pending;if(b!==null){f.shared.pending=null;var C=b,D=C.next;C.next=null,y===null?p=D:y.next=D,y=C;var Q=t.alternate;Q!==null&&(Q=Q.updateQueue,b=Q.lastBaseUpdate,b!==y&&(b===null?Q.firstBaseUpdate=D:b.next=D,Q.lastBaseUpdate=C))}if(p!==null){var G=f.baseState;y=0,Q=D=C=null,b=p;do{var H=b.lane,oe=b.eventTime;if((l&H)===H){Q!==null&&(Q=Q.next={eventTime:oe,lane:0,tag:b.tag,payload:b.payload,callback:b.callback,next:null});e:{var ue=t,de=b;switch(H=r,oe=o,de.tag){case 1:if(ue=de.payload,typeof ue=="function"){G=ue.call(oe,G,H);break e}G=ue;break e;case 3:ue.flags=ue.flags&-65537|128;case 0:if(ue=de.payload,H=typeof ue=="function"?ue.call(oe,G,H):ue,H==null)break e;G=ie({},G,H);break e;case 2:Zn=!0}}b.callback!==null&&b.lane!==0&&(t.flags|=64,H=f.effects,H===null?f.effects=[b]:H.push(b))}else oe={eventTime:oe,lane:H,tag:b.tag,payload:b.payload,callback:b.callback,next:null},Q===null?(D=Q=oe,C=G):Q=Q.next=oe,y|=H;if(b=b.next,b===null){if(b=f.shared.pending,b===null)break;H=b,b=H.next,H.next=null,f.lastBaseUpdate=H,f.shared.pending=null}}while(!0);if(Q===null&&(C=G),f.baseState=C,f.firstBaseUpdate=D,f.lastBaseUpdate=Q,r=f.shared.interleaved,r!==null){f=r;do y|=f.lane,f=f.next;while(f!==r)}else p===null&&(f.shared.lanes=0);Tr|=y,t.lanes=y,t.memoizedState=G}}function Of(t,r,o){if(t=r.effects,r.effects=null,t!==null)for(r=0;r<t.length;r++){var l=t[r],f=l.callback;if(f!==null){if(l.callback=null,l=o,typeof f!="function")throw Error(i(191,f));f.call(l)}}}var as={},Sn=Kn(as),ls=Kn(as),cs=Kn(as);function Cr(t){if(t===as)throw Error(i(174));return t}function $l(t,r){switch(Ve(cs,r),Ve(ls,t),Ve(Sn,as),t=r.nodeType,t){case 9:case 11:r=(r=r.documentElement)?r.namespaceURI:Ua(null,"");break;default:t=t===8?r.parentNode:r,r=t.namespaceURI||null,t=t.tagName,r=Ua(r,t)}Je(Sn),Ve(Sn,r)}function pi(){Je(Sn),Je(ls),Je(cs)}function xf(t){Cr(cs.current);var r=Cr(Sn.current),o=Ua(r,t.type);r!==o&&(Ve(ls,t),Ve(Sn,o))}function Ul(t){ls.current===t&&(Je(Sn),Je(ls))}var Qe=Kn(0);function wo(t){for(var r=t;r!==null;){if(r.tag===13){var o=r.memoizedState;if(o!==null&&(o=o.dehydrated,o===null||o.data==="$?"||o.data==="$!"))return r}else if(r.tag===19&&r.memoizedProps.revealOrder!==void 0){if((r.flags&128)!==0)return r}else if(r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return null;r=r.return}r.sibling.return=r.return,r=r.sibling}return null}var zl=[];function Vl(){for(var t=0;t<zl.length;t++)zl[t]._workInProgressVersionPrimary=null;zl.length=0}var So=B.ReactCurrentDispatcher,Wl=B.ReactCurrentBatchConfig,Mr=0,Ge=null,rt=null,st=null,ko=!1,us=!1,ds=0,Ky=0;function mt(){throw Error(i(321))}function ql(t,r){if(r===null)return!1;for(var o=0;o<r.length&&o<t.length;o++)if(!on(t[o],r[o]))return!1;return!0}function Jl(t,r,o,l,f,p){if(Mr=p,Ge=r,r.memoizedState=null,r.updateQueue=null,r.lanes=0,So.current=t===null||t.memoizedState===null?e_:t_,t=o(l,f),us){p=0;do{if(us=!1,ds=0,25<=p)throw Error(i(301));p+=1,st=rt=null,r.updateQueue=null,So.current=n_,t=o(l,f)}while(us)}if(So.current=Co,r=rt!==null&&rt.next!==null,Mr=0,st=rt=Ge=null,ko=!1,r)throw Error(i(300));return t}function Hl(){var t=ds!==0;return ds=0,t}function kn(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return st===null?Ge.memoizedState=st=t:st=st.next=t,st}function Jt(){if(rt===null){var t=Ge.alternate;t=t!==null?t.memoizedState:null}else t=rt.next;var r=st===null?Ge.memoizedState:st.next;if(r!==null)st=r,rt=t;else{if(t===null)throw Error(i(310));rt=t,t={memoizedState:rt.memoizedState,baseState:rt.baseState,baseQueue:rt.baseQueue,queue:rt.queue,next:null},st===null?Ge.memoizedState=st=t:st=st.next=t}return st}function fs(t,r){return typeof r=="function"?r(t):r}function Ql(t){var r=Jt(),o=r.queue;if(o===null)throw Error(i(311));o.lastRenderedReducer=t;var l=rt,f=l.baseQueue,p=o.pending;if(p!==null){if(f!==null){var y=f.next;f.next=p.next,p.next=y}l.baseQueue=f=p,o.pending=null}if(f!==null){p=f.next,l=l.baseState;var b=y=null,C=null,D=p;do{var Q=D.lane;if((Mr&Q)===Q)C!==null&&(C=C.next={lane:0,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null}),l=D.hasEagerState?D.eagerState:t(l,D.action);else{var G={lane:Q,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null};C===null?(b=C=G,y=l):C=C.next=G,Ge.lanes|=Q,Tr|=Q}D=D.next}while(D!==null&&D!==p);C===null?y=l:C.next=b,on(l,r.memoizedState)||(xt=!0),r.memoizedState=l,r.baseState=y,r.baseQueue=C,o.lastRenderedState=l}if(t=o.interleaved,t!==null){f=t;do p=f.lane,Ge.lanes|=p,Tr|=p,f=f.next;while(f!==t)}else f===null&&(o.lanes=0);return[r.memoizedState,o.dispatch]}function Gl(t){var r=Jt(),o=r.queue;if(o===null)throw Error(i(311));o.lastRenderedReducer=t;var l=o.dispatch,f=o.pending,p=r.memoizedState;if(f!==null){o.pending=null;var y=f=f.next;do p=t(p,y.action),y=y.next;while(y!==f);on(p,r.memoizedState)||(xt=!0),r.memoizedState=p,r.baseQueue===null&&(r.baseState=p),o.lastRenderedState=p}return[p,l]}function Af(){}function Lf(t,r){var o=Ge,l=Jt(),f=r(),p=!on(l.memoizedState,f);if(p&&(l.memoizedState=f,xt=!0),l=l.queue,Kl(Rf.bind(null,o,l,t),[t]),l.getSnapshot!==r||p||st!==null&&st.memoizedState.tag&1){if(o.flags|=2048,ps(9,jf.bind(null,o,l,f,r),void 0,null),ot===null)throw Error(i(349));(Mr&30)!==0||Nf(o,r,f)}return f}function Nf(t,r,o){t.flags|=16384,t={getSnapshot:r,value:o},r=Ge.updateQueue,r===null?(r={lastEffect:null,stores:null},Ge.updateQueue=r,r.stores=[t]):(o=r.stores,o===null?r.stores=[t]:o.push(t))}function jf(t,r,o,l){r.value=o,r.getSnapshot=l,Df(r)&&If(t)}function Rf(t,r,o){return o(function(){Df(r)&&If(t)})}function Df(t){var r=t.getSnapshot;t=t.value;try{var o=r();return!on(t,o)}catch{return!0}}function If(t){var r=Ln(t,1);r!==null&&dn(r,t,1,-1)}function Ff(t){var r=kn();return typeof t=="function"&&(t=t()),r.memoizedState=r.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:fs,lastRenderedState:t},r.queue=t,t=t.dispatch=Zy.bind(null,Ge,t),[r.memoizedState,t]}function ps(t,r,o,l){return t={tag:t,create:r,destroy:o,deps:l,next:null},r=Ge.updateQueue,r===null?(r={lastEffect:null,stores:null},Ge.updateQueue=r,r.lastEffect=t.next=t):(o=r.lastEffect,o===null?r.lastEffect=t.next=t:(l=o.next,o.next=t,t.next=l,r.lastEffect=t)),t}function Bf(){return Jt().memoizedState}function bo(t,r,o,l){var f=kn();Ge.flags|=t,f.memoizedState=ps(1|r,o,void 0,l===void 0?null:l)}function Eo(t,r,o,l){var f=Jt();l=l===void 0?null:l;var p=void 0;if(rt!==null){var y=rt.memoizedState;if(p=y.destroy,l!==null&&ql(l,y.deps)){f.memoizedState=ps(r,o,p,l);return}}Ge.flags|=t,f.memoizedState=ps(1|r,o,p,l)}function $f(t,r){return bo(8390656,8,t,r)}function Kl(t,r){return Eo(2048,8,t,r)}function Uf(t,r){return Eo(4,2,t,r)}function zf(t,r){return Eo(4,4,t,r)}function Vf(t,r){if(typeof r=="function")return t=t(),r(t),function(){r(null)};if(r!=null)return t=t(),r.current=t,function(){r.current=null}}function Wf(t,r,o){return o=o!=null?o.concat([t]):null,Eo(4,4,Vf.bind(null,r,t),o)}function Yl(){}function qf(t,r){var o=Jt();r=r===void 0?null:r;var l=o.memoizedState;return l!==null&&r!==null&&ql(r,l[1])?l[0]:(o.memoizedState=[t,r],t)}function Jf(t,r){var o=Jt();r=r===void 0?null:r;var l=o.memoizedState;return l!==null&&r!==null&&ql(r,l[1])?l[0]:(t=t(),o.memoizedState=[t,r],t)}function Hf(t,r,o){return(Mr&21)===0?(t.baseState&&(t.baseState=!1,xt=!0),t.memoizedState=o):(on(o,r)||(o=kd(),Ge.lanes|=o,Tr|=o,t.baseState=!0),r)}function Yy(t,r){var o=Ie;Ie=o!==0&&4>o?o:4,t(!0);var l=Wl.transition;Wl.transition={};try{t(!1),r()}finally{Ie=o,Wl.transition=l}}function Qf(){return Jt().memoizedState}function Xy(t,r,o){var l=ir(t);if(o={lane:l,action:o,hasEagerState:!1,eagerState:null,next:null},Gf(t))Kf(r,o);else if(o=Mf(t,r,o,l),o!==null){var f=St();dn(o,t,l,f),Yf(o,r,l)}}function Zy(t,r,o){var l=ir(t),f={lane:l,action:o,hasEagerState:!1,eagerState:null,next:null};if(Gf(t))Kf(r,f);else{var p=t.alternate;if(t.lanes===0&&(p===null||p.lanes===0)&&(p=r.lastRenderedReducer,p!==null))try{var y=r.lastRenderedState,b=p(y,o);if(f.hasEagerState=!0,f.eagerState=b,on(b,y)){var C=r.interleaved;C===null?(f.next=f,Fl(r)):(f.next=C.next,C.next=f),r.interleaved=f;return}}catch{}finally{}o=Mf(t,r,f,l),o!==null&&(f=St(),dn(o,t,l,f),Yf(o,r,l))}}function Gf(t){var r=t.alternate;return t===Ge||r!==null&&r===Ge}function Kf(t,r){us=ko=!0;var o=t.pending;o===null?r.next=r:(r.next=o.next,o.next=r),t.pending=r}function Yf(t,r,o){if((o&4194240)!==0){var l=r.lanes;l&=t.pendingLanes,o|=l,r.lanes=o,Za(t,o)}}var Co={readContext:qt,useCallback:mt,useContext:mt,useEffect:mt,useImperativeHandle:mt,useInsertionEffect:mt,useLayoutEffect:mt,useMemo:mt,useReducer:mt,useRef:mt,useState:mt,useDebugValue:mt,useDeferredValue:mt,useTransition:mt,useMutableSource:mt,useSyncExternalStore:mt,useId:mt,unstable_isNewReconciler:!1},e_={readContext:qt,useCallback:function(t,r){return kn().memoizedState=[t,r===void 0?null:r],t},useContext:qt,useEffect:$f,useImperativeHandle:function(t,r,o){return o=o!=null?o.concat([t]):null,bo(4194308,4,Vf.bind(null,r,t),o)},useLayoutEffect:function(t,r){return bo(4194308,4,t,r)},useInsertionEffect:function(t,r){return bo(4,2,t,r)},useMemo:function(t,r){var o=kn();return r=r===void 0?null:r,t=t(),o.memoizedState=[t,r],t},useReducer:function(t,r,o){var l=kn();return r=o!==void 0?o(r):r,l.memoizedState=l.baseState=r,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:r},l.queue=t,t=t.dispatch=Xy.bind(null,Ge,t),[l.memoizedState,t]},useRef:function(t){var r=kn();return t={current:t},r.memoizedState=t},useState:Ff,useDebugValue:Yl,useDeferredValue:function(t){return kn().memoizedState=t},useTransition:function(){var t=Ff(!1),r=t[0];return t=Yy.bind(null,t[1]),kn().memoizedState=t,[r,t]},useMutableSource:function(){},useSyncExternalStore:function(t,r,o){var l=Ge,f=kn();if(He){if(o===void 0)throw Error(i(407));o=o()}else{if(o=r(),ot===null)throw Error(i(349));(Mr&30)!==0||Nf(l,r,o)}f.memoizedState=o;var p={value:o,getSnapshot:r};return f.queue=p,$f(Rf.bind(null,l,p,t),[t]),l.flags|=2048,ps(9,jf.bind(null,l,p,o,r),void 0,null),o},useId:function(){var t=kn(),r=ot.identifierPrefix;if(He){var o=An,l=xn;o=(l&~(1<<32-sn(l)-1)).toString(32)+o,r=":"+r+"R"+o,o=ds++,0<o&&(r+="H"+o.toString(32)),r+=":"}else o=Ky++,r=":"+r+"r"+o.toString(32)+":";return t.memoizedState=r},unstable_isNewReconciler:!1},t_={readContext:qt,useCallback:qf,useContext:qt,useEffect:Kl,useImperativeHandle:Wf,useInsertionEffect:Uf,useLayoutEffect:zf,useMemo:Jf,useReducer:Ql,useRef:Bf,useState:function(){return Ql(fs)},useDebugValue:Yl,useDeferredValue:function(t){var r=Jt();return Hf(r,rt.memoizedState,t)},useTransition:function(){var t=Ql(fs)[0],r=Jt().memoizedState;return[t,r]},useMutableSource:Af,useSyncExternalStore:Lf,useId:Qf,unstable_isNewReconciler:!1},n_={readContext:qt,useCallback:qf,useContext:qt,useEffect:Kl,useImperativeHandle:Wf,useInsertionEffect:Uf,useLayoutEffect:zf,useMemo:Jf,useReducer:Gl,useRef:Bf,useState:function(){return Gl(fs)},useDebugValue:Yl,useDeferredValue:function(t){var r=Jt();return rt===null?r.memoizedState=t:Hf(r,rt.memoizedState,t)},useTransition:function(){var t=Gl(fs)[0],r=Jt().memoizedState;return[t,r]},useMutableSource:Af,useSyncExternalStore:Lf,useId:Qf,unstable_isNewReconciler:!1};function ln(t,r){if(t&&t.defaultProps){r=ie({},r),t=t.defaultProps;for(var o in t)r[o]===void 0&&(r[o]=t[o]);return r}return r}function Xl(t,r,o,l){r=t.memoizedState,o=o(l,r),o=o==null?r:ie({},r,o),t.memoizedState=o,t.lanes===0&&(t.updateQueue.baseState=o)}var Mo={isMounted:function(t){return(t=t._reactInternals)?_r(t)===t:!1},enqueueSetState:function(t,r,o){t=t._reactInternals;var l=St(),f=ir(t),p=Nn(l,f);p.payload=r,o!=null&&(p.callback=o),r=er(t,p,f),r!==null&&(dn(r,t,f,l),yo(r,t,f))},enqueueReplaceState:function(t,r,o){t=t._reactInternals;var l=St(),f=ir(t),p=Nn(l,f);p.tag=1,p.payload=r,o!=null&&(p.callback=o),r=er(t,p,f),r!==null&&(dn(r,t,f,l),yo(r,t,f))},enqueueForceUpdate:function(t,r){t=t._reactInternals;var o=St(),l=ir(t),f=Nn(o,l);f.tag=2,r!=null&&(f.callback=r),r=er(t,f,l),r!==null&&(dn(r,t,l,o),yo(r,t,l))}};function Xf(t,r,o,l,f,p,y){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,p,y):r.prototype&&r.prototype.isPureReactComponent?!Zi(o,l)||!Zi(f,p):!0}function Zf(t,r,o){var l=!1,f=Yn,p=r.contextType;return typeof p=="object"&&p!==null?p=qt(p):(f=Ot(r)?Sr:ht.current,l=r.contextTypes,p=(l=l!=null)?oi(t,f):Yn),r=new r(o,p),t.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=Mo,t.stateNode=r,r._reactInternals=t,l&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=f,t.__reactInternalMemoizedMaskedChildContext=p),r}function ep(t,r,o,l){t=r.state,typeof r.componentWillReceiveProps=="function"&&r.componentWillReceiveProps(o,l),typeof r.UNSAFE_componentWillReceiveProps=="function"&&r.UNSAFE_componentWillReceiveProps(o,l),r.state!==t&&Mo.enqueueReplaceState(r,r.state,null)}function Zl(t,r,o,l){var f=t.stateNode;f.props=o,f.state=t.memoizedState,f.refs={},Bl(t);var p=r.contextType;typeof p=="object"&&p!==null?f.context=qt(p):(p=Ot(r)?Sr:ht.current,f.context=oi(t,p)),f.state=t.memoizedState,p=r.getDerivedStateFromProps,typeof p=="function"&&(Xl(t,r,p,o),f.state=t.memoizedState),typeof r.getDerivedStateFromProps=="function"||typeof f.getSnapshotBeforeUpdate=="function"||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(r=f.state,typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount(),r!==f.state&&Mo.enqueueReplaceState(f,f.state,null),_o(t,o,f,l),f.state=t.memoizedState),typeof f.componentDidMount=="function"&&(t.flags|=4194308)}function hi(t,r){try{var o="",l=r;do o+=v(l),l=l.return;while(l);var f=o}catch(p){f=`
Error generating stack: `+p.message+`
`+p.stack}return{value:t,source:r,stack:f,digest:null}}function ec(t,r,o){return{value:t,source:null,stack:o??null,digest:r??null}}function tc(t,r){try{console.error(r.value)}catch(o){setTimeout(function(){throw o})}}var r_=typeof WeakMap=="function"?WeakMap:Map;function tp(t,r,o){o=Nn(-1,o),o.tag=3,o.payload={element:null};var l=r.value;return o.callback=function(){No||(No=!0,vc=l),tc(t,r)},o}function np(t,r,o){o=Nn(-1,o),o.tag=3;var l=t.type.getDerivedStateFromError;if(typeof l=="function"){var f=r.value;o.payload=function(){return l(f)},o.callback=function(){tc(t,r)}}var p=t.stateNode;return p!==null&&typeof p.componentDidCatch=="function"&&(o.callback=function(){tc(t,r),typeof l!="function"&&(nr===null?nr=new Set([this]):nr.add(this));var y=r.stack;this.componentDidCatch(r.value,{componentStack:y!==null?y:""})}),o}function rp(t,r,o){var l=t.pingCache;if(l===null){l=t.pingCache=new r_;var f=new Set;l.set(r,f)}else f=l.get(r),f===void 0&&(f=new Set,l.set(r,f));f.has(o)||(f.add(o),t=g_.bind(null,t,r,o),r.then(t,t))}function ip(t){do{var r;if((r=t.tag===13)&&(r=t.memoizedState,r=r!==null?r.dehydrated!==null:!0),r)return t;t=t.return}while(t!==null);return null}function sp(t,r,o,l,f){return(t.mode&1)===0?(t===r?t.flags|=65536:(t.flags|=128,o.flags|=131072,o.flags&=-52805,o.tag===1&&(o.alternate===null?o.tag=17:(r=Nn(-1,1),r.tag=2,er(o,r,1))),o.lanes|=1),t):(t.flags|=65536,t.lanes=f,t)}var i_=B.ReactCurrentOwner,xt=!1;function wt(t,r,o,l){r.child=t===null?Cf(r,null,o,l):ui(r,t.child,o,l)}function op(t,r,o,l,f){o=o.render;var p=r.ref;return fi(r,f),l=Jl(t,r,o,l,p,f),o=Hl(),t!==null&&!xt?(r.updateQueue=t.updateQueue,r.flags&=-2053,t.lanes&=~f,jn(t,r,f)):(He&&o&&Ol(r),r.flags|=1,wt(t,r,l,f),r.child)}function ap(t,r,o,l,f){if(t===null){var p=o.type;return typeof p=="function"&&!bc(p)&&p.defaultProps===void 0&&o.compare===null&&o.defaultProps===void 0?(r.tag=15,r.type=p,lp(t,r,p,l,f)):(t=Bo(o.type,null,l,r,r.mode,f),t.ref=r.ref,t.return=r,r.child=t)}if(p=t.child,(t.lanes&f)===0){var y=p.memoizedProps;if(o=o.compare,o=o!==null?o:Zi,o(y,l)&&t.ref===r.ref)return jn(t,r,f)}return r.flags|=1,t=or(p,l),t.ref=r.ref,t.return=r,r.child=t}function lp(t,r,o,l,f){if(t!==null){var p=t.memoizedProps;if(Zi(p,l)&&t.ref===r.ref)if(xt=!1,r.pendingProps=l=p,(t.lanes&f)!==0)(t.flags&131072)!==0&&(xt=!0);else return r.lanes=t.lanes,jn(t,r,f)}return nc(t,r,o,l,f)}function cp(t,r,o){var l=r.pendingProps,f=l.children,p=t!==null?t.memoizedState:null;if(l.mode==="hidden")if((r.mode&1)===0)r.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ve(vi,Ft),Ft|=o;else{if((o&1073741824)===0)return t=p!==null?p.baseLanes|o:o,r.lanes=r.childLanes=1073741824,r.memoizedState={baseLanes:t,cachePool:null,transitions:null},r.updateQueue=null,Ve(vi,Ft),Ft|=t,null;r.memoizedState={baseLanes:0,cachePool:null,transitions:null},l=p!==null?p.baseLanes:o,Ve(vi,Ft),Ft|=l}else p!==null?(l=p.baseLanes|o,r.memoizedState=null):l=o,Ve(vi,Ft),Ft|=l;return wt(t,r,f,o),r.child}function up(t,r){var o=r.ref;(t===null&&o!==null||t!==null&&t.ref!==o)&&(r.flags|=512,r.flags|=2097152)}function nc(t,r,o,l,f){var p=Ot(o)?Sr:ht.current;return p=oi(r,p),fi(r,f),o=Jl(t,r,o,l,p,f),l=Hl(),t!==null&&!xt?(r.updateQueue=t.updateQueue,r.flags&=-2053,t.lanes&=~f,jn(t,r,f)):(He&&l&&Ol(r),r.flags|=1,wt(t,r,o,f),r.child)}function dp(t,r,o,l,f){if(Ot(o)){var p=!0;co(r)}else p=!1;if(fi(r,f),r.stateNode===null)Po(t,r),Zf(r,o,l),Zl(r,o,l,f),l=!0;else if(t===null){var y=r.stateNode,b=r.memoizedProps;y.props=b;var C=y.context,D=o.contextType;typeof D=="object"&&D!==null?D=qt(D):(D=Ot(o)?Sr:ht.current,D=oi(r,D));var Q=o.getDerivedStateFromProps,G=typeof Q=="function"||typeof y.getSnapshotBeforeUpdate=="function";G||typeof y.UNSAFE_componentWillReceiveProps!="function"&&typeof y.componentWillReceiveProps!="function"||(b!==l||C!==D)&&ep(r,y,l,D),Zn=!1;var H=r.memoizedState;y.state=H,_o(r,l,y,f),C=r.memoizedState,b!==l||H!==C||Pt.current||Zn?(typeof Q=="function"&&(Xl(r,o,Q,l),C=r.memoizedState),(b=Zn||Xf(r,o,b,l,H,C,D))?(G||typeof y.UNSAFE_componentWillMount!="function"&&typeof y.componentWillMount!="function"||(typeof y.componentWillMount=="function"&&y.componentWillMount(),typeof y.UNSAFE_componentWillMount=="function"&&y.UNSAFE_componentWillMount()),typeof y.componentDidMount=="function"&&(r.flags|=4194308)):(typeof y.componentDidMount=="function"&&(r.flags|=4194308),r.memoizedProps=l,r.memoizedState=C),y.props=l,y.state=C,y.context=D,l=b):(typeof y.componentDidMount=="function"&&(r.flags|=4194308),l=!1)}else{y=r.stateNode,Tf(t,r),b=r.memoizedProps,D=r.type===r.elementType?b:ln(r.type,b),y.props=D,G=r.pendingProps,H=y.context,C=o.contextType,typeof C=="object"&&C!==null?C=qt(C):(C=Ot(o)?Sr:ht.current,C=oi(r,C));var oe=o.getDerivedStateFromProps;(Q=typeof oe=="function"||typeof y.getSnapshotBeforeUpdate=="function")||typeof y.UNSAFE_componentWillReceiveProps!="function"&&typeof y.componentWillReceiveProps!="function"||(b!==G||H!==C)&&ep(r,y,l,C),Zn=!1,H=r.memoizedState,y.state=H,_o(r,l,y,f);var ue=r.memoizedState;b!==G||H!==ue||Pt.current||Zn?(typeof oe=="function"&&(Xl(r,o,oe,l),ue=r.memoizedState),(D=Zn||Xf(r,o,D,l,H,ue,C)||!1)?(Q||typeof y.UNSAFE_componentWillUpdate!="function"&&typeof y.componentWillUpdate!="function"||(typeof y.componentWillUpdate=="function"&&y.componentWillUpdate(l,ue,C),typeof y.UNSAFE_componentWillUpdate=="function"&&y.UNSAFE_componentWillUpdate(l,ue,C)),typeof y.componentDidUpdate=="function"&&(r.flags|=4),typeof y.getSnapshotBeforeUpdate=="function"&&(r.flags|=1024)):(typeof y.componentDidUpdate!="function"||b===t.memoizedProps&&H===t.memoizedState||(r.flags|=4),typeof y.getSnapshotBeforeUpdate!="function"||b===t.memoizedProps&&H===t.memoizedState||(r.flags|=1024),r.memoizedProps=l,r.memoizedState=ue),y.props=l,y.state=ue,y.context=C,l=D):(typeof y.componentDidUpdate!="function"||b===t.memoizedProps&&H===t.memoizedState||(r.flags|=4),typeof y.getSnapshotBeforeUpdate!="function"||b===t.memoizedProps&&H===t.memoizedState||(r.flags|=1024),l=!1)}return rc(t,r,o,l,p,f)}function rc(t,r,o,l,f,p){up(t,r);var y=(r.flags&128)!==0;if(!l&&!y)return f&&vf(r,o,!1),jn(t,r,p);l=r.stateNode,i_.current=r;var b=y&&typeof o.getDerivedStateFromError!="function"?null:l.render();return r.flags|=1,t!==null&&y?(r.child=ui(r,t.child,null,p),r.child=ui(r,null,b,p)):wt(t,r,b,p),r.memoizedState=l.state,f&&vf(r,o,!0),r.child}function fp(t){var r=t.stateNode;r.pendingContext?hf(t,r.pendingContext,r.pendingContext!==r.context):r.context&&hf(t,r.context,!1),$l(t,r.containerInfo)}function pp(t,r,o,l,f){return ci(),Nl(f),r.flags|=256,wt(t,r,o,l),r.child}var ic={dehydrated:null,treeContext:null,retryLane:0};function sc(t){return{baseLanes:t,cachePool:null,transitions:null}}function hp(t,r,o){var l=r.pendingProps,f=Qe.current,p=!1,y=(r.flags&128)!==0,b;if((b=y)||(b=t!==null&&t.memoizedState===null?!1:(f&2)!==0),b?(p=!0,r.flags&=-129):(t===null||t.memoizedState!==null)&&(f|=1),Ve(Qe,f&1),t===null)return Ll(r),t=r.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?((r.mode&1)===0?r.lanes=1:t.data==="$!"?r.lanes=8:r.lanes=1073741824,null):(y=l.children,t=l.fallback,p?(l=r.mode,p=r.child,y={mode:"hidden",children:y},(l&1)===0&&p!==null?(p.childLanes=0,p.pendingProps=y):p=$o(y,l,0,null),t=Ar(t,l,o,null),p.return=r,t.return=r,p.sibling=t,r.child=p,r.child.memoizedState=sc(o),r.memoizedState=ic,t):oc(r,y));if(f=t.memoizedState,f!==null&&(b=f.dehydrated,b!==null))return s_(t,r,y,l,b,f,o);if(p){p=l.fallback,y=r.mode,f=t.child,b=f.sibling;var C={mode:"hidden",children:l.children};return(y&1)===0&&r.child!==f?(l=r.child,l.childLanes=0,l.pendingProps=C,r.deletions=null):(l=or(f,C),l.subtreeFlags=f.subtreeFlags&14680064),b!==null?p=or(b,p):(p=Ar(p,y,o,null),p.flags|=2),p.return=r,l.return=r,l.sibling=p,r.child=l,l=p,p=r.child,y=t.child.memoizedState,y=y===null?sc(o):{baseLanes:y.baseLanes|o,cachePool:null,transitions:y.transitions},p.memoizedState=y,p.childLanes=t.childLanes&~o,r.memoizedState=ic,l}return p=t.child,t=p.sibling,l=or(p,{mode:"visible",children:l.children}),(r.mode&1)===0&&(l.lanes=o),l.return=r,l.sibling=null,t!==null&&(o=r.deletions,o===null?(r.deletions=[t],r.flags|=16):o.push(t)),r.child=l,r.memoizedState=null,l}function oc(t,r){return r=$o({mode:"visible",children:r},t.mode,0,null),r.return=t,t.child=r}function To(t,r,o,l){return l!==null&&Nl(l),ui(r,t.child,null,o),t=oc(r,r.pendingProps.children),t.flags|=2,r.memoizedState=null,t}function s_(t,r,o,l,f,p,y){if(o)return r.flags&256?(r.flags&=-257,l=ec(Error(i(422))),To(t,r,y,l)):r.memoizedState!==null?(r.child=t.child,r.flags|=128,null):(p=l.fallback,f=r.mode,l=$o({mode:"visible",children:l.children},f,0,null),p=Ar(p,f,y,null),p.flags|=2,l.return=r,p.return=r,l.sibling=p,r.child=l,(r.mode&1)!==0&&ui(r,t.child,null,y),r.child.memoizedState=sc(y),r.memoizedState=ic,p);if((r.mode&1)===0)return To(t,r,y,null);if(f.data==="$!"){if(l=f.nextSibling&&f.nextSibling.dataset,l)var b=l.dgst;return l=b,p=Error(i(419)),l=ec(p,l,void 0),To(t,r,y,l)}if(b=(y&t.childLanes)!==0,xt||b){if(l=ot,l!==null){switch(y&-y){case 4:f=2;break;case 16:f=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:f=32;break;case 536870912:f=268435456;break;default:f=0}f=(f&(l.suspendedLanes|y))!==0?0:f,f!==0&&f!==p.retryLane&&(p.retryLane=f,Ln(t,f),dn(l,t,f,-1))}return kc(),l=ec(Error(i(421))),To(t,r,y,l)}return f.data==="$?"?(r.flags|=128,r.child=t.child,r=y_.bind(null,t),f._reactRetry=r,null):(t=p.treeContext,It=Gn(f.nextSibling),Dt=r,He=!0,an=null,t!==null&&(Vt[Wt++]=xn,Vt[Wt++]=An,Vt[Wt++]=kr,xn=t.id,An=t.overflow,kr=r),r=oc(r,l.children),r.flags|=4096,r)}function mp(t,r,o){t.lanes|=r;var l=t.alternate;l!==null&&(l.lanes|=r),Il(t.return,r,o)}function ac(t,r,o,l,f){var p=t.memoizedState;p===null?t.memoizedState={isBackwards:r,rendering:null,renderingStartTime:0,last:l,tail:o,tailMode:f}:(p.isBackwards=r,p.rendering=null,p.renderingStartTime=0,p.last=l,p.tail=o,p.tailMode=f)}function vp(t,r,o){var l=r.pendingProps,f=l.revealOrder,p=l.tail;if(wt(t,r,l.children,o),l=Qe.current,(l&2)!==0)l=l&1|2,r.flags|=128;else{if(t!==null&&(t.flags&128)!==0)e:for(t=r.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&mp(t,o,r);else if(t.tag===19)mp(t,o,r);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===r)break e;for(;t.sibling===null;){if(t.return===null||t.return===r)break e;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}if(Ve(Qe,l),(r.mode&1)===0)r.memoizedState=null;else switch(f){case"forwards":for(o=r.child,f=null;o!==null;)t=o.alternate,t!==null&&wo(t)===null&&(f=o),o=o.sibling;o=f,o===null?(f=r.child,r.child=null):(f=o.sibling,o.sibling=null),ac(r,!1,f,o,p);break;case"backwards":for(o=null,f=r.child,r.child=null;f!==null;){if(t=f.alternate,t!==null&&wo(t)===null){r.child=f;break}t=f.sibling,f.sibling=o,o=f,f=t}ac(r,!0,o,null,p);break;case"together":ac(r,!1,null,null,void 0);break;default:r.memoizedState=null}return r.child}function Po(t,r){(r.mode&1)===0&&t!==null&&(t.alternate=null,r.alternate=null,r.flags|=2)}function jn(t,r,o){if(t!==null&&(r.dependencies=t.dependencies),Tr|=r.lanes,(o&r.childLanes)===0)return null;if(t!==null&&r.child!==t.child)throw Error(i(153));if(r.child!==null){for(t=r.child,o=or(t,t.pendingProps),r.child=o,o.return=r;t.sibling!==null;)t=t.sibling,o=o.sibling=or(t,t.pendingProps),o.return=r;o.sibling=null}return r.child}function o_(t,r,o){switch(r.tag){case 3:fp(r),ci();break;case 5:xf(r);break;case 1:Ot(r.type)&&co(r);break;case 4:$l(r,r.stateNode.containerInfo);break;case 10:var l=r.type._context,f=r.memoizedProps.value;Ve(vo,l._currentValue),l._currentValue=f;break;case 13:if(l=r.memoizedState,l!==null)return l.dehydrated!==null?(Ve(Qe,Qe.current&1),r.flags|=128,null):(o&r.child.childLanes)!==0?hp(t,r,o):(Ve(Qe,Qe.current&1),t=jn(t,r,o),t!==null?t.sibling:null);Ve(Qe,Qe.current&1);break;case 19:if(l=(o&r.childLanes)!==0,(t.flags&128)!==0){if(l)return vp(t,r,o);r.flags|=128}if(f=r.memoizedState,f!==null&&(f.rendering=null,f.tail=null,f.lastEffect=null),Ve(Qe,Qe.current),l)break;return null;case 22:case 23:return r.lanes=0,cp(t,r,o)}return jn(t,r,o)}var gp,lc,yp,_p;gp=function(t,r){for(var o=r.child;o!==null;){if(o.tag===5||o.tag===6)t.appendChild(o.stateNode);else if(o.tag!==4&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===r)break;for(;o.sibling===null;){if(o.return===null||o.return===r)return;o=o.return}o.sibling.return=o.return,o=o.sibling}},lc=function(){},yp=function(t,r,o,l){var f=t.memoizedProps;if(f!==l){t=r.stateNode,Cr(Sn.current);var p=null;switch(o){case"input":f=Tt(t,f),l=Tt(t,l),p=[];break;case"select":f=ie({},f,{value:void 0}),l=ie({},l,{value:void 0}),p=[];break;case"textarea":f=Tn(t,f),l=Tn(t,l),p=[];break;default:typeof f.onClick!="function"&&typeof l.onClick=="function"&&(t.onclick=oo)}za(o,l);var y;o=null;for(D in f)if(!l.hasOwnProperty(D)&&f.hasOwnProperty(D)&&f[D]!=null)if(D==="style"){var b=f[D];for(y in b)b.hasOwnProperty(y)&&(o||(o={}),o[y]="")}else D!=="dangerouslySetInnerHTML"&&D!=="children"&&D!=="suppressContentEditableWarning"&&D!=="suppressHydrationWarning"&&D!=="autoFocus"&&(a.hasOwnProperty(D)?p||(p=[]):(p=p||[]).push(D,null));for(D in l){var C=l[D];if(b=f!=null?f[D]:void 0,l.hasOwnProperty(D)&&C!==b&&(C!=null||b!=null))if(D==="style")if(b){for(y in b)!b.hasOwnProperty(y)||C&&C.hasOwnProperty(y)||(o||(o={}),o[y]="");for(y in C)C.hasOwnProperty(y)&&b[y]!==C[y]&&(o||(o={}),o[y]=C[y])}else o||(p||(p=[]),p.push(D,o)),o=C;else D==="dangerouslySetInnerHTML"?(C=C?C.__html:void 0,b=b?b.__html:void 0,C!=null&&b!==C&&(p=p||[]).push(D,C)):D==="children"?typeof C!="string"&&typeof C!="number"||(p=p||[]).push(D,""+C):D!=="suppressContentEditableWarning"&&D!=="suppressHydrationWarning"&&(a.hasOwnProperty(D)?(C!=null&&D==="onScroll"&&qe("scroll",t),p||b===C||(p=[])):(p=p||[]).push(D,C))}o&&(p=p||[]).push("style",o);var D=p;(r.updateQueue=D)&&(r.flags|=4)}},_p=function(t,r,o,l){o!==l&&(r.flags|=4)};function hs(t,r){if(!He)switch(t.tailMode){case"hidden":r=t.tail;for(var o=null;r!==null;)r.alternate!==null&&(o=r),r=r.sibling;o===null?t.tail=null:o.sibling=null;break;case"collapsed":o=t.tail;for(var l=null;o!==null;)o.alternate!==null&&(l=o),o=o.sibling;l===null?r||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function vt(t){var r=t.alternate!==null&&t.alternate.child===t.child,o=0,l=0;if(r)for(var f=t.child;f!==null;)o|=f.lanes|f.childLanes,l|=f.subtreeFlags&14680064,l|=f.flags&14680064,f.return=t,f=f.sibling;else for(f=t.child;f!==null;)o|=f.lanes|f.childLanes,l|=f.subtreeFlags,l|=f.flags,f.return=t,f=f.sibling;return t.subtreeFlags|=l,t.childLanes=o,r}function a_(t,r,o){var l=r.pendingProps;switch(xl(r),r.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return vt(r),null;case 1:return Ot(r.type)&&lo(),vt(r),null;case 3:return l=r.stateNode,pi(),Je(Pt),Je(ht),Vl(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(ho(r)?r.flags|=4:t===null||t.memoizedState.isDehydrated&&(r.flags&256)===0||(r.flags|=1024,an!==null&&(_c(an),an=null))),lc(t,r),vt(r),null;case 5:Ul(r);var f=Cr(cs.current);if(o=r.type,t!==null&&r.stateNode!=null)yp(t,r,o,l,f),t.ref!==r.ref&&(r.flags|=512,r.flags|=2097152);else{if(!l){if(r.stateNode===null)throw Error(i(166));return vt(r),null}if(t=Cr(Sn.current),ho(r)){l=r.stateNode,o=r.type;var p=r.memoizedProps;switch(l[wn]=r,l[is]=p,t=(r.mode&1)!==0,o){case"dialog":qe("cancel",l),qe("close",l);break;case"iframe":case"object":case"embed":qe("load",l);break;case"video":case"audio":for(f=0;f<ts.length;f++)qe(ts[f],l);break;case"source":qe("error",l);break;case"img":case"image":case"link":qe("error",l),qe("load",l);break;case"details":qe("toggle",l);break;case"input":yn(l,p),qe("invalid",l);break;case"select":l._wrapperState={wasMultiple:!!p.multiple},qe("invalid",l);break;case"textarea":zn(l,p),qe("invalid",l)}za(o,p),f=null;for(var y in p)if(p.hasOwnProperty(y)){var b=p[y];y==="children"?typeof b=="string"?l.textContent!==b&&(p.suppressHydrationWarning!==!0&&so(l.textContent,b,t),f=["children",b]):typeof b=="number"&&l.textContent!==""+b&&(p.suppressHydrationWarning!==!0&&so(l.textContent,b,t),f=["children",""+b]):a.hasOwnProperty(y)&&b!=null&&y==="onScroll"&&qe("scroll",l)}switch(o){case"input":ce(l),_t(l,p,!0);break;case"textarea":ce(l),Ds(l);break;case"select":case"option":break;default:typeof p.onClick=="function"&&(l.onclick=oo)}l=f,r.updateQueue=l,l!==null&&(r.flags|=4)}else{y=f.nodeType===9?f:f.ownerDocument,t==="http://www.w3.org/1999/xhtml"&&(t=Is(o)),t==="http://www.w3.org/1999/xhtml"?o==="script"?(t=y.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):typeof l.is=="string"?t=y.createElement(o,{is:l.is}):(t=y.createElement(o),o==="select"&&(y=t,l.multiple?y.multiple=!0:l.size&&(y.size=l.size))):t=y.createElementNS(t,o),t[wn]=r,t[is]=l,gp(t,r,!1,!1),r.stateNode=t;e:{switch(y=Va(o,l),o){case"dialog":qe("cancel",t),qe("close",t),f=l;break;case"iframe":case"object":case"embed":qe("load",t),f=l;break;case"video":case"audio":for(f=0;f<ts.length;f++)qe(ts[f],t);f=l;break;case"source":qe("error",t),f=l;break;case"img":case"image":case"link":qe("error",t),qe("load",t),f=l;break;case"details":qe("toggle",t),f=l;break;case"input":yn(t,l),f=Tt(t,l),qe("invalid",t);break;case"option":f=l;break;case"select":t._wrapperState={wasMultiple:!!l.multiple},f=ie({},l,{value:void 0}),qe("invalid",t);break;case"textarea":zn(t,l),f=Tn(t,l),qe("invalid",t);break;default:f=l}za(o,f),b=f;for(p in b)if(b.hasOwnProperty(p)){var C=b[p];p==="style"?ad(t,C):p==="dangerouslySetInnerHTML"?(C=C?C.__html:void 0,C!=null&&sd(t,C)):p==="children"?typeof C=="string"?(o!=="textarea"||C!=="")&&Di(t,C):typeof C=="number"&&Di(t,""+C):p!=="suppressContentEditableWarning"&&p!=="suppressHydrationWarning"&&p!=="autoFocus"&&(a.hasOwnProperty(p)?C!=null&&p==="onScroll"&&qe("scroll",t):C!=null&&re(t,p,C,y))}switch(o){case"input":ce(t),_t(t,l,!1);break;case"textarea":ce(t),Ds(t);break;case"option":l.value!=null&&t.setAttribute("value",""+N(l.value));break;case"select":t.multiple=!!l.multiple,p=l.value,p!=null?rn(t,!!l.multiple,p,!1):l.defaultValue!=null&&rn(t,!!l.multiple,l.defaultValue,!0);break;default:typeof f.onClick=="function"&&(t.onclick=oo)}switch(o){case"button":case"input":case"select":case"textarea":l=!!l.autoFocus;break e;case"img":l=!0;break e;default:l=!1}}l&&(r.flags|=4)}r.ref!==null&&(r.flags|=512,r.flags|=2097152)}return vt(r),null;case 6:if(t&&r.stateNode!=null)_p(t,r,t.memoizedProps,l);else{if(typeof l!="string"&&r.stateNode===null)throw Error(i(166));if(o=Cr(cs.current),Cr(Sn.current),ho(r)){if(l=r.stateNode,o=r.memoizedProps,l[wn]=r,(p=l.nodeValue!==o)&&(t=Dt,t!==null))switch(t.tag){case 3:so(l.nodeValue,o,(t.mode&1)!==0);break;case 5:t.memoizedProps.suppressHydrationWarning!==!0&&so(l.nodeValue,o,(t.mode&1)!==0)}p&&(r.flags|=4)}else l=(o.nodeType===9?o:o.ownerDocument).createTextNode(l),l[wn]=r,r.stateNode=l}return vt(r),null;case 13:if(Je(Qe),l=r.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(He&&It!==null&&(r.mode&1)!==0&&(r.flags&128)===0)kf(),ci(),r.flags|=98560,p=!1;else if(p=ho(r),l!==null&&l.dehydrated!==null){if(t===null){if(!p)throw Error(i(318));if(p=r.memoizedState,p=p!==null?p.dehydrated:null,!p)throw Error(i(317));p[wn]=r}else ci(),(r.flags&128)===0&&(r.memoizedState=null),r.flags|=4;vt(r),p=!1}else an!==null&&(_c(an),an=null),p=!0;if(!p)return r.flags&65536?r:null}return(r.flags&128)!==0?(r.lanes=o,r):(l=l!==null,l!==(t!==null&&t.memoizedState!==null)&&l&&(r.child.flags|=8192,(r.mode&1)!==0&&(t===null||(Qe.current&1)!==0?it===0&&(it=3):kc())),r.updateQueue!==null&&(r.flags|=4),vt(r),null);case 4:return pi(),lc(t,r),t===null&&ns(r.stateNode.containerInfo),vt(r),null;case 10:return Dl(r.type._context),vt(r),null;case 17:return Ot(r.type)&&lo(),vt(r),null;case 19:if(Je(Qe),p=r.memoizedState,p===null)return vt(r),null;if(l=(r.flags&128)!==0,y=p.rendering,y===null)if(l)hs(p,!1);else{if(it!==0||t!==null&&(t.flags&128)!==0)for(t=r.child;t!==null;){if(y=wo(t),y!==null){for(r.flags|=128,hs(p,!1),l=y.updateQueue,l!==null&&(r.updateQueue=l,r.flags|=4),r.subtreeFlags=0,l=o,o=r.child;o!==null;)p=o,t=l,p.flags&=14680066,y=p.alternate,y===null?(p.childLanes=0,p.lanes=t,p.child=null,p.subtreeFlags=0,p.memoizedProps=null,p.memoizedState=null,p.updateQueue=null,p.dependencies=null,p.stateNode=null):(p.childLanes=y.childLanes,p.lanes=y.lanes,p.child=y.child,p.subtreeFlags=0,p.deletions=null,p.memoizedProps=y.memoizedProps,p.memoizedState=y.memoizedState,p.updateQueue=y.updateQueue,p.type=y.type,t=y.dependencies,p.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),o=o.sibling;return Ve(Qe,Qe.current&1|2),r.child}t=t.sibling}p.tail!==null&&Xe()>gi&&(r.flags|=128,l=!0,hs(p,!1),r.lanes=4194304)}else{if(!l)if(t=wo(y),t!==null){if(r.flags|=128,l=!0,o=t.updateQueue,o!==null&&(r.updateQueue=o,r.flags|=4),hs(p,!0),p.tail===null&&p.tailMode==="hidden"&&!y.alternate&&!He)return vt(r),null}else 2*Xe()-p.renderingStartTime>gi&&o!==1073741824&&(r.flags|=128,l=!0,hs(p,!1),r.lanes=4194304);p.isBackwards?(y.sibling=r.child,r.child=y):(o=p.last,o!==null?o.sibling=y:r.child=y,p.last=y)}return p.tail!==null?(r=p.tail,p.rendering=r,p.tail=r.sibling,p.renderingStartTime=Xe(),r.sibling=null,o=Qe.current,Ve(Qe,l?o&1|2:o&1),r):(vt(r),null);case 22:case 23:return Sc(),l=r.memoizedState!==null,t!==null&&t.memoizedState!==null!==l&&(r.flags|=8192),l&&(r.mode&1)!==0?(Ft&1073741824)!==0&&(vt(r),r.subtreeFlags&6&&(r.flags|=8192)):vt(r),null;case 24:return null;case 25:return null}throw Error(i(156,r.tag))}function l_(t,r){switch(xl(r),r.tag){case 1:return Ot(r.type)&&lo(),t=r.flags,t&65536?(r.flags=t&-65537|128,r):null;case 3:return pi(),Je(Pt),Je(ht),Vl(),t=r.flags,(t&65536)!==0&&(t&128)===0?(r.flags=t&-65537|128,r):null;case 5:return Ul(r),null;case 13:if(Je(Qe),t=r.memoizedState,t!==null&&t.dehydrated!==null){if(r.alternate===null)throw Error(i(340));ci()}return t=r.flags,t&65536?(r.flags=t&-65537|128,r):null;case 19:return Je(Qe),null;case 4:return pi(),null;case 10:return Dl(r.type._context),null;case 22:case 23:return Sc(),null;case 24:return null;default:return null}}var Oo=!1,gt=!1,c_=typeof WeakSet=="function"?WeakSet:Set,le=null;function mi(t,r){var o=t.ref;if(o!==null)if(typeof o=="function")try{o(null)}catch(l){Ye(t,r,l)}else o.current=null}function cc(t,r,o){try{o()}catch(l){Ye(t,r,l)}}var wp=!1;function u_(t,r){if(Sl=Qs,t=Yd(),pl(t)){if("selectionStart"in t)var o={start:t.selectionStart,end:t.selectionEnd};else e:{o=(o=t.ownerDocument)&&o.defaultView||window;var l=o.getSelection&&o.getSelection();if(l&&l.rangeCount!==0){o=l.anchorNode;var f=l.anchorOffset,p=l.focusNode;l=l.focusOffset;try{o.nodeType,p.nodeType}catch{o=null;break e}var y=0,b=-1,C=-1,D=0,Q=0,G=t,H=null;t:for(;;){for(var oe;G!==o||f!==0&&G.nodeType!==3||(b=y+f),G!==p||l!==0&&G.nodeType!==3||(C=y+l),G.nodeType===3&&(y+=G.nodeValue.length),(oe=G.firstChild)!==null;)H=G,G=oe;for(;;){if(G===t)break t;if(H===o&&++D===f&&(b=y),H===p&&++Q===l&&(C=y),(oe=G.nextSibling)!==null)break;G=H,H=G.parentNode}G=oe}o=b===-1||C===-1?null:{start:b,end:C}}else o=null}o=o||{start:0,end:0}}else o=null;for(kl={focusedElem:t,selectionRange:o},Qs=!1,le=r;le!==null;)if(r=le,t=r.child,(r.subtreeFlags&1028)!==0&&t!==null)t.return=r,le=t;else for(;le!==null;){r=le;try{var ue=r.alternate;if((r.flags&1024)!==0)switch(r.tag){case 0:case 11:case 15:break;case 1:if(ue!==null){var de=ue.memoizedProps,Ze=ue.memoizedState,A=r.stateNode,M=A.getSnapshotBeforeUpdate(r.elementType===r.type?de:ln(r.type,de),Ze);A.__reactInternalSnapshotBeforeUpdate=M}break;case 3:var j=r.stateNode.containerInfo;j.nodeType===1?j.textContent="":j.nodeType===9&&j.documentElement&&j.removeChild(j.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(i(163))}}catch(ee){Ye(r,r.return,ee)}if(t=r.sibling,t!==null){t.return=r.return,le=t;break}le=r.return}return ue=wp,wp=!1,ue}function ms(t,r,o){var l=r.updateQueue;if(l=l!==null?l.lastEffect:null,l!==null){var f=l=l.next;do{if((f.tag&t)===t){var p=f.destroy;f.destroy=void 0,p!==void 0&&cc(r,o,p)}f=f.next}while(f!==l)}}function xo(t,r){if(r=r.updateQueue,r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&t)===t){var l=o.create;o.destroy=l()}o=o.next}while(o!==r)}}function uc(t){var r=t.ref;if(r!==null){var o=t.stateNode;switch(t.tag){case 5:t=o;break;default:t=o}typeof r=="function"?r(t):r.current=t}}function Sp(t){var r=t.alternate;r!==null&&(t.alternate=null,Sp(r)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(r=t.stateNode,r!==null&&(delete r[wn],delete r[is],delete r[Ml],delete r[Jy],delete r[Hy])),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function kp(t){return t.tag===5||t.tag===3||t.tag===4}function bp(t){e:for(;;){for(;t.sibling===null;){if(t.return===null||kp(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue e;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function dc(t,r,o){var l=t.tag;if(l===5||l===6)t=t.stateNode,r?o.nodeType===8?o.parentNode.insertBefore(t,r):o.insertBefore(t,r):(o.nodeType===8?(r=o.parentNode,r.insertBefore(t,o)):(r=o,r.appendChild(t)),o=o._reactRootContainer,o!=null||r.onclick!==null||(r.onclick=oo));else if(l!==4&&(t=t.child,t!==null))for(dc(t,r,o),t=t.sibling;t!==null;)dc(t,r,o),t=t.sibling}function fc(t,r,o){var l=t.tag;if(l===5||l===6)t=t.stateNode,r?o.insertBefore(t,r):o.appendChild(t);else if(l!==4&&(t=t.child,t!==null))for(fc(t,r,o),t=t.sibling;t!==null;)fc(t,r,o),t=t.sibling}var ut=null,cn=!1;function tr(t,r,o){for(o=o.child;o!==null;)Ep(t,r,o),o=o.sibling}function Ep(t,r,o){if(_n&&typeof _n.onCommitFiberUnmount=="function")try{_n.onCommitFiberUnmount(zs,o)}catch{}switch(o.tag){case 5:gt||mi(o,r);case 6:var l=ut,f=cn;ut=null,tr(t,r,o),ut=l,cn=f,ut!==null&&(cn?(t=ut,o=o.stateNode,t.nodeType===8?t.parentNode.removeChild(o):t.removeChild(o)):ut.removeChild(o.stateNode));break;case 18:ut!==null&&(cn?(t=ut,o=o.stateNode,t.nodeType===8?Cl(t.parentNode,o):t.nodeType===1&&Cl(t,o),Hi(t)):Cl(ut,o.stateNode));break;case 4:l=ut,f=cn,ut=o.stateNode.containerInfo,cn=!0,tr(t,r,o),ut=l,cn=f;break;case 0:case 11:case 14:case 15:if(!gt&&(l=o.updateQueue,l!==null&&(l=l.lastEffect,l!==null))){f=l=l.next;do{var p=f,y=p.destroy;p=p.tag,y!==void 0&&((p&2)!==0||(p&4)!==0)&&cc(o,r,y),f=f.next}while(f!==l)}tr(t,r,o);break;case 1:if(!gt&&(mi(o,r),l=o.stateNode,typeof l.componentWillUnmount=="function"))try{l.props=o.memoizedProps,l.state=o.memoizedState,l.componentWillUnmount()}catch(b){Ye(o,r,b)}tr(t,r,o);break;case 21:tr(t,r,o);break;case 22:o.mode&1?(gt=(l=gt)||o.memoizedState!==null,tr(t,r,o),gt=l):tr(t,r,o);break;default:tr(t,r,o)}}function Cp(t){var r=t.updateQueue;if(r!==null){t.updateQueue=null;var o=t.stateNode;o===null&&(o=t.stateNode=new c_),r.forEach(function(l){var f=__.bind(null,t,l);o.has(l)||(o.add(l),l.then(f,f))})}}function un(t,r){var o=r.deletions;if(o!==null)for(var l=0;l<o.length;l++){var f=o[l];try{var p=t,y=r,b=y;e:for(;b!==null;){switch(b.tag){case 5:ut=b.stateNode,cn=!1;break e;case 3:ut=b.stateNode.containerInfo,cn=!0;break e;case 4:ut=b.stateNode.containerInfo,cn=!0;break e}b=b.return}if(ut===null)throw Error(i(160));Ep(p,y,f),ut=null,cn=!1;var C=f.alternate;C!==null&&(C.return=null),f.return=null}catch(D){Ye(f,r,D)}}if(r.subtreeFlags&12854)for(r=r.child;r!==null;)Mp(r,t),r=r.sibling}function Mp(t,r){var o=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(un(r,t),bn(t),l&4){try{ms(3,t,t.return),xo(3,t)}catch(de){Ye(t,t.return,de)}try{ms(5,t,t.return)}catch(de){Ye(t,t.return,de)}}break;case 1:un(r,t),bn(t),l&512&&o!==null&&mi(o,o.return);break;case 5:if(un(r,t),bn(t),l&512&&o!==null&&mi(o,o.return),t.flags&32){var f=t.stateNode;try{Di(f,"")}catch(de){Ye(t,t.return,de)}}if(l&4&&(f=t.stateNode,f!=null)){var p=t.memoizedProps,y=o!==null?o.memoizedProps:p,b=t.type,C=t.updateQueue;if(t.updateQueue=null,C!==null)try{b==="input"&&p.type==="radio"&&p.name!=null&&Un(f,p),Va(b,y);var D=Va(b,p);for(y=0;y<C.length;y+=2){var Q=C[y],G=C[y+1];Q==="style"?ad(f,G):Q==="dangerouslySetInnerHTML"?sd(f,G):Q==="children"?Di(f,G):re(f,Q,G,D)}switch(b){case"input":Ke(f,p);break;case"textarea":Ri(f,p);break;case"select":var H=f._wrapperState.wasMultiple;f._wrapperState.wasMultiple=!!p.multiple;var oe=p.value;oe!=null?rn(f,!!p.multiple,oe,!1):H!==!!p.multiple&&(p.defaultValue!=null?rn(f,!!p.multiple,p.defaultValue,!0):rn(f,!!p.multiple,p.multiple?[]:"",!1))}f[is]=p}catch(de){Ye(t,t.return,de)}}break;case 6:if(un(r,t),bn(t),l&4){if(t.stateNode===null)throw Error(i(162));f=t.stateNode,p=t.memoizedProps;try{f.nodeValue=p}catch(de){Ye(t,t.return,de)}}break;case 3:if(un(r,t),bn(t),l&4&&o!==null&&o.memoizedState.isDehydrated)try{Hi(r.containerInfo)}catch(de){Ye(t,t.return,de)}break;case 4:un(r,t),bn(t);break;case 13:un(r,t),bn(t),f=t.child,f.flags&8192&&(p=f.memoizedState!==null,f.stateNode.isHidden=p,!p||f.alternate!==null&&f.alternate.memoizedState!==null||(mc=Xe())),l&4&&Cp(t);break;case 22:if(Q=o!==null&&o.memoizedState!==null,t.mode&1?(gt=(D=gt)||Q,un(r,t),gt=D):un(r,t),bn(t),l&8192){if(D=t.memoizedState!==null,(t.stateNode.isHidden=D)&&!Q&&(t.mode&1)!==0)for(le=t,Q=t.child;Q!==null;){for(G=le=Q;le!==null;){switch(H=le,oe=H.child,H.tag){case 0:case 11:case 14:case 15:ms(4,H,H.return);break;case 1:mi(H,H.return);var ue=H.stateNode;if(typeof ue.componentWillUnmount=="function"){l=H,o=H.return;try{r=l,ue.props=r.memoizedProps,ue.state=r.memoizedState,ue.componentWillUnmount()}catch(de){Ye(l,o,de)}}break;case 5:mi(H,H.return);break;case 22:if(H.memoizedState!==null){Op(G);continue}}oe!==null?(oe.return=H,le=oe):Op(G)}Q=Q.sibling}e:for(Q=null,G=t;;){if(G.tag===5){if(Q===null){Q=G;try{f=G.stateNode,D?(p=f.style,typeof p.setProperty=="function"?p.setProperty("display","none","important"):p.display="none"):(b=G.stateNode,C=G.memoizedProps.style,y=C!=null&&C.hasOwnProperty("display")?C.display:null,b.style.display=od("display",y))}catch(de){Ye(t,t.return,de)}}}else if(G.tag===6){if(Q===null)try{G.stateNode.nodeValue=D?"":G.memoizedProps}catch(de){Ye(t,t.return,de)}}else if((G.tag!==22&&G.tag!==23||G.memoizedState===null||G===t)&&G.child!==null){G.child.return=G,G=G.child;continue}if(G===t)break e;for(;G.sibling===null;){if(G.return===null||G.return===t)break e;Q===G&&(Q=null),G=G.return}Q===G&&(Q=null),G.sibling.return=G.return,G=G.sibling}}break;case 19:un(r,t),bn(t),l&4&&Cp(t);break;case 21:break;default:un(r,t),bn(t)}}function bn(t){var r=t.flags;if(r&2){try{e:{for(var o=t.return;o!==null;){if(kp(o)){var l=o;break e}o=o.return}throw Error(i(160))}switch(l.tag){case 5:var f=l.stateNode;l.flags&32&&(Di(f,""),l.flags&=-33);var p=bp(t);fc(t,p,f);break;case 3:case 4:var y=l.stateNode.containerInfo,b=bp(t);dc(t,b,y);break;default:throw Error(i(161))}}catch(C){Ye(t,t.return,C)}t.flags&=-3}r&4096&&(t.flags&=-4097)}function d_(t,r,o){le=t,Tp(t)}function Tp(t,r,o){for(var l=(t.mode&1)!==0;le!==null;){var f=le,p=f.child;if(f.tag===22&&l){var y=f.memoizedState!==null||Oo;if(!y){var b=f.alternate,C=b!==null&&b.memoizedState!==null||gt;b=Oo;var D=gt;if(Oo=y,(gt=C)&&!D)for(le=f;le!==null;)y=le,C=y.child,y.tag===22&&y.memoizedState!==null?xp(f):C!==null?(C.return=y,le=C):xp(f);for(;p!==null;)le=p,Tp(p),p=p.sibling;le=f,Oo=b,gt=D}Pp(t)}else(f.subtreeFlags&8772)!==0&&p!==null?(p.return=f,le=p):Pp(t)}}function Pp(t){for(;le!==null;){var r=le;if((r.flags&8772)!==0){var o=r.alternate;try{if((r.flags&8772)!==0)switch(r.tag){case 0:case 11:case 15:gt||xo(5,r);break;case 1:var l=r.stateNode;if(r.flags&4&&!gt)if(o===null)l.componentDidMount();else{var f=r.elementType===r.type?o.memoizedProps:ln(r.type,o.memoizedProps);l.componentDidUpdate(f,o.memoizedState,l.__reactInternalSnapshotBeforeUpdate)}var p=r.updateQueue;p!==null&&Of(r,p,l);break;case 3:var y=r.updateQueue;if(y!==null){if(o=null,r.child!==null)switch(r.child.tag){case 5:o=r.child.stateNode;break;case 1:o=r.child.stateNode}Of(r,y,o)}break;case 5:var b=r.stateNode;if(o===null&&r.flags&4){o=b;var C=r.memoizedProps;switch(r.type){case"button":case"input":case"select":case"textarea":C.autoFocus&&o.focus();break;case"img":C.src&&(o.src=C.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(r.memoizedState===null){var D=r.alternate;if(D!==null){var Q=D.memoizedState;if(Q!==null){var G=Q.dehydrated;G!==null&&Hi(G)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(i(163))}gt||r.flags&512&&uc(r)}catch(H){Ye(r,r.return,H)}}if(r===t){le=null;break}if(o=r.sibling,o!==null){o.return=r.return,le=o;break}le=r.return}}function Op(t){for(;le!==null;){var r=le;if(r===t){le=null;break}var o=r.sibling;if(o!==null){o.return=r.return,le=o;break}le=r.return}}function xp(t){for(;le!==null;){var r=le;try{switch(r.tag){case 0:case 11:case 15:var o=r.return;try{xo(4,r)}catch(C){Ye(r,o,C)}break;case 1:var l=r.stateNode;if(typeof l.componentDidMount=="function"){var f=r.return;try{l.componentDidMount()}catch(C){Ye(r,f,C)}}var p=r.return;try{uc(r)}catch(C){Ye(r,p,C)}break;case 5:var y=r.return;try{uc(r)}catch(C){Ye(r,y,C)}}}catch(C){Ye(r,r.return,C)}if(r===t){le=null;break}var b=r.sibling;if(b!==null){b.return=r.return,le=b;break}le=r.return}}var f_=Math.ceil,Ao=B.ReactCurrentDispatcher,pc=B.ReactCurrentOwner,Ht=B.ReactCurrentBatchConfig,Ae=0,ot=null,nt=null,dt=0,Ft=0,vi=Kn(0),it=0,vs=null,Tr=0,Lo=0,hc=0,gs=null,At=null,mc=0,gi=1/0,Rn=null,No=!1,vc=null,nr=null,jo=!1,rr=null,Ro=0,ys=0,gc=null,Do=-1,Io=0;function St(){return(Ae&6)!==0?Xe():Do!==-1?Do:Do=Xe()}function ir(t){return(t.mode&1)===0?1:(Ae&2)!==0&&dt!==0?dt&-dt:Gy.transition!==null?(Io===0&&(Io=kd()),Io):(t=Ie,t!==0||(t=window.event,t=t===void 0?16:Ad(t.type)),t)}function dn(t,r,o,l){if(50<ys)throw ys=0,gc=null,Error(i(185));zi(t,o,l),((Ae&2)===0||t!==ot)&&(t===ot&&((Ae&2)===0&&(Lo|=o),it===4&&sr(t,dt)),Lt(t,l),o===1&&Ae===0&&(r.mode&1)===0&&(gi=Xe()+500,uo&&Xn()))}function Lt(t,r){var o=t.callbackNode;Gg(t,r);var l=qs(t,t===ot?dt:0);if(l===0)o!==null&&_d(o),t.callbackNode=null,t.callbackPriority=0;else if(r=l&-l,t.callbackPriority!==r){if(o!=null&&_d(o),r===1)t.tag===0?Qy(Lp.bind(null,t)):gf(Lp.bind(null,t)),Wy(function(){(Ae&6)===0&&Xn()}),o=null;else{switch(bd(l)){case 1:o=Ka;break;case 4:o=wd;break;case 16:o=Us;break;case 536870912:o=Sd;break;default:o=Us}o=$p(o,Ap.bind(null,t))}t.callbackPriority=r,t.callbackNode=o}}function Ap(t,r){if(Do=-1,Io=0,(Ae&6)!==0)throw Error(i(327));var o=t.callbackNode;if(yi()&&t.callbackNode!==o)return null;var l=qs(t,t===ot?dt:0);if(l===0)return null;if((l&30)!==0||(l&t.expiredLanes)!==0||r)r=Fo(t,l);else{r=l;var f=Ae;Ae|=2;var p=jp();(ot!==t||dt!==r)&&(Rn=null,gi=Xe()+500,Or(t,r));do try{m_();break}catch(b){Np(t,b)}while(!0);Rl(),Ao.current=p,Ae=f,nt!==null?r=0:(ot=null,dt=0,r=it)}if(r!==0){if(r===2&&(f=Ya(t),f!==0&&(l=f,r=yc(t,f))),r===1)throw o=vs,Or(t,0),sr(t,l),Lt(t,Xe()),o;if(r===6)sr(t,l);else{if(f=t.current.alternate,(l&30)===0&&!p_(f)&&(r=Fo(t,l),r===2&&(p=Ya(t),p!==0&&(l=p,r=yc(t,p))),r===1))throw o=vs,Or(t,0),sr(t,l),Lt(t,Xe()),o;switch(t.finishedWork=f,t.finishedLanes=l,r){case 0:case 1:throw Error(i(345));case 2:xr(t,At,Rn);break;case 3:if(sr(t,l),(l&130023424)===l&&(r=mc+500-Xe(),10<r)){if(qs(t,0)!==0)break;if(f=t.suspendedLanes,(f&l)!==l){St(),t.pingedLanes|=t.suspendedLanes&f;break}t.timeoutHandle=El(xr.bind(null,t,At,Rn),r);break}xr(t,At,Rn);break;case 4:if(sr(t,l),(l&4194240)===l)break;for(r=t.eventTimes,f=-1;0<l;){var y=31-sn(l);p=1<<y,y=r[y],y>f&&(f=y),l&=~p}if(l=f,l=Xe()-l,l=(120>l?120:480>l?480:1080>l?1080:1920>l?1920:3e3>l?3e3:4320>l?4320:1960*f_(l/1960))-l,10<l){t.timeoutHandle=El(xr.bind(null,t,At,Rn),l);break}xr(t,At,Rn);break;case 5:xr(t,At,Rn);break;default:throw Error(i(329))}}}return Lt(t,Xe()),t.callbackNode===o?Ap.bind(null,t):null}function yc(t,r){var o=gs;return t.current.memoizedState.isDehydrated&&(Or(t,r).flags|=256),t=Fo(t,r),t!==2&&(r=At,At=o,r!==null&&_c(r)),t}function _c(t){At===null?At=t:At.push.apply(At,t)}function p_(t){for(var r=t;;){if(r.flags&16384){var o=r.updateQueue;if(o!==null&&(o=o.stores,o!==null))for(var l=0;l<o.length;l++){var f=o[l],p=f.getSnapshot;f=f.value;try{if(!on(p(),f))return!1}catch{return!1}}}if(o=r.child,r.subtreeFlags&16384&&o!==null)o.return=r,r=o;else{if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return!0;r=r.return}r.sibling.return=r.return,r=r.sibling}}return!0}function sr(t,r){for(r&=~hc,r&=~Lo,t.suspendedLanes|=r,t.pingedLanes&=~r,t=t.expirationTimes;0<r;){var o=31-sn(r),l=1<<o;t[o]=-1,r&=~l}}function Lp(t){if((Ae&6)!==0)throw Error(i(327));yi();var r=qs(t,0);if((r&1)===0)return Lt(t,Xe()),null;var o=Fo(t,r);if(t.tag!==0&&o===2){var l=Ya(t);l!==0&&(r=l,o=yc(t,l))}if(o===1)throw o=vs,Or(t,0),sr(t,r),Lt(t,Xe()),o;if(o===6)throw Error(i(345));return t.finishedWork=t.current.alternate,t.finishedLanes=r,xr(t,At,Rn),Lt(t,Xe()),null}function wc(t,r){var o=Ae;Ae|=1;try{return t(r)}finally{Ae=o,Ae===0&&(gi=Xe()+500,uo&&Xn())}}function Pr(t){rr!==null&&rr.tag===0&&(Ae&6)===0&&yi();var r=Ae;Ae|=1;var o=Ht.transition,l=Ie;try{if(Ht.transition=null,Ie=1,t)return t()}finally{Ie=l,Ht.transition=o,Ae=r,(Ae&6)===0&&Xn()}}function Sc(){Ft=vi.current,Je(vi)}function Or(t,r){t.finishedWork=null,t.finishedLanes=0;var o=t.timeoutHandle;if(o!==-1&&(t.timeoutHandle=-1,Vy(o)),nt!==null)for(o=nt.return;o!==null;){var l=o;switch(xl(l),l.tag){case 1:l=l.type.childContextTypes,l!=null&&lo();break;case 3:pi(),Je(Pt),Je(ht),Vl();break;case 5:Ul(l);break;case 4:pi();break;case 13:Je(Qe);break;case 19:Je(Qe);break;case 10:Dl(l.type._context);break;case 22:case 23:Sc()}o=o.return}if(ot=t,nt=t=or(t.current,null),dt=Ft=r,it=0,vs=null,hc=Lo=Tr=0,At=gs=null,Er!==null){for(r=0;r<Er.length;r++)if(o=Er[r],l=o.interleaved,l!==null){o.interleaved=null;var f=l.next,p=o.pending;if(p!==null){var y=p.next;p.next=f,l.next=y}o.pending=l}Er=null}return t}function Np(t,r){do{var o=nt;try{if(Rl(),So.current=Co,ko){for(var l=Ge.memoizedState;l!==null;){var f=l.queue;f!==null&&(f.pending=null),l=l.next}ko=!1}if(Mr=0,st=rt=Ge=null,us=!1,ds=0,pc.current=null,o===null||o.return===null){it=1,vs=r,nt=null;break}e:{var p=t,y=o.return,b=o,C=r;if(r=dt,b.flags|=32768,C!==null&&typeof C=="object"&&typeof C.then=="function"){var D=C,Q=b,G=Q.tag;if((Q.mode&1)===0&&(G===0||G===11||G===15)){var H=Q.alternate;H?(Q.updateQueue=H.updateQueue,Q.memoizedState=H.memoizedState,Q.lanes=H.lanes):(Q.updateQueue=null,Q.memoizedState=null)}var oe=ip(y);if(oe!==null){oe.flags&=-257,sp(oe,y,b,p,r),oe.mode&1&&rp(p,D,r),r=oe,C=D;var ue=r.updateQueue;if(ue===null){var de=new Set;de.add(C),r.updateQueue=de}else ue.add(C);break e}else{if((r&1)===0){rp(p,D,r),kc();break e}C=Error(i(426))}}else if(He&&b.mode&1){var Ze=ip(y);if(Ze!==null){(Ze.flags&65536)===0&&(Ze.flags|=256),sp(Ze,y,b,p,r),Nl(hi(C,b));break e}}p=C=hi(C,b),it!==4&&(it=2),gs===null?gs=[p]:gs.push(p),p=y;do{switch(p.tag){case 3:p.flags|=65536,r&=-r,p.lanes|=r;var A=tp(p,C,r);Pf(p,A);break e;case 1:b=C;var M=p.type,j=p.stateNode;if((p.flags&128)===0&&(typeof M.getDerivedStateFromError=="function"||j!==null&&typeof j.componentDidCatch=="function"&&(nr===null||!nr.has(j)))){p.flags|=65536,r&=-r,p.lanes|=r;var ee=np(p,b,r);Pf(p,ee);break e}}p=p.return}while(p!==null)}Dp(o)}catch(fe){r=fe,nt===o&&o!==null&&(nt=o=o.return);continue}break}while(!0)}function jp(){var t=Ao.current;return Ao.current=Co,t===null?Co:t}function kc(){(it===0||it===3||it===2)&&(it=4),ot===null||(Tr&268435455)===0&&(Lo&268435455)===0||sr(ot,dt)}function Fo(t,r){var o=Ae;Ae|=2;var l=jp();(ot!==t||dt!==r)&&(Rn=null,Or(t,r));do try{h_();break}catch(f){Np(t,f)}while(!0);if(Rl(),Ae=o,Ao.current=l,nt!==null)throw Error(i(261));return ot=null,dt=0,it}function h_(){for(;nt!==null;)Rp(nt)}function m_(){for(;nt!==null&&!$g();)Rp(nt)}function Rp(t){var r=Bp(t.alternate,t,Ft);t.memoizedProps=t.pendingProps,r===null?Dp(t):nt=r,pc.current=null}function Dp(t){var r=t;do{var o=r.alternate;if(t=r.return,(r.flags&32768)===0){if(o=a_(o,r,Ft),o!==null){nt=o;return}}else{if(o=l_(o,r),o!==null){o.flags&=32767,nt=o;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{it=6,nt=null;return}}if(r=r.sibling,r!==null){nt=r;return}nt=r=t}while(r!==null);it===0&&(it=5)}function xr(t,r,o){var l=Ie,f=Ht.transition;try{Ht.transition=null,Ie=1,v_(t,r,o,l)}finally{Ht.transition=f,Ie=l}return null}function v_(t,r,o,l){do yi();while(rr!==null);if((Ae&6)!==0)throw Error(i(327));o=t.finishedWork;var f=t.finishedLanes;if(o===null)return null;if(t.finishedWork=null,t.finishedLanes=0,o===t.current)throw Error(i(177));t.callbackNode=null,t.callbackPriority=0;var p=o.lanes|o.childLanes;if(Kg(t,p),t===ot&&(nt=ot=null,dt=0),(o.subtreeFlags&2064)===0&&(o.flags&2064)===0||jo||(jo=!0,$p(Us,function(){return yi(),null})),p=(o.flags&15990)!==0,(o.subtreeFlags&15990)!==0||p){p=Ht.transition,Ht.transition=null;var y=Ie;Ie=1;var b=Ae;Ae|=4,pc.current=null,u_(t,o),Mp(o,t),Dy(kl),Qs=!!Sl,kl=Sl=null,t.current=o,d_(o),Ug(),Ae=b,Ie=y,Ht.transition=p}else t.current=o;if(jo&&(jo=!1,rr=t,Ro=f),p=t.pendingLanes,p===0&&(nr=null),Wg(o.stateNode),Lt(t,Xe()),r!==null)for(l=t.onRecoverableError,o=0;o<r.length;o++)f=r[o],l(f.value,{componentStack:f.stack,digest:f.digest});if(No)throw No=!1,t=vc,vc=null,t;return(Ro&1)!==0&&t.tag!==0&&yi(),p=t.pendingLanes,(p&1)!==0?t===gc?ys++:(ys=0,gc=t):ys=0,Xn(),null}function yi(){if(rr!==null){var t=bd(Ro),r=Ht.transition,o=Ie;try{if(Ht.transition=null,Ie=16>t?16:t,rr===null)var l=!1;else{if(t=rr,rr=null,Ro=0,(Ae&6)!==0)throw Error(i(331));var f=Ae;for(Ae|=4,le=t.current;le!==null;){var p=le,y=p.child;if((le.flags&16)!==0){var b=p.deletions;if(b!==null){for(var C=0;C<b.length;C++){var D=b[C];for(le=D;le!==null;){var Q=le;switch(Q.tag){case 0:case 11:case 15:ms(8,Q,p)}var G=Q.child;if(G!==null)G.return=Q,le=G;else for(;le!==null;){Q=le;var H=Q.sibling,oe=Q.return;if(Sp(Q),Q===D){le=null;break}if(H!==null){H.return=oe,le=H;break}le=oe}}}var ue=p.alternate;if(ue!==null){var de=ue.child;if(de!==null){ue.child=null;do{var Ze=de.sibling;de.sibling=null,de=Ze}while(de!==null)}}le=p}}if((p.subtreeFlags&2064)!==0&&y!==null)y.return=p,le=y;else e:for(;le!==null;){if(p=le,(p.flags&2048)!==0)switch(p.tag){case 0:case 11:case 15:ms(9,p,p.return)}var A=p.sibling;if(A!==null){A.return=p.return,le=A;break e}le=p.return}}var M=t.current;for(le=M;le!==null;){y=le;var j=y.child;if((y.subtreeFlags&2064)!==0&&j!==null)j.return=y,le=j;else e:for(y=M;le!==null;){if(b=le,(b.flags&2048)!==0)try{switch(b.tag){case 0:case 11:case 15:xo(9,b)}}catch(fe){Ye(b,b.return,fe)}if(b===y){le=null;break e}var ee=b.sibling;if(ee!==null){ee.return=b.return,le=ee;break e}le=b.return}}if(Ae=f,Xn(),_n&&typeof _n.onPostCommitFiberRoot=="function")try{_n.onPostCommitFiberRoot(zs,t)}catch{}l=!0}return l}finally{Ie=o,Ht.transition=r}}return!1}function Ip(t,r,o){r=hi(o,r),r=tp(t,r,1),t=er(t,r,1),r=St(),t!==null&&(zi(t,1,r),Lt(t,r))}function Ye(t,r,o){if(t.tag===3)Ip(t,t,o);else for(;r!==null;){if(r.tag===3){Ip(r,t,o);break}else if(r.tag===1){var l=r.stateNode;if(typeof r.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(nr===null||!nr.has(l))){t=hi(o,t),t=np(r,t,1),r=er(r,t,1),t=St(),r!==null&&(zi(r,1,t),Lt(r,t));break}}r=r.return}}function g_(t,r,o){var l=t.pingCache;l!==null&&l.delete(r),r=St(),t.pingedLanes|=t.suspendedLanes&o,ot===t&&(dt&o)===o&&(it===4||it===3&&(dt&130023424)===dt&&500>Xe()-mc?Or(t,0):hc|=o),Lt(t,r)}function Fp(t,r){r===0&&((t.mode&1)===0?r=1:(r=Ws,Ws<<=1,(Ws&130023424)===0&&(Ws=4194304)));var o=St();t=Ln(t,r),t!==null&&(zi(t,r,o),Lt(t,o))}function y_(t){var r=t.memoizedState,o=0;r!==null&&(o=r.retryLane),Fp(t,o)}function __(t,r){var o=0;switch(t.tag){case 13:var l=t.stateNode,f=t.memoizedState;f!==null&&(o=f.retryLane);break;case 19:l=t.stateNode;break;default:throw Error(i(314))}l!==null&&l.delete(r),Fp(t,o)}var Bp;Bp=function(t,r,o){if(t!==null)if(t.memoizedProps!==r.pendingProps||Pt.current)xt=!0;else{if((t.lanes&o)===0&&(r.flags&128)===0)return xt=!1,o_(t,r,o);xt=(t.flags&131072)!==0}else xt=!1,He&&(r.flags&1048576)!==0&&yf(r,po,r.index);switch(r.lanes=0,r.tag){case 2:var l=r.type;Po(t,r),t=r.pendingProps;var f=oi(r,ht.current);fi(r,o),f=Jl(null,r,l,t,f,o);var p=Hl();return r.flags|=1,typeof f=="object"&&f!==null&&typeof f.render=="function"&&f.$$typeof===void 0?(r.tag=1,r.memoizedState=null,r.updateQueue=null,Ot(l)?(p=!0,co(r)):p=!1,r.memoizedState=f.state!==null&&f.state!==void 0?f.state:null,Bl(r),f.updater=Mo,r.stateNode=f,f._reactInternals=r,Zl(r,l,t,o),r=rc(null,r,l,!0,p,o)):(r.tag=0,He&&p&&Ol(r),wt(null,r,f,o),r=r.child),r;case 16:l=r.elementType;e:{switch(Po(t,r),t=r.pendingProps,f=l._init,l=f(l._payload),r.type=l,f=r.tag=S_(l),t=ln(l,t),f){case 0:r=nc(null,r,l,t,o);break e;case 1:r=dp(null,r,l,t,o);break e;case 11:r=op(null,r,l,t,o);break e;case 14:r=ap(null,r,l,ln(l.type,t),o);break e}throw Error(i(306,l,""))}return r;case 0:return l=r.type,f=r.pendingProps,f=r.elementType===l?f:ln(l,f),nc(t,r,l,f,o);case 1:return l=r.type,f=r.pendingProps,f=r.elementType===l?f:ln(l,f),dp(t,r,l,f,o);case 3:e:{if(fp(r),t===null)throw Error(i(387));l=r.pendingProps,p=r.memoizedState,f=p.element,Tf(t,r),_o(r,l,null,o);var y=r.memoizedState;if(l=y.element,p.isDehydrated)if(p={element:l,isDehydrated:!1,cache:y.cache,pendingSuspenseBoundaries:y.pendingSuspenseBoundaries,transitions:y.transitions},r.updateQueue.baseState=p,r.memoizedState=p,r.flags&256){f=hi(Error(i(423)),r),r=pp(t,r,l,o,f);break e}else if(l!==f){f=hi(Error(i(424)),r),r=pp(t,r,l,o,f);break e}else for(It=Gn(r.stateNode.containerInfo.firstChild),Dt=r,He=!0,an=null,o=Cf(r,null,l,o),r.child=o;o;)o.flags=o.flags&-3|4096,o=o.sibling;else{if(ci(),l===f){r=jn(t,r,o);break e}wt(t,r,l,o)}r=r.child}return r;case 5:return xf(r),t===null&&Ll(r),l=r.type,f=r.pendingProps,p=t!==null?t.memoizedProps:null,y=f.children,bl(l,f)?y=null:p!==null&&bl(l,p)&&(r.flags|=32),up(t,r),wt(t,r,y,o),r.child;case 6:return t===null&&Ll(r),null;case 13:return hp(t,r,o);case 4:return $l(r,r.stateNode.containerInfo),l=r.pendingProps,t===null?r.child=ui(r,null,l,o):wt(t,r,l,o),r.child;case 11:return l=r.type,f=r.pendingProps,f=r.elementType===l?f:ln(l,f),op(t,r,l,f,o);case 7:return wt(t,r,r.pendingProps,o),r.child;case 8:return wt(t,r,r.pendingProps.children,o),r.child;case 12:return wt(t,r,r.pendingProps.children,o),r.child;case 10:e:{if(l=r.type._context,f=r.pendingProps,p=r.memoizedProps,y=f.value,Ve(vo,l._currentValue),l._currentValue=y,p!==null)if(on(p.value,y)){if(p.children===f.children&&!Pt.current){r=jn(t,r,o);break e}}else for(p=r.child,p!==null&&(p.return=r);p!==null;){var b=p.dependencies;if(b!==null){y=p.child;for(var C=b.firstContext;C!==null;){if(C.context===l){if(p.tag===1){C=Nn(-1,o&-o),C.tag=2;var D=p.updateQueue;if(D!==null){D=D.shared;var Q=D.pending;Q===null?C.next=C:(C.next=Q.next,Q.next=C),D.pending=C}}p.lanes|=o,C=p.alternate,C!==null&&(C.lanes|=o),Il(p.return,o,r),b.lanes|=o;break}C=C.next}}else if(p.tag===10)y=p.type===r.type?null:p.child;else if(p.tag===18){if(y=p.return,y===null)throw Error(i(341));y.lanes|=o,b=y.alternate,b!==null&&(b.lanes|=o),Il(y,o,r),y=p.sibling}else y=p.child;if(y!==null)y.return=p;else for(y=p;y!==null;){if(y===r){y=null;break}if(p=y.sibling,p!==null){p.return=y.return,y=p;break}y=y.return}p=y}wt(t,r,f.children,o),r=r.child}return r;case 9:return f=r.type,l=r.pendingProps.children,fi(r,o),f=qt(f),l=l(f),r.flags|=1,wt(t,r,l,o),r.child;case 14:return l=r.type,f=ln(l,r.pendingProps),f=ln(l.type,f),ap(t,r,l,f,o);case 15:return lp(t,r,r.type,r.pendingProps,o);case 17:return l=r.type,f=r.pendingProps,f=r.elementType===l?f:ln(l,f),Po(t,r),r.tag=1,Ot(l)?(t=!0,co(r)):t=!1,fi(r,o),Zf(r,l,f),Zl(r,l,f,o),rc(null,r,l,!0,t,o);case 19:return vp(t,r,o);case 22:return cp(t,r,o)}throw Error(i(156,r.tag))};function $p(t,r){return yd(t,r)}function w_(t,r,o,l){this.tag=t,this.key=o,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=r,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Qt(t,r,o,l){return new w_(t,r,o,l)}function bc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function S_(t){if(typeof t=="function")return bc(t)?1:0;if(t!=null){if(t=t.$$typeof,t===se)return 11;if(t===Oe)return 14}return 2}function or(t,r){var o=t.alternate;return o===null?(o=Qt(t.tag,r,t.key,t.mode),o.elementType=t.elementType,o.type=t.type,o.stateNode=t.stateNode,o.alternate=t,t.alternate=o):(o.pendingProps=r,o.type=t.type,o.flags=0,o.subtreeFlags=0,o.deletions=null),o.flags=t.flags&14680064,o.childLanes=t.childLanes,o.lanes=t.lanes,o.child=t.child,o.memoizedProps=t.memoizedProps,o.memoizedState=t.memoizedState,o.updateQueue=t.updateQueue,r=t.dependencies,o.dependencies=r===null?null:{lanes:r.lanes,firstContext:r.firstContext},o.sibling=t.sibling,o.index=t.index,o.ref=t.ref,o}function Bo(t,r,o,l,f,p){var y=2;if(l=t,typeof t=="function")bc(t)&&(y=1);else if(typeof t=="string")y=5;else e:switch(t){case R:return Ar(o.children,f,p,r);case z:y=8,f|=8;break;case X:return t=Qt(12,o,r,f|2),t.elementType=X,t.lanes=p,t;case pe:return t=Qt(13,o,r,f),t.elementType=pe,t.lanes=p,t;case _e:return t=Qt(19,o,r,f),t.elementType=_e,t.lanes=p,t;case he:return $o(o,f,p,r);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case $:y=10;break e;case te:y=9;break e;case se:y=11;break e;case Oe:y=14;break e;case Re:y=16,l=null;break e}throw Error(i(130,t==null?t:typeof t,""))}return r=Qt(y,o,r,f),r.elementType=t,r.type=l,r.lanes=p,r}function Ar(t,r,o,l){return t=Qt(7,t,l,r),t.lanes=o,t}function $o(t,r,o,l){return t=Qt(22,t,l,r),t.elementType=he,t.lanes=o,t.stateNode={isHidden:!1},t}function Ec(t,r,o){return t=Qt(6,t,null,r),t.lanes=o,t}function Cc(t,r,o){return r=Qt(4,t.children!==null?t.children:[],t.key,r),r.lanes=o,r.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},r}function k_(t,r,o,l,f){this.tag=r,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Xa(0),this.expirationTimes=Xa(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Xa(0),this.identifierPrefix=l,this.onRecoverableError=f,this.mutableSourceEagerHydrationData=null}function Mc(t,r,o,l,f,p,y,b,C){return t=new k_(t,r,o,b,C),r===1?(r=1,p===!0&&(r|=8)):r=0,p=Qt(3,null,null,r),t.current=p,p.stateNode=t,p.memoizedState={element:l,isDehydrated:o,cache:null,transitions:null,pendingSuspenseBoundaries:null},Bl(p),t}function b_(t,r,o){var l=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:U,key:l==null?null:""+l,children:t,containerInfo:r,implementation:o}}function Up(t){if(!t)return Yn;t=t._reactInternals;e:{if(_r(t)!==t||t.tag!==1)throw Error(i(170));var r=t;do{switch(r.tag){case 3:r=r.stateNode.context;break e;case 1:if(Ot(r.type)){r=r.stateNode.__reactInternalMemoizedMergedChildContext;break e}}r=r.return}while(r!==null);throw Error(i(171))}if(t.tag===1){var o=t.type;if(Ot(o))return mf(t,o,r)}return r}function zp(t,r,o,l,f,p,y,b,C){return t=Mc(o,l,!0,t,f,p,y,b,C),t.context=Up(null),o=t.current,l=St(),f=ir(o),p=Nn(l,f),p.callback=r??null,er(o,p,f),t.current.lanes=f,zi(t,f,l),Lt(t,l),t}function Uo(t,r,o,l){var f=r.current,p=St(),y=ir(f);return o=Up(o),r.context===null?r.context=o:r.pendingContext=o,r=Nn(p,y),r.payload={element:t},l=l===void 0?null:l,l!==null&&(r.callback=l),t=er(f,r,y),t!==null&&(dn(t,f,y,p),yo(t,f,y)),y}function zo(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return t.child.stateNode;default:return t.child.stateNode}}function Vp(t,r){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var o=t.retryLane;t.retryLane=o!==0&&o<r?o:r}}function Tc(t,r){Vp(t,r),(t=t.alternate)&&Vp(t,r)}function E_(){return null}var Wp=typeof reportError=="function"?reportError:function(t){console.error(t)};function Pc(t){this._internalRoot=t}Vo.prototype.render=Pc.prototype.render=function(t){var r=this._internalRoot;if(r===null)throw Error(i(409));Uo(t,r,null,null)},Vo.prototype.unmount=Pc.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var r=t.containerInfo;Pr(function(){Uo(null,t,null,null)}),r[Pn]=null}};function Vo(t){this._internalRoot=t}Vo.prototype.unstable_scheduleHydration=function(t){if(t){var r=Md();t={blockedOn:null,target:t,priority:r};for(var o=0;o<Jn.length&&r!==0&&r<Jn[o].priority;o++);Jn.splice(o,0,t),o===0&&Od(t)}};function Oc(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function Wo(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11&&(t.nodeType!==8||t.nodeValue!==" react-mount-point-unstable "))}function qp(){}function C_(t,r,o,l,f){if(f){if(typeof l=="function"){var p=l;l=function(){var D=zo(y);p.call(D)}}var y=zp(r,l,t,0,null,!1,!1,"",qp);return t._reactRootContainer=y,t[Pn]=y.current,ns(t.nodeType===8?t.parentNode:t),Pr(),y}for(;f=t.lastChild;)t.removeChild(f);if(typeof l=="function"){var b=l;l=function(){var D=zo(C);b.call(D)}}var C=Mc(t,0,!1,null,null,!1,!1,"",qp);return t._reactRootContainer=C,t[Pn]=C.current,ns(t.nodeType===8?t.parentNode:t),Pr(function(){Uo(r,C,o,l)}),C}function qo(t,r,o,l,f){var p=o._reactRootContainer;if(p){var y=p;if(typeof f=="function"){var b=f;f=function(){var C=zo(y);b.call(C)}}Uo(r,y,t,f)}else y=C_(o,r,t,f,l);return zo(y)}Ed=function(t){switch(t.tag){case 3:var r=t.stateNode;if(r.current.memoizedState.isDehydrated){var o=Ui(r.pendingLanes);o!==0&&(Za(r,o|1),Lt(r,Xe()),(Ae&6)===0&&(gi=Xe()+500,Xn()))}break;case 13:Pr(function(){var l=Ln(t,1);if(l!==null){var f=St();dn(l,t,1,f)}}),Tc(t,1)}},el=function(t){if(t.tag===13){var r=Ln(t,134217728);if(r!==null){var o=St();dn(r,t,134217728,o)}Tc(t,134217728)}},Cd=function(t){if(t.tag===13){var r=ir(t),o=Ln(t,r);if(o!==null){var l=St();dn(o,t,r,l)}Tc(t,r)}},Md=function(){return Ie},Td=function(t,r){var o=Ie;try{return Ie=t,r()}finally{Ie=o}},Ja=function(t,r,o){switch(r){case"input":if(Ke(t,o),r=o.name,o.type==="radio"&&r!=null){for(o=t;o.parentNode;)o=o.parentNode;for(o=o.querySelectorAll("input[name="+JSON.stringify(""+r)+'][type="radio"]'),r=0;r<o.length;r++){var l=o[r];if(l!==t&&l.form===t.form){var f=ao(l);if(!f)throw Error(i(90));Ee(l),Ke(l,f)}}}break;case"textarea":Ri(t,o);break;case"select":r=o.value,r!=null&&rn(t,!!o.multiple,r,!1)}},dd=wc,fd=Pr;var M_={usingClientEntryPoint:!1,Events:[ss,ii,ao,cd,ud,wc]},_s={findFiberByHostInstance:wr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},T_={bundleType:_s.bundleType,version:_s.version,rendererPackageName:_s.rendererPackageName,rendererConfig:_s.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:B.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return t=vd(t),t===null?null:t.stateNode},findFiberByHostInstance:_s.findFiberByHostInstance||E_,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Jo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Jo.isDisabled&&Jo.supportsFiber)try{zs=Jo.inject(T_),_n=Jo}catch{}}return Nt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=M_,Nt.createPortal=function(t,r){var o=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Oc(r))throw Error(i(200));return b_(t,r,null,o)},Nt.createRoot=function(t,r){if(!Oc(t))throw Error(i(299));var o=!1,l="",f=Wp;return r!=null&&(r.unstable_strictMode===!0&&(o=!0),r.identifierPrefix!==void 0&&(l=r.identifierPrefix),r.onRecoverableError!==void 0&&(f=r.onRecoverableError)),r=Mc(t,1,!1,null,null,o,!1,l,f),t[Pn]=r.current,ns(t.nodeType===8?t.parentNode:t),new Pc(r)},Nt.findDOMNode=function(t){if(t==null)return null;if(t.nodeType===1)return t;var r=t._reactInternals;if(r===void 0)throw typeof t.render=="function"?Error(i(188)):(t=Object.keys(t).join(","),Error(i(268,t)));return t=vd(r),t=t===null?null:t.stateNode,t},Nt.flushSync=function(t){return Pr(t)},Nt.hydrate=function(t,r,o){if(!Wo(r))throw Error(i(200));return qo(null,t,r,!0,o)},Nt.hydrateRoot=function(t,r,o){if(!Oc(t))throw Error(i(405));var l=o!=null&&o.hydratedSources||null,f=!1,p="",y=Wp;if(o!=null&&(o.unstable_strictMode===!0&&(f=!0),o.identifierPrefix!==void 0&&(p=o.identifierPrefix),o.onRecoverableError!==void 0&&(y=o.onRecoverableError)),r=zp(r,null,t,1,o??null,f,!1,p,y),t[Pn]=r.current,ns(t),l)for(t=0;t<l.length;t++)o=l[t],f=o._getVersion,f=f(o._source),r.mutableSourceEagerHydrationData==null?r.mutableSourceEagerHydrationData=[o,f]:r.mutableSourceEagerHydrationData.push(o,f);return new Vo(r)},Nt.render=function(t,r,o){if(!Wo(r))throw Error(i(200));return qo(null,t,r,!1,o)},Nt.unmountComponentAtNode=function(t){if(!Wo(t))throw Error(i(40));return t._reactRootContainer?(Pr(function(){qo(null,null,t,!1,function(){t._reactRootContainer=null,t[Pn]=null})}),!0):!1},Nt.unstable_batchedUpdates=wc,Nt.unstable_renderSubtreeIntoContainer=function(t,r,o,l){if(!Wo(o))throw Error(i(200));if(t==null||t._reactInternals===void 0)throw Error(i(38));return qo(t,r,o,!1,l)},Nt.version="18.3.1-next-f1338f8080-20240426",Nt}var Zp;function R_(){if(Zp)return Lc.exports;Zp=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(e){console.error(e)}}return n(),Lc.exports=j_(),Lc.exports}var eh;function D_(){if(eh)return Ho;eh=1;var n=R_();return Ho.createRoot=n.createRoot,Ho.hydrateRoot=n.hydrateRoot,Ho}var I_=D_();const F_=ju(I_);var Qo={exports:{}},th;function B_(){if(th)return Qo.exports;th=1;var n=typeof Reflect=="object"?Reflect:null,e=n&&typeof n.apply=="function"?n.apply:function(I,U,R){return Function.prototype.apply.call(I,U,R)},i;n&&typeof n.ownKeys=="function"?i=n.ownKeys:Object.getOwnPropertySymbols?i=function(I){return Object.getOwnPropertyNames(I).concat(Object.getOwnPropertySymbols(I))}:i=function(I){return Object.getOwnPropertyNames(I)};function s(B){console&&console.warn&&console.warn(B)}var a=Number.isNaN||function(I){return I!==I};function c(){c.init.call(this)}Qo.exports=c,Qo.exports.once=Y,c.EventEmitter=c,c.prototype._events=void 0,c.prototype._eventsCount=0,c.prototype._maxListeners=void 0;var d=10;function h(B){if(typeof B!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof B)}Object.defineProperty(c,"defaultMaxListeners",{enumerable:!0,get:function(){return d},set:function(B){if(typeof B!="number"||B<0||a(B))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+B+".");d=B}}),c.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},c.prototype.setMaxListeners=function(I){if(typeof I!="number"||I<0||a(I))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+I+".");return this._maxListeners=I,this};function m(B){return B._maxListeners===void 0?c.defaultMaxListeners:B._maxListeners}c.prototype.getMaxListeners=function(){return m(this)},c.prototype.emit=function(I){for(var U=[],R=1;R<arguments.length;R++)U.push(arguments[R]);var z=I==="error",X=this._events;if(X!==void 0)z=z&&X.error===void 0;else if(!z)return!1;if(z){var $;if(U.length>0&&($=U[0]),$ instanceof Error)throw $;var te=new Error("Unhandled error."+($?" ("+$.message+")":""));throw te.context=$,te}var se=X[I];if(se===void 0)return!1;if(typeof se=="function")e(se,this,U);else for(var pe=se.length,_e=P(se,pe),R=0;R<pe;++R)e(_e[R],this,U);return!0};function _(B,I,U,R){var z,X,$;if(h(U),X=B._events,X===void 0?(X=B._events=Object.create(null),B._eventsCount=0):(X.newListener!==void 0&&(B.emit("newListener",I,U.listener?U.listener:U),X=B._events),$=X[I]),$===void 0)$=X[I]=U,++B._eventsCount;else if(typeof $=="function"?$=X[I]=R?[U,$]:[$,U]:R?$.unshift(U):$.push(U),z=m(B),z>0&&$.length>z&&!$.warned){$.warned=!0;var te=new Error("Possible EventEmitter memory leak detected. "+$.length+" "+String(I)+" listeners added. Use emitter.setMaxListeners() to increase limit");te.name="MaxListenersExceededWarning",te.emitter=B,te.type=I,te.count=$.length,s(te)}return B}c.prototype.addListener=function(I,U){return _(this,I,U,!1)},c.prototype.on=c.prototype.addListener,c.prototype.prependListener=function(I,U){return _(this,I,U,!0)};function w(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function E(B,I,U){var R={fired:!1,wrapFn:void 0,target:B,type:I,listener:U},z=w.bind(R);return z.listener=U,R.wrapFn=z,z}c.prototype.once=function(I,U){return h(U),this.on(I,E(this,I,U)),this},c.prototype.prependOnceListener=function(I,U){return h(U),this.prependListener(I,E(this,I,U)),this},c.prototype.removeListener=function(I,U){var R,z,X,$,te;if(h(U),z=this._events,z===void 0)return this;if(R=z[I],R===void 0)return this;if(R===U||R.listener===U)--this._eventsCount===0?this._events=Object.create(null):(delete z[I],z.removeListener&&this.emit("removeListener",I,R.listener||U));else if(typeof R!="function"){for(X=-1,$=R.length-1;$>=0;$--)if(R[$]===U||R[$].listener===U){te=R[$].listener,X=$;break}if(X<0)return this;X===0?R.shift():L(R,X),R.length===1&&(z[I]=R[0]),z.removeListener!==void 0&&this.emit("removeListener",I,te||U)}return this},c.prototype.off=c.prototype.removeListener,c.prototype.removeAllListeners=function(I){var U,R,z;if(R=this._events,R===void 0)return this;if(R.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):R[I]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete R[I]),this;if(arguments.length===0){var X=Object.keys(R),$;for(z=0;z<X.length;++z)$=X[z],$!=="removeListener"&&this.removeAllListeners($);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(U=R[I],typeof U=="function")this.removeListener(I,U);else if(U!==void 0)for(z=U.length-1;z>=0;z--)this.removeListener(I,U[z]);return this};function x(B,I,U){var R=B._events;if(R===void 0)return[];var z=R[I];return z===void 0?[]:typeof z=="function"?U?[z.listener||z]:[z]:U?F(z):P(z,z.length)}c.prototype.listeners=function(I){return x(this,I,!0)},c.prototype.rawListeners=function(I){return x(this,I,!1)},c.listenerCount=function(B,I){return typeof B.listenerCount=="function"?B.listenerCount(I):T.call(B,I)},c.prototype.listenerCount=T;function T(B){var I=this._events;if(I!==void 0){var U=I[B];if(typeof U=="function")return 1;if(U!==void 0)return U.length}return 0}c.prototype.eventNames=function(){return this._eventsCount>0?i(this._events):[]};function P(B,I){for(var U=new Array(I),R=0;R<I;++R)U[R]=B[R];return U}function L(B,I){for(;I+1<B.length;I++)B[I]=B[I+1];B.pop()}function F(B){for(var I=new Array(B.length),U=0;U<I.length;++U)I[U]=B[U].listener||B[U];return I}function Y(B,I){return new Promise(function(U,R){function z($){B.removeListener(I,X),R($)}function X(){typeof B.removeListener=="function"&&B.removeListener("error",z),U([].slice.call(arguments))}re(B,I,X,{once:!0}),I!=="error"&&ne(B,z,{once:!0})})}function ne(B,I,U){typeof B.on=="function"&&re(B,"error",I,U)}function re(B,I,U,R){if(typeof B.on=="function")R.once?B.once(I,U):B.on(I,U);else if(typeof B.addEventListener=="function")B.addEventListener(I,function z(X){R.once&&B.removeEventListener(I,z),U(X)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof B)}return Qo.exports}var $_=B_();const U_=ju($_);var pt=[];for(var Rc=0;Rc<256;++Rc)pt.push((Rc+256).toString(16).slice(1));function z_(n,e=0){return(pt[n[e+0]]+pt[n[e+1]]+pt[n[e+2]]+pt[n[e+3]]+"-"+pt[n[e+4]]+pt[n[e+5]]+"-"+pt[n[e+6]]+pt[n[e+7]]+"-"+pt[n[e+8]]+pt[n[e+9]]+"-"+pt[n[e+10]]+pt[n[e+11]]+pt[n[e+12]]+pt[n[e+13]]+pt[n[e+14]]+pt[n[e+15]]).toLowerCase()}var Go,V_=new Uint8Array(16);function W_(){if(!Go&&(Go=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Go))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Go(V_)}var q_=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto);const nh={randomUUID:q_};function J_(n,e,i){if(nh.randomUUID&&!e&&!n)return nh.randomUUID();n=n||{};var s=n.random||(n.rng||W_)();if(s[6]=s[6]&15|64,s[8]=s[8]&63|128,e){i=i||0;for(var a=0;a<16;++a)e[i+a]=s[a];return e}return z_(s)}const H_={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},mv={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},et={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},kt={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},ur={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"};class W{static getFirstMatch(e,i){const s=i.match(e);return s&&s.length>0&&s[1]||""}static getSecondMatch(e,i){const s=i.match(e);return s&&s.length>1&&s[2]||""}static matchAndReturnConst(e,i,s){if(e.test(i))return s}static getWindowsVersionName(e){switch(e){case"NT":return"NT";case"XP":return"XP";case"NT 5.0":return"2000";case"NT 5.1":return"XP";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}}static getMacOSVersionName(e){const i=e.split(".").splice(0,2).map(s=>parseInt(s,10)||0);if(i.push(0),i[0]===10)switch(i[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}}static getAndroidVersionName(e){const i=e.split(".").splice(0,2).map(s=>parseInt(s,10)||0);if(i.push(0),!(i[0]===1&&i[1]<5)){if(i[0]===1&&i[1]<6)return"Cupcake";if(i[0]===1&&i[1]>=6)return"Donut";if(i[0]===2&&i[1]<2)return"Eclair";if(i[0]===2&&i[1]===2)return"Froyo";if(i[0]===2&&i[1]>2)return"Gingerbread";if(i[0]===3)return"Honeycomb";if(i[0]===4&&i[1]<1)return"Ice Cream Sandwich";if(i[0]===4&&i[1]<4)return"Jelly Bean";if(i[0]===4&&i[1]>=4)return"KitKat";if(i[0]===5)return"Lollipop";if(i[0]===6)return"Marshmallow";if(i[0]===7)return"Nougat";if(i[0]===8)return"Oreo";if(i[0]===9)return"Pie"}}static getVersionPrecision(e){return e.split(".").length}static compareVersions(e,i,s=!1){const a=W.getVersionPrecision(e),c=W.getVersionPrecision(i);let d=Math.max(a,c),h=0;const m=W.map([e,i],_=>{const w=d-W.getVersionPrecision(_),E=_+new Array(w+1).join(".0");return W.map(E.split("."),x=>new Array(20-x.length).join("0")+x).reverse()});for(s&&(h=d-Math.min(a,c)),d-=1;d>=h;){if(m[0][d]>m[1][d])return 1;if(m[0][d]===m[1][d]){if(d===h)return 0;d-=1}else if(m[0][d]<m[1][d])return-1}}static map(e,i){const s=[];let a;if(Array.prototype.map)return Array.prototype.map.call(e,i);for(a=0;a<e.length;a+=1)s.push(i(e[a]));return s}static find(e,i){let s,a;if(Array.prototype.find)return Array.prototype.find.call(e,i);for(s=0,a=e.length;s<a;s+=1){const c=e[s];if(i(c,s))return c}}static assign(e,...i){const s=e;let a,c;if(Object.assign)return Object.assign(e,...i);for(a=0,c=i.length;a<c;a+=1){const d=i[a];typeof d=="object"&&d!==null&&Object.keys(d).forEach(m=>{s[m]=d[m]})}return e}static getBrowserAlias(e){return H_[e]}static getBrowserTypeByAlias(e){return mv[e]||""}}const $e=/version\/(\d+(\.?_?\d+)+)/i,Q_=[{test:[/googlebot/i],describe(n){const e={name:"Googlebot"},i=W.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,n)||W.getFirstMatch($e,n);return i&&(e.version=i),e}},{test:[/opera/i],describe(n){const e={name:"Opera"},i=W.getFirstMatch($e,n)||W.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/opr\/|opios/i],describe(n){const e={name:"Opera"},i=W.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,n)||W.getFirstMatch($e,n);return i&&(e.version=i),e}},{test:[/SamsungBrowser/i],describe(n){const e={name:"Samsung Internet for Android"},i=W.getFirstMatch($e,n)||W.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/Whale/i],describe(n){const e={name:"NAVER Whale Browser"},i=W.getFirstMatch($e,n)||W.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/MZBrowser/i],describe(n){const e={name:"MZ Browser"},i=W.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,n)||W.getFirstMatch($e,n);return i&&(e.version=i),e}},{test:[/focus/i],describe(n){const e={name:"Focus"},i=W.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,n)||W.getFirstMatch($e,n);return i&&(e.version=i),e}},{test:[/swing/i],describe(n){const e={name:"Swing"},i=W.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,n)||W.getFirstMatch($e,n);return i&&(e.version=i),e}},{test:[/coast/i],describe(n){const e={name:"Opera Coast"},i=W.getFirstMatch($e,n)||W.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe(n){const e={name:"Opera Touch"},i=W.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,n)||W.getFirstMatch($e,n);return i&&(e.version=i),e}},{test:[/yabrowser/i],describe(n){const e={name:"Yandex Browser"},i=W.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,n)||W.getFirstMatch($e,n);return i&&(e.version=i),e}},{test:[/ucbrowser/i],describe(n){const e={name:"UC Browser"},i=W.getFirstMatch($e,n)||W.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/Maxthon|mxios/i],describe(n){const e={name:"Maxthon"},i=W.getFirstMatch($e,n)||W.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/epiphany/i],describe(n){const e={name:"Epiphany"},i=W.getFirstMatch($e,n)||W.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/puffin/i],describe(n){const e={name:"Puffin"},i=W.getFirstMatch($e,n)||W.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/sleipnir/i],describe(n){const e={name:"Sleipnir"},i=W.getFirstMatch($e,n)||W.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/k-meleon/i],describe(n){const e={name:"K-Meleon"},i=W.getFirstMatch($e,n)||W.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/micromessenger/i],describe(n){const e={name:"WeChat"},i=W.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,n)||W.getFirstMatch($e,n);return i&&(e.version=i),e}},{test:[/qqbrowser/i],describe(n){const e={name:/qqbrowserlite/i.test(n)?"QQ Browser Lite":"QQ Browser"},i=W.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,n)||W.getFirstMatch($e,n);return i&&(e.version=i),e}},{test:[/msie|trident/i],describe(n){const e={name:"Internet Explorer"},i=W.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/\sedg\//i],describe(n){const e={name:"Microsoft Edge"},i=W.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/edg([ea]|ios)/i],describe(n){const e={name:"Microsoft Edge"},i=W.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/vivaldi/i],describe(n){const e={name:"Vivaldi"},i=W.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/seamonkey/i],describe(n){const e={name:"SeaMonkey"},i=W.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/sailfish/i],describe(n){const e={name:"Sailfish"},i=W.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,n);return i&&(e.version=i),e}},{test:[/silk/i],describe(n){const e={name:"Amazon Silk"},i=W.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/phantom/i],describe(n){const e={name:"PhantomJS"},i=W.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/slimerjs/i],describe(n){const e={name:"SlimerJS"},i=W.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(n){const e={name:"BlackBerry"},i=W.getFirstMatch($e,n)||W.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/(web|hpw)[o0]s/i],describe(n){const e={name:"WebOS Browser"},i=W.getFirstMatch($e,n)||W.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/bada/i],describe(n){const e={name:"Bada"},i=W.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/tizen/i],describe(n){const e={name:"Tizen"},i=W.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,n)||W.getFirstMatch($e,n);return i&&(e.version=i),e}},{test:[/qupzilla/i],describe(n){const e={name:"QupZilla"},i=W.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,n)||W.getFirstMatch($e,n);return i&&(e.version=i),e}},{test:[/firefox|iceweasel|fxios/i],describe(n){const e={name:"Firefox"},i=W.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/electron/i],describe(n){const e={name:"Electron"},i=W.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/MiuiBrowser/i],describe(n){const e={name:"Miui"},i=W.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/chromium/i],describe(n){const e={name:"Chromium"},i=W.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,n)||W.getFirstMatch($e,n);return i&&(e.version=i),e}},{test:[/chrome|crios|crmo/i],describe(n){const e={name:"Chrome"},i=W.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/GSA/i],describe(n){const e={name:"Google Search"},i=W.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test(n){const e=!n.test(/like android/i),i=n.test(/android/i);return e&&i},describe(n){const e={name:"Android Browser"},i=W.getFirstMatch($e,n);return i&&(e.version=i),e}},{test:[/playstation 4/i],describe(n){const e={name:"PlayStation 4"},i=W.getFirstMatch($e,n);return i&&(e.version=i),e}},{test:[/safari|applewebkit/i],describe(n){const e={name:"Safari"},i=W.getFirstMatch($e,n);return i&&(e.version=i),e}},{test:[/.*/i],describe(n){const e=/^(.*)\/(.*) /,i=/^(.*)\/(.*)[ \t]\((.*)/,a=n.search("\\(")!==-1?i:e;return{name:W.getFirstMatch(a,n),version:W.getSecondMatch(a,n)}}}],G_=[{test:[/Roku\/DVP/],describe(n){const e=W.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,n);return{name:kt.Roku,version:e}}},{test:[/windows phone/i],describe(n){const e=W.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,n);return{name:kt.WindowsPhone,version:e}}},{test:[/windows /i],describe(n){const e=W.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,n),i=W.getWindowsVersionName(e);return{name:kt.Windows,version:e,versionName:i}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(n){const e={name:kt.iOS},i=W.getSecondMatch(/(Version\/)(\d[\d.]+)/,n);return i&&(e.version=i),e}},{test:[/macintosh/i],describe(n){const e=W.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,n).replace(/[_\s]/g,"."),i=W.getMacOSVersionName(e),s={name:kt.MacOS,version:e};return i&&(s.versionName=i),s}},{test:[/(ipod|iphone|ipad)/i],describe(n){const e=W.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,n).replace(/[_\s]/g,".");return{name:kt.iOS,version:e}}},{test(n){const e=!n.test(/like android/i),i=n.test(/android/i);return e&&i},describe(n){const e=W.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,n),i=W.getAndroidVersionName(e),s={name:kt.Android,version:e};return i&&(s.versionName=i),s}},{test:[/(web|hpw)[o0]s/i],describe(n){const e=W.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,n),i={name:kt.WebOS};return e&&e.length&&(i.version=e),i}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(n){const e=W.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,n)||W.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,n)||W.getFirstMatch(/\bbb(\d+)/i,n);return{name:kt.BlackBerry,version:e}}},{test:[/bada/i],describe(n){const e=W.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,n);return{name:kt.Bada,version:e}}},{test:[/tizen/i],describe(n){const e=W.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,n);return{name:kt.Tizen,version:e}}},{test:[/linux/i],describe(){return{name:kt.Linux}}},{test:[/CrOS/],describe(){return{name:kt.ChromeOS}}},{test:[/PlayStation 4/],describe(n){const e=W.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,n);return{name:kt.PlayStation4,version:e}}}],K_=[{test:[/googlebot/i],describe(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe(n){const e=W.getFirstMatch(/(can-l01)/i,n)&&"Nova",i={type:et.mobile,vendor:"Huawei"};return e&&(i.model=e),i}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe(){return{type:et.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe(){return{type:et.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(){return{type:et.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe(){return{type:et.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe(){return{type:et.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe(){return{type:et.tablet}}},{test(n){const e=n.test(/ipod|iphone/i),i=n.test(/like (ipod|iphone)/i);return e&&!i},describe(n){const e=W.getFirstMatch(/(ipod|iphone)/i,n);return{type:et.mobile,vendor:"Apple",model:e}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe(){return{type:et.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe(){return{type:et.mobile}}},{test(n){return n.getBrowserName(!0)==="blackberry"},describe(){return{type:et.mobile,vendor:"BlackBerry"}}},{test(n){return n.getBrowserName(!0)==="bada"},describe(){return{type:et.mobile}}},{test(n){return n.getBrowserName()==="windows phone"},describe(){return{type:et.mobile,vendor:"Microsoft"}}},{test(n){const e=Number(String(n.getOSVersion()).split(".")[0]);return n.getOSName(!0)==="android"&&e>=3},describe(){return{type:et.tablet}}},{test(n){return n.getOSName(!0)==="android"},describe(){return{type:et.mobile}}},{test(n){return n.getOSName(!0)==="macos"},describe(){return{type:et.desktop,vendor:"Apple"}}},{test(n){return n.getOSName(!0)==="windows"},describe(){return{type:et.desktop}}},{test(n){return n.getOSName(!0)==="linux"},describe(){return{type:et.desktop}}},{test(n){return n.getOSName(!0)==="playstation 4"},describe(){return{type:et.tv}}},{test(n){return n.getOSName(!0)==="roku"},describe(){return{type:et.tv}}}],Y_=[{test(n){return n.getBrowserName(!0)==="microsoft edge"},describe(n){if(/\sedg\//i.test(n))return{name:ur.Blink};const i=W.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,n);return{name:ur.EdgeHTML,version:i}}},{test:[/trident/i],describe(n){const e={name:ur.Trident},i=W.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test(n){return n.test(/presto/i)},describe(n){const e={name:ur.Presto},i=W.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test(n){const e=n.test(/gecko/i),i=n.test(/like gecko/i);return e&&!i},describe(n){const e={name:ur.Gecko},i=W.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/(apple)?webkit\/537\.36/i],describe(){return{name:ur.Blink}}},{test:[/(apple)?webkit/i],describe(n){const e={name:ur.WebKit},i=W.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}}];class rh{constructor(e,i=!1){if(e==null||e==="")throw new Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},i!==!0&&this.parse()}getUA(){return this._ua}test(e){return e.test(this._ua)}parseBrowser(){this.parsedResult.browser={};const e=W.find(Q_,i=>{if(typeof i.test=="function")return i.test(this);if(i.test instanceof Array)return i.test.some(s=>this.test(s));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.browser=e.describe(this.getUA())),this.parsedResult.browser}getBrowser(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()}getBrowserName(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""}getBrowserVersion(){return this.getBrowser().version}getOS(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()}parseOS(){this.parsedResult.os={};const e=W.find(G_,i=>{if(typeof i.test=="function")return i.test(this);if(i.test instanceof Array)return i.test.some(s=>this.test(s));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.os=e.describe(this.getUA())),this.parsedResult.os}getOSName(e){const{name:i}=this.getOS();return e?String(i).toLowerCase()||"":i||""}getOSVersion(){return this.getOS().version}getPlatform(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()}getPlatformType(e=!1){const{type:i}=this.getPlatform();return e?String(i).toLowerCase()||"":i||""}parsePlatform(){this.parsedResult.platform={};const e=W.find(K_,i=>{if(typeof i.test=="function")return i.test(this);if(i.test instanceof Array)return i.test.some(s=>this.test(s));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.platform=e.describe(this.getUA())),this.parsedResult.platform}getEngine(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()}getEngineName(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""}parseEngine(){this.parsedResult.engine={};const e=W.find(Y_,i=>{if(typeof i.test=="function")return i.test(this);if(i.test instanceof Array)return i.test.some(s=>this.test(s));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.engine=e.describe(this.getUA())),this.parsedResult.engine}parse(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this}getResult(){return W.assign({},this.parsedResult)}satisfies(e){const i={};let s=0;const a={};let c=0;if(Object.keys(e).forEach(h=>{const m=e[h];typeof m=="string"?(a[h]=m,c+=1):typeof m=="object"&&(i[h]=m,s+=1)}),s>0){const h=Object.keys(i),m=W.find(h,w=>this.isOS(w));if(m){const w=this.satisfies(i[m]);if(w!==void 0)return w}const _=W.find(h,w=>this.isPlatform(w));if(_){const w=this.satisfies(i[_]);if(w!==void 0)return w}}if(c>0){const h=Object.keys(a),m=W.find(h,_=>this.isBrowser(_,!0));if(m!==void 0)return this.compareVersion(a[m])}}isBrowser(e,i=!1){const s=this.getBrowserName().toLowerCase();let a=e.toLowerCase();const c=W.getBrowserTypeByAlias(a);return i&&c&&(a=c.toLowerCase()),a===s}compareVersion(e){let i=[0],s=e,a=!1;const c=this.getBrowserVersion();if(typeof c=="string")return e[0]===">"||e[0]==="<"?(s=e.substr(1),e[1]==="="?(a=!0,s=e.substr(2)):i=[],e[0]===">"?i.push(1):i.push(-1)):e[0]==="="?s=e.substr(1):e[0]==="~"&&(a=!0,s=e.substr(1)),i.indexOf(W.compareVersions(c,s,a))>-1}isOS(e){return this.getOSName(!0)===String(e).toLowerCase()}isPlatform(e){return this.getPlatformType(!0)===String(e).toLowerCase()}isEngine(e){return this.getEngineName(!0)===String(e).toLowerCase()}is(e,i=!1){return this.isBrowser(e,i)||this.isOS(e)||this.isPlatform(e)}some(e=[]){return e.some(i=>this.is(i))}}/*!
 * Bowser - a browser detector
 * https://github.com/lancedikson/bowser
 * MIT License | (c) Dustin Diaz 2012-2015
 * MIT License | (c) Denis Demchenko 2015-2019
 */class X_{static getParser(e,i=!1){if(typeof e!="string")throw new Error("UserAgent should be a string");return new rh(e,i)}static parse(e){return new rh(e).getResult()}static get BROWSER_MAP(){return mv}static get ENGINE_MAP(){return ur}static get OS_MAP(){return kt}static get PLATFORMS_MAP(){return et}}function yr(n,e){return Object.keys(e).forEach(function(i){i==="default"||i==="__esModule"||Object.prototype.hasOwnProperty.call(n,i)||Object.defineProperty(n,i,{enumerable:!0,get:function(){return e[i]}})}),n}function Z_(n){return n&&n.__esModule?n.default:n}function ct(n,e,i,s){Object.defineProperty(n,e,{get:i,set:s,enumerable:!0,configurable:!0})}var Oa={},vv={};ct(vv,"PipecatClient",()=>Li);var Ci={};Ci=JSON.parse('{"name":"@pipecat-ai/client-js","version":"1.0.0","license":"BSD-2-Clause","main":"dist/index.js","module":"dist/index.module.js","types":"dist/index.d.ts","source":"index.ts","repository":{"type":"git","url":"git+https://github.com/pipecat-ai/pipecat-client-web.git"},"files":["dist","package.json","README.md"],"scripts":{"build":"jest --silent --passWithNoTests && parcel build --no-cache","dev":"parcel watch","lint":"eslint . --report-unused-disable-directives --max-warnings 0","test":"jest"},"jest":{"preset":"ts-jest","testEnvironment":"jsdom"},"devDependencies":{"@jest/globals":"^29.7.0","@types/clone-deep":"^4.0.4","@types/jest":"^29.5.12","@types/uuid":"^10.0.0","eslint":"^9.11.1","eslint-config-prettier":"^9.1.0","eslint-plugin-simple-import-sort":"^12.1.1","jest":"^29.7.0","jest-environment-jsdom":"^30.0.2","ts-jest":"^29.2.5"},"dependencies":{"@types/events":"^3.0.3","bowser":"^2.11.0","clone-deep":"^4.0.1","events":"^3.3.0","typed-emitter":"^2.1.0","uuid":"^10.0.0"}}');var xa={},ew={},Jr={};ct(Jr,"RTVIError",()=>mn);ct(Jr,"ConnectionTimeoutError",()=>tw);ct(Jr,"StartBotError",()=>su);ct(Jr,"TransportStartError",()=>gv);ct(Jr,"BotNotReadyError",()=>yv);ct(Jr,"UnsupportedFeatureError",()=>nw);class mn extends Error{constructor(e,i){super(e),this.status=i}}class tw extends mn{constructor(e){super(e??"Bot did not enter ready state within the specified timeout period.")}}class su extends mn{constructor(e,i){super(e??"Failed to connect / invalid auth bundle from base url",i??500),this.error="invalid-request-error"}}class gv extends mn{constructor(e){super(e??"Unable to connect to transport")}}class yv extends mn{constructor(e){super(e??"Attempt to call action on transport when not in 'ready' state.")}}class nw extends mn{constructor(e,i,s){let a=`${e} not supported${s?`: ${s}`:""}`;i&&(a=`${i} does not support ${e}${s?`: ${s}`:""}`),super(a),this.feature=e}}var _v={};ct(_v,"RTVIEvent",()=>ve);var ve;(function(n){n.Connected="connected",n.Disconnected="disconnected",n.TransportStateChanged="transportStateChanged",n.BotConnected="botConnected",n.BotReady="botReady",n.BotDisconnected="botDisconnected",n.Error="error",n.ServerMessage="serverMessage",n.ServerResponse="serverResponse",n.MessageError="messageError",n.Metrics="metrics",n.BotStartedSpeaking="botStartedSpeaking",n.BotStoppedSpeaking="botStoppedSpeaking",n.UserStartedSpeaking="userStartedSpeaking",n.UserStoppedSpeaking="userStoppedSpeaking",n.UserTranscript="userTranscript",n.BotTranscript="botTranscript",n.BotLlmText="botLlmText",n.BotLlmStarted="botLlmStarted",n.BotLlmStopped="botLlmStopped",n.LLMFunctionCall="llmFunctionCall",n.BotLlmSearchResponse="botLlmSearchResponse",n.BotTtsText="botTtsText",n.BotTtsStarted="botTtsStarted",n.BotTtsStopped="botTtsStopped",n.ParticipantConnected="participantConnected",n.ParticipantLeft="participantLeft",n.TrackStarted="trackStarted",n.TrackStopped="trackStopped",n.ScreenTrackStarted="screenTrackStarted",n.ScreenTrackStopped="screenTrackStopped",n.ScreenShareError="screenShareError",n.LocalAudioLevel="localAudioLevel",n.RemoteAudioLevel="remoteAudioLevel",n.AvailableCamsUpdated="availableCamsUpdated",n.AvailableMicsUpdated="availableMicsUpdated",n.AvailableSpeakersUpdated="availableSpeakersUpdated",n.CamUpdated="camUpdated",n.MicUpdated="micUpdated",n.SpeakerUpdated="speakerUpdated"})(ve||(ve={}));var Ai={};ct(Ai,"RTVI_PROTOCOL_VERSION",()=>wv);ct(Ai,"RTVI_MESSAGE_LABEL",()=>Sv);ct(Ai,"RTVIMessageType",()=>Pe);ct(Ai,"setAboutClient",()=>Du);ct(Ai,"RTVIMessage",()=>Xt);const wv="1.0.0",Sv="rtvi-ai";var Pe;(function(n){n.CLIENT_READY="client-ready",n.DISCONNECT_BOT="disconnect-bot",n.CLIENT_MESSAGE="client-message",n.APPEND_TO_CONTEXT="append-to-context",n.BOT_READY="bot-ready",n.ERROR="error",n.METRICS="metrics",n.SERVER_MESSAGE="server-message",n.SERVER_RESPONSE="server-response",n.ERROR_RESPONSE="error-response",n.APPEND_TO_CONTEXT_RESULT="append-to-context-result",n.USER_TRANSCRIPTION="user-transcription",n.BOT_TRANSCRIPTION="bot-transcription",n.USER_STARTED_SPEAKING="user-started-speaking",n.USER_STOPPED_SPEAKING="user-stopped-speaking",n.BOT_STARTED_SPEAKING="bot-started-speaking",n.BOT_STOPPED_SPEAKING="bot-stopped-speaking",n.USER_LLM_TEXT="user-llm-text",n.BOT_LLM_TEXT="bot-llm-text",n.BOT_LLM_STARTED="bot-llm-started",n.BOT_LLM_STOPPED="bot-llm-stopped",n.LLM_FUNCTION_CALL="llm-function-call",n.LLM_FUNCTION_CALL_RESULT="llm-function-call-result",n.BOT_LLM_SEARCH_RESPONSE="bot-llm-search-response",n.BOT_TTS_TEXT="bot-tts-text",n.BOT_TTS_STARTED="bot-tts-started",n.BOT_TTS_STOPPED="bot-tts-stopped"})(Pe||(Pe={}));let Es;function Du(n){Es?Es={...Es,...n}:Es=n}class Xt{constructor(e,i,s){this.label=Sv,this.type=e,this.data=i,this.id=s||J_().slice(0,8)}static clientReady(){return new Xt(Pe.CLIENT_READY,{version:wv,about:Es||{library:Ci.name,library_version:Ci.version}})}static disconnectBot(){return new Xt(Pe.DISCONNECT_BOT,{})}static error(e,i=!1){return new Xt(Pe.ERROR,{message:e,fatal:i})}}yr(xa,ew);yr(xa,Jr);yr(xa,_v);yr(xa,Ai);function Aa(n,e,i){const s=i.value;return i.value=function(...a){if(this.state==="ready")return s.apply(this,a);throw new yv(`Attempt to call ${e.toString()} when transport not in ready state. Await connect() first.`)},i}var kv={};ct(kv,"MessageDispatcher",()=>bv);var Iu={};ct(Iu,"LogLevel",()=>pr);ct(Iu,"logger",()=>Be);var pr;(function(n){n[n.NONE=0]="NONE",n[n.ERROR=1]="ERROR",n[n.WARN=2]="WARN",n[n.INFO=3]="INFO",n[n.DEBUG=4]="DEBUG"})(pr||(pr={}));class wi{constructor(){this.level=pr.DEBUG}static getInstance(){return wi.instance||(wi.instance=new wi),wi.instance}setLevel(e){this.level=e}debug(...e){this.level>=pr.DEBUG&&console.debug(...e)}info(...e){this.level>=pr.INFO&&console.info(...e)}warn(...e){this.level>=pr.WARN&&console.warn(...e)}error(...e){this.level>=pr.ERROR&&console.error(...e)}}const Be=wi.getInstance();class bv{constructor(e){this._queue=new Array,this._gcInterval=void 0,this._queue=[],this._sendMethod=e}disconnect(){this.clearQueue(),clearInterval(this._gcInterval),this._gcInterval=void 0}dispatch(e,i=Pe.CLIENT_MESSAGE,s=1e4){this._gcInterval||(this._gcInterval=setInterval(()=>{this._gc()},2e3));const a=new Xt(i,e),c=new Promise((d,h)=>{this._queue.push({message:a,timestamp:Date.now(),timeout:s,resolve:d,reject:h})});Be.debug("[MessageDispatcher] dispatch",a);try{this._sendMethod(a)}catch(d){return Be.error("[MessageDispatcher] Error sending message",d),Promise.reject(d)}return this._gc(),c}clearQueue(){this._queue=[]}_resolveReject(e,i=!0){const s=this._queue.find(a=>a.message.id===e.id);return s&&(i?(Be.debug("[MessageDispatcher] Resolve",e),s.resolve(e)):(Be.debug("[MessageDispatcher] Reject",e),s.reject(e)),this._queue=this._queue.filter(a=>a.message.id!==e.id),Be.debug("[MessageDispatcher] Queue",this._queue)),e}resolve(e){return this._resolveReject(e,!0)}reject(e){return this._resolveReject(e,!1)}_gc(){const e=[];this._queue=this._queue.filter(i=>{const s=Date.now()-i.timestamp<i.timeout;return s||e.push(i),s}),e.forEach(i=>{i.message.type===Pe.CLIENT_MESSAGE&&i.reject(new Xt(Pe.ERROR_RESPONSE,{error:"Timed out waiting for response",msgType:i.message.data.t,data:i.message.data.d,fatal:!1}))}),Be.debug("[MessageDispatcher] GC",this._queue)}}function rw(n){return typeof n=="object"&&n!==null&&Object.keys(n).includes("endpoint")?typeof n.endpoint=="string":!1}async function iw(n,e){e||(e=new AbortController);let i;return new Promise((s,a)=>{(async()=>(n.timeout&&(i=setTimeout(async()=>{e.abort(),a(new Error("Timed out"))},n.timeout)),Be.debug(`[Pipecat Client] Fetching connection params from ${n.endpoint}`),fetch(n.endpoint,{method:"POST",mode:"cors",headers:new Headers({"Content-Type":"application/json",...Object.fromEntries((n.headers??new Headers).entries())}),body:JSON.stringify(n.requestData),signal:e==null?void 0:e.signal}).then(c=>{if(Be.debug(`[Pipecat Client] Received response from ${n.endpoint}`,c),!c.ok)throw c;c.json().then(d=>s(d))}).catch(c=>{Be.error(`[Pipecat Client] Error fetching connection params: ${c}`),c instanceof Response?c.json().then(d=>{a(new su(d.info??d.detail??c.statusText,c.status))}):a(new su)}).finally(()=>{i&&clearTimeout(i)})))()})}var Fu={};ct(Fu,"Transport",()=>Ev);ct(Fu,"TransportWrapper",()=>Cv);class Ev{constructor(){this._state="disconnected"}connect(e){this._abortController=new AbortController;let i=e;try{i=this._validateConnectionParams(e)}catch(s){throw new mn(`Invalid connection params: ${s.message}. Please check your connection params and try again.`)}return this._connect(i)}disconnect(){return this._abortController&&this._abortController.abort(),this._disconnect()}}class Cv{constructor(e){this._transport=e,this._proxy=new Proxy(this._transport,{get:(i,s,a)=>{if(typeof i[s]=="function"){let c;switch(String(s)){case"initialize":c="Direct calls to initialize() are disabled and used internally by the PipecatClient.";break;case"initDevices":c="Direct calls to initDevices() are disabled. Please use the PipecatClient.initDevices() wrapper or let PipecatClient.connect() call it for you.";break;case"sendReadyMessage":c="Direct calls to sendReadyMessage() are disabled and used internally by the PipecatClient.";break;case"connect":c="Direct calls to connect() are disabled. Please use the PipecatClient.connect() wrapper.";break;case"disconnect":c="Direct calls to disconnect() are disabled. Please use the PipecatClient.disconnect() wrapper.";break}return c?()=>{throw new Error(c)}:(...d)=>i[s](...d)}return Reflect.get(i,s,a)}})}get proxy(){return this._proxy}}function sw(){var i,s,a,c,d,h;let n={library:Ci.name,library_version:Ci.version,platform_details:{}},e=null;if((i=window==null?void 0:window.navigator)!=null&&i.userAgent)try{e=X_.parse(window.navigator.userAgent)}catch{}return(s=e==null?void 0:e.browser)!=null&&s.name&&(n.platform_details.browser=e.browser.name),((a=e==null?void 0:e.browser)==null?void 0:a.name)==="Safari"&&!e.browser.version?n.platform_details.browser_version="Web View":(c=e==null?void 0:e.browser)!=null&&c.version&&(n.platform_details.browser_version=e.browser.version),(d=e==null?void 0:e.platform)!=null&&d.type&&(n.platform_details.platform_type=e.platform.type),(h=e==null?void 0:e.engine)!=null&&h.name&&(n.platform_details.engine=e.engine.name),e!=null&&e.os&&(n.platform=e.os.name,n.platform_version=e.os.version),n}var La=function(n,e,i,s){var a=arguments.length,c=a<3?e:s===null?s=Object.getOwnPropertyDescriptor(e,i):s,d;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")c=Reflect.decorate(n,e,i,s);else for(var h=n.length-1;h>=0;h--)(d=n[h])&&(c=(a<3?d(c):a>3?d(e,i,c):d(e,i))||c);return a>3&&c&&Object.defineProperty(e,i,c),c};class ow extends U_{}class Li extends ow{constructor(e){super(),this._functionCallCallbacks={},Du(sw()),this._transport=e.transport,this._transportWrapper=new Cv(this._transport);const i={...e.callbacks,onMessageError:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onMessageError)==null||c.call(a,s),this.emit(ve.MessageError,s)},onError:s=>{var c,d;(d=(c=e==null?void 0:e.callbacks)==null?void 0:c.onError)==null||d.call(c,s);try{this.emit(ve.Error,s)}catch{Be.debug("Could not emit error",s)}const a=s.data;a!=null&&a.fatal&&(Be.error("Fatal error reported. Disconnecting..."),this.disconnect())},onConnected:()=>{var s,a;(a=(s=e==null?void 0:e.callbacks)==null?void 0:s.onConnected)==null||a.call(s),this.emit(ve.Connected)},onDisconnected:()=>{var s,a;(a=(s=e==null?void 0:e.callbacks)==null?void 0:s.onDisconnected)==null||a.call(s),this.emit(ve.Disconnected)},onTransportStateChanged:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onTransportStateChanged)==null||c.call(a,s),this.emit(ve.TransportStateChanged,s)},onParticipantJoined:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onParticipantJoined)==null||c.call(a,s),this.emit(ve.ParticipantConnected,s)},onParticipantLeft:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onParticipantLeft)==null||c.call(a,s),this.emit(ve.ParticipantLeft,s)},onTrackStarted:(s,a)=>{var c,d;(d=(c=e==null?void 0:e.callbacks)==null?void 0:c.onTrackStarted)==null||d.call(c,s,a),this.emit(ve.TrackStarted,s,a)},onTrackStopped:(s,a)=>{var c,d;(d=(c=e==null?void 0:e.callbacks)==null?void 0:c.onTrackStopped)==null||d.call(c,s,a),this.emit(ve.TrackStopped,s,a)},onScreenTrackStarted:(s,a)=>{var c,d;(d=(c=e==null?void 0:e.callbacks)==null?void 0:c.onScreenTrackStarted)==null||d.call(c,s,a),this.emit(ve.ScreenTrackStarted,s,a)},onScreenTrackStopped:(s,a)=>{var c,d;(d=(c=e==null?void 0:e.callbacks)==null?void 0:c.onScreenTrackStopped)==null||d.call(c,s,a),this.emit(ve.ScreenTrackStopped,s,a)},onScreenShareError:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onScreenShareError)==null||c.call(a,s),this.emit(ve.ScreenShareError,s)},onAvailableCamsUpdated:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onAvailableCamsUpdated)==null||c.call(a,s),this.emit(ve.AvailableCamsUpdated,s)},onAvailableMicsUpdated:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onAvailableMicsUpdated)==null||c.call(a,s),this.emit(ve.AvailableMicsUpdated,s)},onAvailableSpeakersUpdated:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onAvailableSpeakersUpdated)==null||c.call(a,s),this.emit(ve.AvailableSpeakersUpdated,s)},onCamUpdated:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onCamUpdated)==null||c.call(a,s),this.emit(ve.CamUpdated,s)},onMicUpdated:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onMicUpdated)==null||c.call(a,s),this.emit(ve.MicUpdated,s)},onSpeakerUpdated:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onSpeakerUpdated)==null||c.call(a,s),this.emit(ve.SpeakerUpdated,s)},onBotConnected:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onBotConnected)==null||c.call(a,s),this.emit(ve.BotConnected,s)},onBotReady:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onBotReady)==null||c.call(a,s),this.emit(ve.BotReady,s)},onBotDisconnected:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onBotDisconnected)==null||c.call(a,s),this.emit(ve.BotDisconnected,s)},onBotStartedSpeaking:()=>{var s,a;(a=(s=e==null?void 0:e.callbacks)==null?void 0:s.onBotStartedSpeaking)==null||a.call(s),this.emit(ve.BotStartedSpeaking)},onBotStoppedSpeaking:()=>{var s,a;(a=(s=e==null?void 0:e.callbacks)==null?void 0:s.onBotStoppedSpeaking)==null||a.call(s),this.emit(ve.BotStoppedSpeaking)},onRemoteAudioLevel:(s,a)=>{var c,d;(d=(c=e==null?void 0:e.callbacks)==null?void 0:c.onRemoteAudioLevel)==null||d.call(c,s,a),this.emit(ve.RemoteAudioLevel,s,a)},onUserStartedSpeaking:()=>{var s,a;(a=(s=e==null?void 0:e.callbacks)==null?void 0:s.onUserStartedSpeaking)==null||a.call(s),this.emit(ve.UserStartedSpeaking)},onUserStoppedSpeaking:()=>{var s,a;(a=(s=e==null?void 0:e.callbacks)==null?void 0:s.onUserStoppedSpeaking)==null||a.call(s),this.emit(ve.UserStoppedSpeaking)},onLocalAudioLevel:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onLocalAudioLevel)==null||c.call(a,s),this.emit(ve.LocalAudioLevel,s)},onUserTranscript:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onUserTranscript)==null||c.call(a,s),this.emit(ve.UserTranscript,s)},onBotTranscript:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onBotTranscript)==null||c.call(a,s),this.emit(ve.BotTranscript,s)},onBotLlmText:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onBotLlmText)==null||c.call(a,s),this.emit(ve.BotLlmText,s)},onBotLlmStarted:()=>{var s,a;(a=(s=e==null?void 0:e.callbacks)==null?void 0:s.onBotLlmStarted)==null||a.call(s),this.emit(ve.BotLlmStarted)},onBotLlmStopped:()=>{var s,a;(a=(s=e==null?void 0:e.callbacks)==null?void 0:s.onBotLlmStopped)==null||a.call(s),this.emit(ve.BotLlmStopped)},onBotTtsText:s=>{var a,c;(c=(a=e==null?void 0:e.callbacks)==null?void 0:a.onBotTtsText)==null||c.call(a,s),this.emit(ve.BotTtsText,s)},onBotTtsStarted:()=>{var s,a;(a=(s=e==null?void 0:e.callbacks)==null?void 0:s.onBotTtsStarted)==null||a.call(s),this.emit(ve.BotTtsStarted)},onBotTtsStopped:()=>{var s,a;(a=(s=e==null?void 0:e.callbacks)==null?void 0:s.onBotTtsStopped)==null||a.call(s),this.emit(ve.BotTtsStopped)}};this._options={...e,callbacks:i,enableMic:e.enableMic??!0,enableCam:e.enableCam??!1,enableScreenShare:e.enableScreenShare??!1},this._initialize(),Be.debug("[Pipecat Client] Initialized",this.version)}setLogLevel(e){Be.setLevel(e)}async initDevices(){Be.debug("[Pipecat Client] Initializing devices..."),await this._transport.initDevices()}async connect(e){if(["authenticating","connecting","connected","ready"].includes(this._transport.state))throw new mn("Voice client has already been started. Please call disconnect() before starting again.");return new Promise((i,s)=>{(async()=>{var a;this._startResolve=i,this._transport.state==="disconnected"&&await this._transport.initDevices();try{let c;if(e)if(rw(e)){if(this._transport.state="authenticating",this._abortController=new AbortController,c=await iw(e,this._abortController),(a=this._abortController)!=null&&a.signal.aborted)return;this._transport.state="authenticated"}else c=e;await this._transport.connect(c),await this._transport.sendReadyMessage()}catch(c){this.disconnect(),s(c);return}})()})}async disconnect(){await this._transport.disconnect(),this._messageDispatcher.disconnect()}_initialize(){this._transport.initialize(this._options,this.handleMessage.bind(this)),this._messageDispatcher=new bv(this._transport.sendMessage.bind(this._transport))}get connected(){return["connected","ready"].includes(this._transport.state)}get transport(){return this._transportWrapper.proxy}get state(){return this._transport.state}get version(){return Z_(Ci).version}async getAllMics(){return await this._transport.getAllMics()}async getAllCams(){return await this._transport.getAllCams()}async getAllSpeakers(){return await this._transport.getAllSpeakers()}get selectedMic(){return this._transport.selectedMic}get selectedCam(){return this._transport.selectedCam}get selectedSpeaker(){return this._transport.selectedSpeaker}updateMic(e){this._transport.updateMic(e)}updateCam(e){this._transport.updateCam(e)}updateSpeaker(e){this._transport.updateSpeaker(e)}enableMic(e){this._transport.enableMic(e)}get isMicEnabled(){return this._transport.isMicEnabled}enableCam(e){this._transport.enableCam(e)}get isCamEnabled(){return this._transport.isCamEnabled}tracks(){return this._transport.tracks()}enableScreenShare(e){return this._transport.enableScreenShare(e)}get isSharingScreen(){return this._transport.isSharingScreen}sendClientMessage(e,i){this._transport.sendMessage(new Xt(Pe.CLIENT_MESSAGE,{t:e,d:i}))}async sendClientRequest(e,i,s){const a={t:e,d:i};return(await this._messageDispatcher.dispatch(a,Pe.CLIENT_MESSAGE,s)).data.d}registerFunctionCallHandler(e,i){this._functionCallCallbacks[e]=i}async appendToContext(e){return!!(await this._messageDispatcher.dispatch({role:e.role,content:e.content,run_immediately:e.run_immediately},Pe.APPEND_TO_CONTEXT)).data.result}disconnectBot(){this._transport.sendMessage(new Xt(Pe.DISCONNECT_BOT,{}))}handleMessage(e){var i,s,a,c,d,h,m,_,w,E,x,T,P,L,F,Y,ne,re,B,I,U,R,z,X,$,te,se,pe,_e,Oe,Re,he,Z,ae,ie,O,V;switch(Be.debug("[RTVI Message]",e),e.type){case Pe.BOT_READY:{const u=e.data,g=u.version?u.version.split(".").map(Number):[0,0,0];Be.debug(`[Pipecat Client] Bot is ready. Version: ${u.version}`),g[0]<1&&Be.warn("[Pipecat Client] Bot version is less than 1.0.0, which may not be compatible with this client."),(i=this._startResolve)==null||i.call(this,e.data),(a=(s=this._options.callbacks)==null?void 0:s.onBotReady)==null||a.call(s,e.data);break}case Pe.ERROR:(d=(c=this._options.callbacks)==null?void 0:c.onError)==null||d.call(c,e);break;case Pe.SERVER_RESPONSE:this._messageDispatcher.resolve(e);break;case Pe.ERROR_RESPONSE:{const u=this._messageDispatcher.reject(e);(m=(h=this._options.callbacks)==null?void 0:h.onMessageError)==null||m.call(h,u);break}case Pe.USER_STARTED_SPEAKING:(w=(_=this._options.callbacks)==null?void 0:_.onUserStartedSpeaking)==null||w.call(_);break;case Pe.USER_STOPPED_SPEAKING:(x=(E=this._options.callbacks)==null?void 0:E.onUserStoppedSpeaking)==null||x.call(E);break;case Pe.BOT_STARTED_SPEAKING:(P=(T=this._options.callbacks)==null?void 0:T.onBotStartedSpeaking)==null||P.call(T);break;case Pe.BOT_STOPPED_SPEAKING:(F=(L=this._options.callbacks)==null?void 0:L.onBotStoppedSpeaking)==null||F.call(L);break;case Pe.USER_TRANSCRIPTION:{const u=e.data;(ne=(Y=this._options.callbacks)==null?void 0:Y.onUserTranscript)==null||ne.call(Y,u);break}case Pe.BOT_TRANSCRIPTION:(B=(re=this._options.callbacks)==null?void 0:re.onBotTranscript)==null||B.call(re,e.data);break;case Pe.BOT_LLM_TEXT:(U=(I=this._options.callbacks)==null?void 0:I.onBotLlmText)==null||U.call(I,e.data);break;case Pe.BOT_LLM_STARTED:(z=(R=this._options.callbacks)==null?void 0:R.onBotLlmStarted)==null||z.call(R);break;case Pe.BOT_LLM_STOPPED:($=(X=this._options.callbacks)==null?void 0:X.onBotLlmStopped)==null||$.call(X);break;case Pe.BOT_TTS_TEXT:(se=(te=this._options.callbacks)==null?void 0:te.onBotTtsText)==null||se.call(te,e.data);break;case Pe.BOT_TTS_STARTED:(_e=(pe=this._options.callbacks)==null?void 0:pe.onBotTtsStarted)==null||_e.call(pe);break;case Pe.BOT_TTS_STOPPED:(Re=(Oe=this._options.callbacks)==null?void 0:Oe.onBotTtsStopped)==null||Re.call(Oe);break;case Pe.METRICS:(Z=(he=this._options.callbacks)==null?void 0:he.onMetrics)==null||Z.call(he,e.data),this.emit(ve.Metrics,e.data);break;case Pe.APPEND_TO_CONTEXT_RESULT:case Pe.SERVER_MESSAGE:(ie=(ae=this._options.callbacks)==null?void 0:ae.onServerMessage)==null||ie.call(ae,e.data),this.emit(ve.ServerMessage,e.data);break;case Pe.LLM_FUNCTION_CALL:{const u=e.data,g=this._functionCallCallbacks[u.function_name];if(g){const v={functionName:u.function_name,arguments:u.args};g(v).then(S=>{this._transport.sendMessage(new Xt(Pe.LLM_FUNCTION_CALL_RESULT,{function_name:u.function_name,tool_call_id:u.tool_call_id,arguments:u.args,result:S}))})}break}case Pe.BOT_LLM_SEARCH_RESPONSE:{const u=e.data;(V=(O=this._options.callbacks)==null?void 0:O.onBotLlmSearchResponse)==null||V.call(O,u),this.emit(ve.BotLlmSearchResponse,u);break}default:Be.debug("[Pipecat Client] Unrecognized message type",e.type);break}}}La([Aa],Li.prototype,"sendClientMessage",null);La([Aa],Li.prototype,"sendClientRequest",null);La([Aa],Li.prototype,"appendToContext",null);La([Aa],Li.prototype,"disconnectBot",null);yr(Oa,vv);yr(Oa,kv);yr(Oa,Iu);yr(Oa,Fu);const Cs={},ih=(n,e)=>n.unstable_is?n.unstable_is(e):e===n,sh=n=>"init"in n,Dc=n=>!!n.write,oh=n=>"v"in n||"e"in n,Ko=n=>{if("e"in n)throw n.e;if((Cs?"production":void 0)!=="production"&&!("v"in n))throw new Error("[Bug] atom state is not initialized");return n.v},va=new WeakMap,ah=n=>{var e;return ga(n)&&!!((e=va.get(n))!=null&&e[0])},aw=n=>{const e=va.get(n);e!=null&&e[0]&&(e[0]=!1,e[1].forEach(i=>i()))},Mv=(n,e)=>{let i=va.get(n);if(!i){i=[!0,new Set],va.set(n,i);const s=()=>{i[0]=!1};n.then(s,s)}i[1].add(e)},ga=n=>typeof(n==null?void 0:n.then)=="function",Tv=(n,e,i)=>{i.p.has(n)||(i.p.add(n),e.then(()=>{i.p.delete(n)},()=>{i.p.delete(n)}))},Ic=(n,e,i)=>{const s=i(n),a="v"in s,c=s.v;if(ga(e))for(const d of s.d.keys())Tv(n,e,i(d));s.v=e,delete s.e,(!a||!Object.is(c,s.v))&&(++s.n,ga(c)&&aw(c))},lh=(n,e,i)=>{var s;const a=new Set;for(const c of((s=i.get(n))==null?void 0:s.t)||[])i.has(c)&&a.add(c);for(const c of e.p)a.add(c);return a},lw=()=>{const n=new Set,e=()=>{n.forEach(i=>i())};return e.add=i=>(n.add(i),()=>{n.delete(i)}),e},Fc=()=>{const n={},e=new WeakMap,i=s=>{var a,c;(a=e.get(n))==null||a.forEach(d=>d(s)),(c=e.get(s))==null||c.forEach(d=>d())};return i.add=(s,a)=>{const c=s||n,d=(e.has(c)?e:e.set(c,new Set)).get(c);return d.add(a),()=>{d==null||d.delete(a),d.size||e.delete(c)}},i},cw=n=>(n.c||(n.c=Fc()),n.m||(n.m=Fc()),n.u||(n.u=Fc()),n.f||(n.f=lw()),n),uw=Symbol(),dw=(n=new WeakMap,e=new WeakMap,i=new WeakMap,s=new Set,a=new Set,c=new Set,d={},h=(x,...T)=>x.read(...T),m=(x,...T)=>x.write(...T),_=(x,T)=>{var P;return(P=x.unstable_onInit)==null?void 0:P.call(x,T)},w=(x,T)=>{var P;return(P=x.onMount)==null?void 0:P.call(x,T)},...E)=>{const x=E[0]||(R=>{if((Cs?"production":void 0)!=="production"&&!R)throw new Error("Atom is undefined or null");let z=n.get(R);return z||(z={d:new Map,p:new Set,n:0},n.set(R,z),_==null||_(R,U)),z}),T=E[1]||(()=>{const R=[],z=X=>{try{X()}catch($){R.push($)}};do{d.f&&z(d.f);const X=new Set,$=X.add.bind(X);s.forEach(te=>{var se;return(se=e.get(te))==null?void 0:se.l.forEach($)}),s.clear(),c.forEach($),c.clear(),a.forEach($),a.clear(),X.forEach(z),s.size&&P()}while(s.size||c.size||a.size);if(R.length)throw new AggregateError(R)}),P=E[2]||(()=>{const R=[],z=new WeakSet,X=new WeakSet,$=Array.from(s);for(;$.length;){const te=$[$.length-1],se=x(te);if(X.has(te)){$.pop();continue}if(z.has(te)){if(i.get(te)===se.n)R.push([te,se]);else if((Cs?"production":void 0)!=="production"&&i.has(te))throw new Error("[Bug] invalidated atom exists");X.add(te),$.pop();continue}z.add(te);for(const pe of lh(te,se,e))z.has(pe)||$.push(pe)}for(let te=R.length-1;te>=0;--te){const[se,pe]=R[te];let _e=!1;for(const Oe of pe.d.keys())if(Oe!==se&&s.has(Oe)){_e=!0;break}_e&&(L(se),ne(se)),i.delete(se)}}),L=E[3]||(R=>{var z;const X=x(R);if(oh(X)&&(e.has(R)&&i.get(R)!==X.n||Array.from(X.d).every(([he,Z])=>L(he).n===Z)))return X;X.d.clear();let $=!0;const te=()=>{e.has(R)&&(ne(R),P(),T())},se=he=>{var Z;if(ih(R,he)){const ie=x(he);if(!oh(ie))if(sh(he))Ic(he,he.init,x);else throw new Error("no atom init");return Ko(ie)}const ae=L(he);try{return Ko(ae)}finally{X.d.set(he,ae.n),ah(X.v)&&Tv(R,X.v,ae),(Z=e.get(he))==null||Z.t.add(R),$||te()}};let pe,_e;const Oe={get signal(){return pe||(pe=new AbortController),pe.signal},get setSelf(){return(Cs?"production":void 0)!=="production"&&!Dc(R)&&console.warn("setSelf function cannot be used with read-only atom"),!_e&&Dc(R)&&(_e=(...he)=>{if((Cs?"production":void 0)!=="production"&&$&&console.warn("setSelf function cannot be called in sync"),!$)try{return Y(R,...he)}finally{P(),T()}}),_e}},Re=X.n;try{const he=h(R,se,Oe);return Ic(R,he,x),ga(he)&&(Mv(he,()=>pe==null?void 0:pe.abort()),he.then(te,te)),X}catch(he){return delete X.v,X.e=he,++X.n,X}finally{$=!1,Re!==X.n&&i.get(R)===Re&&(i.set(R,X.n),s.add(R),(z=d.c)==null||z.call(d,R))}}),F=E[4]||(R=>{const z=[R];for(;z.length;){const X=z.pop(),$=x(X);for(const te of lh(X,$,e)){const se=x(te);i.set(te,se.n),z.push(te)}}}),Y=E[5]||((R,...z)=>{let X=!0;const $=se=>Ko(L(se)),te=(se,...pe)=>{var _e;const Oe=x(se);try{if(ih(R,se)){if(!sh(se))throw new Error("atom not writable");const Re=Oe.n,he=pe[0];Ic(se,he,x),ne(se),Re!==Oe.n&&(s.add(se),(_e=d.c)==null||_e.call(d,se),F(se));return}else return Y(se,...pe)}finally{X||(P(),T())}};try{return m(R,$,te,...z)}finally{X=!1}}),ne=E[6]||(R=>{var z;const X=x(R),$=e.get(R);if($&&!ah(X.v)){for(const[te,se]of X.d)if(!$.d.has(te)){const pe=x(te);re(te).t.add(R),$.d.add(te),se!==pe.n&&(s.add(te),(z=d.c)==null||z.call(d,te),F(te))}for(const te of $.d||[])if(!X.d.has(te)){$.d.delete(te);const se=B(te);se==null||se.t.delete(R)}}}),re=E[7]||(R=>{var z;const X=x(R);let $=e.get(R);if(!$){L(R);for(const te of X.d.keys())re(te).t.add(R);if($={l:new Set,d:new Set(X.d.keys()),t:new Set},e.set(R,$),(z=d.m)==null||z.call(d,R),Dc(R)){const te=()=>{let se=!0;const pe=(..._e)=>{try{return Y(R,..._e)}finally{se||(P(),T())}};try{const _e=w(R,pe);_e&&($.u=()=>{se=!0;try{_e()}finally{se=!1}})}finally{se=!1}};a.add(te)}}return $}),B=E[8]||(R=>{var z;const X=x(R);let $=e.get(R);if($&&!$.l.size&&!Array.from($.t).some(te=>{var se;return(se=e.get(te))==null?void 0:se.d.has(R)})){$.u&&c.add($.u),$=void 0,e.delete(R),(z=d.u)==null||z.call(d,R);for(const te of X.d.keys()){const se=B(te);se==null||se.t.delete(R)}return}return $}),I=[n,e,i,s,a,c,d,h,m,_,w,x,T,P,L,F,Y,ne,re,B],U={get:R=>Ko(L(R)),set:(R,...z)=>{try{return Y(R,...z)}finally{P(),T()}},sub:(R,z)=>{const $=re(R).l;return $.add(z),T(),()=>{$.delete(z),B(R),T()}}};return Object.defineProperty(U,uw,{value:I}),U},Pv=dw,fw=cw,ch=Mv,Bu={};let pw=0;function jt(n,e){const i=`atom${++pw}`,s={toString(){return(Bu?"production":void 0)!=="production"&&this.debugLabel?i+":"+this.debugLabel:i}};return typeof n=="function"?s.read=n:(s.init=n,s.read=hw,s.write=mw),e&&(s.write=e),s}function hw(n){return n(this)}function mw(n,e,i){return e(this,typeof i=="function"?i(n(this)):i)}const vw=()=>{let n=0;const e=fw({}),i=new WeakMap,s=new WeakMap,a=Pv(i,s,void 0,void 0,void 0,void 0,e,void 0,(h,m,_,...w)=>n?_(h,...w):h.write(m,_,...w)),c=new Set;return e.m.add(void 0,h=>{c.add(h);const m=i.get(h);m.m=s.get(h)}),e.u.add(void 0,h=>{c.delete(h);const m=i.get(h);delete m.m}),Object.assign(a,{dev4_get_internal_weak_map:()=>(console.log("Deprecated: Use devstore from the devtools library"),i),dev4_get_mounted_atoms:()=>c,dev4_restore_atoms:h=>{const m={read:()=>null,write:(_,w)=>{++n;try{for(const[E,x]of h)"init"in E&&w(E,x)}finally{--n}}};a.set(m)}})};function $u(){return(Bu?"production":void 0)!=="production"?vw():Pv()}let Ss;function gw(){return Ss||(Ss=$u(),(Bu?"production":void 0)!=="production"&&(globalThis.__JOTAI_DEFAULT_STORE__||(globalThis.__JOTAI_DEFAULT_STORE__=Ss),globalThis.__JOTAI_DEFAULT_STORE__!==Ss&&console.warn("Detected multiple Jotai instances. It may cause unexpected behavior with the default store. https://github.com/pmndrs/jotai/discussions/2044"))),Ss}const yw={},Ov=ke.createContext(void 0);function xv(n){const e=ke.useContext(Ov);return(n==null?void 0:n.store)||e||gw()}function _w({children:n,store:e}){const i=ke.useRef(void 0);return!e&&!i.current&&(i.current=$u()),ke.createElement(Ov.Provider,{value:e||i.current},n)}const ou=n=>typeof(n==null?void 0:n.then)=="function",au=n=>{n.status||(n.status="pending",n.then(e=>{n.status="fulfilled",n.value=e},e=>{n.status="rejected",n.reason=e}))},ww=Pa.use||(n=>{if(n.status==="pending")throw n;if(n.status==="fulfilled")return n.value;throw n.status==="rejected"?n.reason:(au(n),n)}),Bc=new WeakMap,uh=(n,e)=>{let i=Bc.get(n);return i||(i=new Promise((s,a)=>{let c=n;const d=_=>w=>{c===_&&s(w)},h=_=>w=>{c===_&&a(w)},m=()=>{try{const _=e();ou(_)?(Bc.set(_,i),c=_,_.then(d(_),h(_)),ch(_,m)):s(_)}catch(_){a(_)}};n.then(d(n),h(n)),ch(n,m)}),Bc.set(n,i)),i};function Av(n,e){const{delay:i,unstable_promiseStatus:s=!Pa.use}=e||{},a=xv(e),[[c,d,h],m]=ke.useReducer(w=>{const E=a.get(n);return Object.is(w[0],E)&&w[1]===a&&w[2]===n?w:[E,a,n]},void 0,()=>[a.get(n),a,n]);let _=c;if((d!==a||h!==n)&&(m(),_=a.get(n)),ke.useEffect(()=>{const w=a.sub(n,()=>{if(s)try{const E=a.get(n);ou(E)&&au(uh(E,()=>a.get(n)))}catch{}if(typeof i=="number"){setTimeout(m,i);return}m()});return m(),w},[a,n,i,s]),ke.useDebugValue(_),ou(_)){const w=uh(_,()=>a.get(n));return s&&au(w),ww(w)}return _}function Lv(n,e){const i=xv(e);return ke.useCallback((...a)=>{if((yw?"production":void 0)!=="production"&&!("write"in n))throw new Error("not writable atom");return i.set(n,...a)},[i,n])}function Sw(n,e){return[Av(n,e),Lv(n,e)]}const kw={};function bw(n,e){let i=null;const s=new Map,a=new Set,c=h=>{let m;if(e===void 0)m=s.get(h);else for(const[w,E]of s)if(e(w,h)){m=E;break}if(m!==void 0)if(i!=null&&i(m[1],h))c.remove(h);else return m[0];const _=n(h);return s.set(h,[_,Date.now()]),d("CREATE",h,_),_},d=(h,m,_)=>{for(const w of a)w({type:h,param:m,atom:_})};return c.unstable_listen=h=>(a.add(h),()=>{a.delete(h)}),c.getParams=()=>s.keys(),c.remove=h=>{if(e===void 0){if(!s.has(h))return;const[m]=s.get(h);s.delete(h),d("REMOVE",h,m)}else for(const[m,[_]]of s)if(e(m,h)){s.delete(m),d("REMOVE",m,_);break}},c.setShouldRemove=h=>{if(i=h,!!i)for(const[m,[_,w]]of s)i(w,m)&&(s.delete(m),d("REMOVE",m,_))},c}const Ew=n=>typeof(n==null?void 0:n.then)=="function";function Cw(n=()=>{try{return window.localStorage}catch(i){(kw?"production":void 0)!=="production"&&typeof window<"u"&&console.warn(i);return}},e){var i;let s,a;const c={getItem:(m,_)=>{var w,E;const x=P=>{if(P=P||"",s!==P){try{a=JSON.parse(P,e==null?void 0:e.reviver)}catch{return _}s=P}return a},T=(E=(w=n())==null?void 0:w.getItem(m))!=null?E:null;return Ew(T)?T.then(x):x(T)},setItem:(m,_)=>{var w;return(w=n())==null?void 0:w.setItem(m,JSON.stringify(_,void 0))},removeItem:m=>{var _;return(_=n())==null?void 0:_.removeItem(m)}},d=m=>(_,w,E)=>m(_,x=>{let T;try{T=JSON.parse(x||"")}catch{T=E}w(T)});let h;try{h=(i=n())==null?void 0:i.subscribe}catch{}return!h&&typeof window<"u"&&typeof window.addEventListener=="function"&&window.Storage&&(h=(m,_)=>{if(!(n()instanceof window.Storage))return()=>{};const w=E=>{E.storageArea===n()&&E.key===m&&_(E.newValue)};return window.addEventListener("storage",w),()=>{window.removeEventListener("storage",w)}}),h&&(c.subscribe=d(h)),c}Cw();function Mw(n,e){const i=ke.useMemo(()=>jt(null,(s,a,...c)=>n(s,a,...c)),[n]);return Lv(i,e)}var lu={};lu=JSON.parse(`{"name":"@pipecat-ai/client-react","version":"1.0.0","license":"BSD-2-Clause","main":"dist/index.js","module":"dist/index.module.js","types":"dist/index.d.ts","source":"src/index.ts","repository":{"type":"git","url":"git+https://github.com/pipecat-ai/pipecat-client-web.git"},"files":["dist","package.json","README.md"],"scripts":{"build":"parcel build --no-cache","dev":"parcel watch","lint":"eslint . --report-unused-disable-directives --max-warnings 0 --ignore-pattern 'dist/'"},"devDependencies":{"@pipecat-ai/client-js":"*","@types/react":"^18.3.3","@types/react-dom":"^18.3.0","@typescript-eslint/eslint-plugin":"^8.32.0","eslint":"^9.11.1","eslint-config-prettier":"^9.1.0","eslint-plugin-react-hooks":"^5.2.0","eslint-plugin-simple-import-sort":"^12.1.1","parcel":"^2.12.0","react":"^18.3.1","react-dom":"^18.3.1","typescript":"^5.2.2"},"peerDependencies":{"@pipecat-ai/client-js":"*","react":">=18","react-dom":">=18"},"dependencies":{"jotai":"^2.9.0"}}`);const Nv=ke.createContext({on:()=>{},off:()=>{}}),Tw=$u(),jv=ke.createContext({}),Rv=({children:n,client:e,jotaiStore:i=Tw})=>{ke.useEffect(()=>{Du({library:lu.name,library_version:lu.version})},[]);const s=ke.useRef({});ke.useEffect(()=>{if(!e)return;const d=Object.values(ve).filter(m=>isNaN(Number(m))),h={};return d.forEach(m=>{const _=(...w)=>{const E=s.current[m];E&&E.forEach(x=>{x(...w)})};h[m]=_,e.on(m,_)}),()=>{d.forEach(m=>{e.off(m,h[m])})}},[e]);const a=ke.useCallback((d,h)=>{s.current[d]||(s.current[d]=new Set),s.current[d].add(h)},[]),c=ke.useCallback((d,h)=>{var m;(m=s.current[d])==null||m.delete(h)},[]);return Le.jsx(_w,{store:i,children:Le.jsx(jv.Provider,{value:{client:e},children:Le.jsx(Nv.Provider,{value:{on:a,off:c},children:n})})})};Rv.displayName="PipecatClientProvider";const Uu=()=>{const{client:n}=ke.useContext(jv);return n},Gt=(n,e)=>{const{on:i,off:s}=ke.useContext(Nv);ke.useEffect(()=>(i(n,e),()=>{s(n,e)}),[n,e,i,s])},Pw=jt(null),Ow=jt(null),xw=jt(null),Aw=jt(null),Lw=jt(null),Nw=jt(null),dh=bw(({local:n,trackType:e})=>{if(n)switch(e){case"audio":return Pw;case"screenAudio":return xw;case"screenVideo":return Aw;case"video":return Ow}return e==="audio"?Lw:Nw}),zu=(n,e)=>{const i=Uu(),s=Av(dh({local:e==="local",trackType:n})),a=Mw(ke.useCallback((c,d,h,m,_)=>{const w=dh({local:_,trackType:m}),E=c(w);(E==null?void 0:E.id)!==h.id&&d(w,h)},[]));return Gt(ve.TrackStarted,ke.useCallback((c,d)=>{a(c,c.kind,!!(d!=null&&d.local))},[a])),Gt(ve.ScreenTrackStarted,ke.useCallback((c,d)=>{const h=c.kind==="audio"?"screenAudio":"screenVideo";a(c,h,!!(d!=null&&d.local))},[a])),ke.useEffect(()=>{var h;if(!i)return;const c=i.tracks(),d=(h=c==null?void 0:c[e])==null?void 0:h[n];d&&a(d,n,e==="local")},[e,n,a,i]),s},Dv=()=>{const n=ke.useRef(null),e=zu("audio","bot");return ke.useEffect(()=>{!n.current||!e||n.current.srcObject&&n.current.srcObject.getAudioTracks()[0].id===e.id||(n.current.srcObject=new MediaStream([e]))},[e]),Gt(ve.SpeakerUpdated,ke.useCallback(i=>{n.current&&typeof n.current.setSinkId=="function"&&n.current.setSinkId(i.deviceId)},[])),Le.jsx(Le.Fragment,{children:Le.jsx("audio",{ref:n,autoPlay:!0})})};Dv.displayName="PipecatClientAudio";const jw=jt("disconnected"),Vu=()=>{const[n,e]=Sw(jw);return Gt(ve.TransportStateChanged,e),n};function Rw(...n){return ke.useCallback(e=>{for(let i=0;i<n.length;i++){const s=n[i];typeof s=="function"?s(e):s&&typeof s=="object"&&(s.current=e)}},n)}var Dw=Rw;const Iv=ke.forwardRef(function({participant:e="local",fit:i="contain",mirror:s,onResize:a,style:c={},trackType:d="video",...h},m){const _=zu(d,e),w=ke.useRef(null),E=Dw(w,m);return ke.useEffect(function(){const T=w.current;if(!T)return;const P=()=>{const re=T.play();re!==void 0&&re.then(()=>{T.controls=!1}).catch(B=>{T.controls=!0,console.warn("Failed to play video",B)})},L=()=>{T.paused&&P()},F=()=>{T.style.transform="scale(1)"},Y=()=>{T.style.transform="",setTimeout(()=>{T.paused&&P()},100)},ne=()=>{document.visibilityState!=="hidden"&&T.paused&&P()};return T.addEventListener("canplay",L),T.addEventListener("enterpictureinpicture",F),T.addEventListener("leavepictureinpicture",Y),document.addEventListener("visibilitychange",ne),()=>{T.removeEventListener("canplay",L),T.removeEventListener("enterpictureinpicture",F),T.removeEventListener("leavepictureinpicture",Y),document.removeEventListener("visibilitychange",ne)}},[]),ke.useEffect(function(){const T=w.current;if(!(!T||!_))return T.srcObject=new MediaStream([_]),T.load(),()=>{T.srcObject=null,T.load()}},[_,_==null?void 0:_.id]),ke.useEffect(function(){const T=w.current;if(!a||!T)return;let P;function L(){P&&cancelAnimationFrame(P),P=requestAnimationFrame(()=>{const F=w.current;if(!F||document.hidden)return;const Y=F.videoWidth,ne=F.videoHeight;Y&&ne&&(a==null||a({aspectRatio:Y/ne,height:ne,width:Y}))})}return L(),T.addEventListener("loadedmetadata",L),T.addEventListener("resize",L),()=>{P&&cancelAnimationFrame(P),T.removeEventListener("loadedmetadata",L),T.removeEventListener("resize",L)}},[a]),Le.jsx("video",{autoPlay:!0,muted:!0,playsInline:!0,ref:E,style:{objectFit:i,transform:s?"scale(-1, 1)":"",...c},...h})});Iv.displayName="PipecatClientVideo";jt([]);jt([]);jt([]);jt({});jt({});jt({});const Iw=Pa.memo(({backgroundColor:n="transparent",barColor:e="black",barCount:i=5,barGap:s=12,barLineCap:a="round",barMaxHeight:c=120,barOrigin:d="center",barWidth:h=30,participantType:m})=>{const _=ke.useRef(null),w=zu("audio",m);return ke.useEffect(()=>{if(!_.current)return;const E=i*h+(i-1)*s,x=c,T=_.current,P=2,L=()=>{T.width=E*P,T.height=x*P,T.style.width=`${E}px`,T.style.height=`${x}px`,F.lineCap=a,F.scale(P,P)},F=T.getContext("2d");if(L(),!w)return;const Y=new AudioContext,ne=Y.createMediaStreamSource(new MediaStream([w])),re=Y.createAnalyser();re.fftSize=1024,ne.connect(re);const B=new Uint8Array(re.frequencyBinCount);F.lineCap=a;const I=Array.from({length:i},($,te)=>{const se=i>20?200:80,pe=1e4,_e=2595*Math.log10(1+se/700),Re=(2595*Math.log10(1+pe/700)-_e)/i,he=_e+te*Re,Z=700*(Math.pow(10,he/2595)-1),ae=700*(Math.pow(10,(he+Re)/2595)-1);return{startFreq:Z,endFreq:ae,smoothValue:0}}),U=$=>{const te=Y.sampleRate/2;return Math.round($/te*(re.frequencyBinCount-1))};function R(){re.getByteFrequencyData(B),F.clearRect(0,0,T.width/P,T.height/P),F.fillStyle=n,F.fillRect(0,0,T.width/P,T.height/P);let $=!1;const te=I.length*h+(I.length-1)*s,se=(T.width/P-te)/2,pe=h/2;I.forEach((_e,Oe)=>{const Re=U(_e.startFreq),he=U(_e.endFreq),Z=B.slice(Re,he),ae=Z.reduce((k,N)=>k+N,0)/Z.length,ie=.2;ae<1?_e.smoothValue=Math.max(_e.smoothValue-ie*5,0):(_e.smoothValue=_e.smoothValue+(ae-_e.smoothValue)*ie,$=!0);const O=se+Oe*(h+s),u=Math.max(0,Math.min(_e.smoothValue/255*c,c));let g,v;const S=T.height/P;switch(d){case"top":g=pe,v=Math.min(pe+u,S-pe);break;case"bottom":v=S-pe,g=Math.max(v-u,pe);break;case"center":default:g=Math.max(S/2-u/2,pe),v=Math.min(S/2+u/2,S-pe);break}_e.smoothValue>0?(F.beginPath(),F.moveTo(O+h/2,g),F.lineTo(O+h/2,v),F.lineWidth=h,F.strokeStyle=e,F.stroke()):z(pe,e,O,g)}),$||X(pe,e),requestAnimationFrame(R)}function z($,te,se,pe){switch(a){case"square":F.fillStyle=te,F.fillRect(se+h/2-$,pe-$,$*2,$*2);break;case"round":default:F.beginPath(),F.arc(se+h/2,pe,$,0,2*Math.PI),F.fillStyle=te,F.fill(),F.closePath();break}}function X($,te){const se=I.length*h+(I.length-1)*s,pe=(T.width/P-se)/2,_e=T.height/P;let Oe;switch(d){case"top":Oe=$;break;case"bottom":Oe=_e-$;break;case"center":default:Oe=_e/2;break}I.forEach((Re,he)=>{const Z=pe+he*(h+s);z($,te,Z,Oe)})}return R(),window.addEventListener("resize",L),()=>{Y.close(),window.removeEventListener("resize",L)}},[n,e,i,s,a,c,d,h,w]),Le.jsx("canvas",{ref:_,style:{display:"block",width:"100%",height:"100%"}})});Iw.displayName="VoiceVisualizer";function fh(n,e){if(n==null)return{};var i,s,a=function(d,h){if(d==null)return{};var m={};for(var _ in d)if({}.hasOwnProperty.call(d,_)){if(h.indexOf(_)!==-1)continue;m[_]=d[_]}return m}(n,e);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(n);for(s=0;s<c.length;s++)i=c[s],e.indexOf(i)===-1&&{}.propertyIsEnumerable.call(n,i)&&(a[i]=n[i])}return a}function Ut(n,e){if(!(n instanceof e))throw new TypeError("Cannot call a class as a function")}function Ne(n){return Ne=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ne(n)}function Fv(n){var e=function(i,s){if(Ne(i)!="object"||!i)return i;var a=i[Symbol.toPrimitive];if(a!==void 0){var c=a.call(i,s);if(Ne(c)!="object")return c;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(i)}(n,"string");return Ne(e)=="symbol"?e:e+""}function ph(n,e){for(var i=0;i<e.length;i++){var s=e[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(n,Fv(s.key),s)}}function zt(n,e,i){return e&&ph(n.prototype,e),i&&ph(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n}function Na(n,e){if(e&&(Ne(e)=="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return function(i){if(i===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i}(n)}function gn(n){return gn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},gn(n)}function xs(n,e){return xs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(i,s){return i.__proto__=s,i},xs(n,e)}function ja(n,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");n.prototype=Object.create(e&&e.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),e&&xs(n,e)}function Mn(n,e,i){return(e=Fv(e))in n?Object.defineProperty(n,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[e]=i,n}function hh(n,e,i,s,a,c,d){try{var h=n[c](d),m=h.value}catch(_){return void i(_)}h.done?e(m):Promise.resolve(m).then(s,a)}function Se(n){return function(){var e=this,i=arguments;return new Promise(function(s,a){var c=n.apply(e,i);function d(m){hh(c,s,a,d,h,"next",m)}function h(m){hh(c,s,a,d,h,"throw",m)}d(void 0)})}}function mh(n,e){(e==null||e>n.length)&&(e=n.length);for(var i=0,s=Array(e);i<e;i++)s[i]=n[i];return s}function Mt(n,e){return function(i){if(Array.isArray(i))return i}(n)||function(i,s){var a=i==null?null:typeof Symbol<"u"&&i[Symbol.iterator]||i["@@iterator"];if(a!=null){var c,d,h,m,_=[],w=!0,E=!1;try{if(h=(a=a.call(i)).next,s===0){if(Object(a)!==a)return;w=!1}else for(;!(w=(c=h.call(a)).done)&&(_.push(c.value),_.length!==s);w=!0);}catch(x){E=!0,d=x}finally{try{if(!w&&a.return!=null&&(m=a.return(),Object(m)!==m))return}finally{if(E)throw d}}return _}}(n,e)||function(i,s){if(i){if(typeof i=="string")return mh(i,s);var a={}.toString.call(i).slice(8,-1);return a==="Object"&&i.constructor&&(a=i.constructor.name),a==="Map"||a==="Set"?Array.from(i):a==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?mh(i,s):void 0}}(n,e)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function Fw(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var Bv,cu={exports:{}},ki=typeof Reflect=="object"?Reflect:null,vh=ki&&typeof ki.apply=="function"?ki.apply:function(n,e,i){return Function.prototype.apply.call(n,e,i)};Bv=ki&&typeof ki.ownKeys=="function"?ki.ownKeys:Object.getOwnPropertySymbols?function(n){return Object.getOwnPropertyNames(n).concat(Object.getOwnPropertySymbols(n))}:function(n){return Object.getOwnPropertyNames(n)};var gh=Number.isNaN||function(n){return n!=n};function Fe(){Fe.init.call(this)}cu.exports=Fe,cu.exports.once=function(n,e){return new Promise(function(i,s){function a(d){n.removeListener(e,c),s(d)}function c(){typeof n.removeListener=="function"&&n.removeListener("error",a),i([].slice.call(arguments))}bh(n,e,c,{once:!0}),e!=="error"&&function(d,h,m){typeof d.on=="function"&&bh(d,"error",h,m)}(n,a,{once:!0})})},Fe.EventEmitter=Fe,Fe.prototype._events=void 0,Fe.prototype._eventsCount=0,Fe.prototype._maxListeners=void 0;var yh=10;function da(n){if(typeof n!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof n)}function $v(n){return n._maxListeners===void 0?Fe.defaultMaxListeners:n._maxListeners}function _h(n,e,i,s){var a,c,d,h;if(da(i),(c=n._events)===void 0?(c=n._events=Object.create(null),n._eventsCount=0):(c.newListener!==void 0&&(n.emit("newListener",e,i.listener?i.listener:i),c=n._events),d=c[e]),d===void 0)d=c[e]=i,++n._eventsCount;else if(typeof d=="function"?d=c[e]=s?[i,d]:[d,i]:s?d.unshift(i):d.push(i),(a=$v(n))>0&&d.length>a&&!d.warned){d.warned=!0;var m=new Error("Possible EventEmitter memory leak detected. "+d.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");m.name="MaxListenersExceededWarning",m.emitter=n,m.type=e,m.count=d.length,h=m,console&&console.warn&&console.warn(h)}return n}function Bw(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function wh(n,e,i){var s={fired:!1,wrapFn:void 0,target:n,type:e,listener:i},a=Bw.bind(s);return a.listener=i,s.wrapFn=a,a}function Sh(n,e,i){var s=n._events;if(s===void 0)return[];var a=s[e];return a===void 0?[]:typeof a=="function"?i?[a.listener||a]:[a]:i?function(c){for(var d=new Array(c.length),h=0;h<d.length;++h)d[h]=c[h].listener||c[h];return d}(a):Uv(a,a.length)}function kh(n){var e=this._events;if(e!==void 0){var i=e[n];if(typeof i=="function")return 1;if(i!==void 0)return i.length}return 0}function Uv(n,e){for(var i=new Array(e),s=0;s<e;++s)i[s]=n[s];return i}function bh(n,e,i,s){if(typeof n.on=="function")s.once?n.once(e,i):n.on(e,i);else{if(typeof n.addEventListener!="function")throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof n);n.addEventListener(e,function a(c){s.once&&n.removeEventListener(e,a),i(c)})}}Object.defineProperty(Fe,"defaultMaxListeners",{enumerable:!0,get:function(){return yh},set:function(n){if(typeof n!="number"||n<0||gh(n))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+n+".");yh=n}}),Fe.init=function(){this._events!==void 0&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},Fe.prototype.setMaxListeners=function(n){if(typeof n!="number"||n<0||gh(n))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+n+".");return this._maxListeners=n,this},Fe.prototype.getMaxListeners=function(){return $v(this)},Fe.prototype.emit=function(n){for(var e=[],i=1;i<arguments.length;i++)e.push(arguments[i]);var s=n==="error",a=this._events;if(a!==void 0)s=s&&a.error===void 0;else if(!s)return!1;if(s){var c;if(e.length>0&&(c=e[0]),c instanceof Error)throw c;var d=new Error("Unhandled error."+(c?" ("+c.message+")":""));throw d.context=c,d}var h=a[n];if(h===void 0)return!1;if(typeof h=="function")vh(h,this,e);else{var m=h.length,_=Uv(h,m);for(i=0;i<m;++i)vh(_[i],this,e)}return!0},Fe.prototype.addListener=function(n,e){return _h(this,n,e,!1)},Fe.prototype.on=Fe.prototype.addListener,Fe.prototype.prependListener=function(n,e){return _h(this,n,e,!0)},Fe.prototype.once=function(n,e){return da(e),this.on(n,wh(this,n,e)),this},Fe.prototype.prependOnceListener=function(n,e){return da(e),this.prependListener(n,wh(this,n,e)),this},Fe.prototype.removeListener=function(n,e){var i,s,a,c,d;if(da(e),(s=this._events)===void 0)return this;if((i=s[n])===void 0)return this;if(i===e||i.listener===e)--this._eventsCount==0?this._events=Object.create(null):(delete s[n],s.removeListener&&this.emit("removeListener",n,i.listener||e));else if(typeof i!="function"){for(a=-1,c=i.length-1;c>=0;c--)if(i[c]===e||i[c].listener===e){d=i[c].listener,a=c;break}if(a<0)return this;a===0?i.shift():function(h,m){for(;m+1<h.length;m++)h[m]=h[m+1];h.pop()}(i,a),i.length===1&&(s[n]=i[0]),s.removeListener!==void 0&&this.emit("removeListener",n,d||e)}return this},Fe.prototype.off=Fe.prototype.removeListener,Fe.prototype.removeAllListeners=function(n){var e,i,s;if((i=this._events)===void 0)return this;if(i.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):i[n]!==void 0&&(--this._eventsCount==0?this._events=Object.create(null):delete i[n]),this;if(arguments.length===0){var a,c=Object.keys(i);for(s=0;s<c.length;++s)(a=c[s])!=="removeListener"&&this.removeAllListeners(a);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(typeof(e=i[n])=="function")this.removeListener(n,e);else if(e!==void 0)for(s=e.length-1;s>=0;s--)this.removeListener(n,e[s]);return this},Fe.prototype.listeners=function(n){return Sh(this,n,!0)},Fe.prototype.rawListeners=function(n){return Sh(this,n,!1)},Fe.listenerCount=function(n,e){return typeof n.listenerCount=="function"?n.listenerCount(e):kh.call(n,e)},Fe.prototype.listenerCount=kh,Fe.prototype.eventNames=function(){return this._eventsCount>0?Bv(this._events):[]};var uu=cu.exports,Yo=Fw(uu),Eh=Object.prototype.hasOwnProperty;function Ch(n,e,i){for(i of n.keys())if(Et(i,e))return i}function Et(n,e){var i,s,a;if(n===e)return!0;if(n&&e&&(i=n.constructor)===e.constructor){if(i===Date)return n.getTime()===e.getTime();if(i===RegExp)return n.toString()===e.toString();if(i===Array){if((s=n.length)===e.length)for(;s--&&Et(n[s],e[s]););return s===-1}if(i===Set){if(n.size!==e.size)return!1;for(s of n)if((a=s)&&typeof a=="object"&&!(a=Ch(e,a))||!e.has(a))return!1;return!0}if(i===Map){if(n.size!==e.size)return!1;for(s of n)if((a=s[0])&&typeof a=="object"&&!(a=Ch(e,a))||!Et(s[1],e.get(a)))return!1;return!0}if(i===ArrayBuffer)n=new Uint8Array(n),e=new Uint8Array(e);else if(i===DataView){if((s=n.byteLength)===e.byteLength)for(;s--&&n.getInt8(s)===e.getInt8(s););return s===-1}if(ArrayBuffer.isView(n)){if((s=n.byteLength)===e.byteLength)for(;s--&&n[s]===e[s];);return s===-1}if(!i||typeof n=="object"){for(i in s=0,n)if(Eh.call(n,i)&&++s&&!Eh.call(e,i)||!(i in e)||!Et(n[i],e[i]))return!1;return Object.keys(e).length===s}}return n!=n&&e!=e}const $w={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},zv={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},tt={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},bt={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},dr={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"};class q{static getFirstMatch(e,i){const s=i.match(e);return s&&s.length>0&&s[1]||""}static getSecondMatch(e,i){const s=i.match(e);return s&&s.length>1&&s[2]||""}static matchAndReturnConst(e,i,s){if(e.test(i))return s}static getWindowsVersionName(e){switch(e){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}}static getMacOSVersionName(e){const i=e.split(".").splice(0,2).map(s=>parseInt(s,10)||0);if(i.push(0),i[0]===10)switch(i[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}}static getAndroidVersionName(e){const i=e.split(".").splice(0,2).map(s=>parseInt(s,10)||0);if(i.push(0),!(i[0]===1&&i[1]<5))return i[0]===1&&i[1]<6?"Cupcake":i[0]===1&&i[1]>=6?"Donut":i[0]===2&&i[1]<2?"Eclair":i[0]===2&&i[1]===2?"Froyo":i[0]===2&&i[1]>2?"Gingerbread":i[0]===3?"Honeycomb":i[0]===4&&i[1]<1?"Ice Cream Sandwich":i[0]===4&&i[1]<4?"Jelly Bean":i[0]===4&&i[1]>=4?"KitKat":i[0]===5?"Lollipop":i[0]===6?"Marshmallow":i[0]===7?"Nougat":i[0]===8?"Oreo":i[0]===9?"Pie":void 0}static getVersionPrecision(e){return e.split(".").length}static compareVersions(e,i,s=!1){const a=q.getVersionPrecision(e),c=q.getVersionPrecision(i);let d=Math.max(a,c),h=0;const m=q.map([e,i],_=>{const w=d-q.getVersionPrecision(_),E=_+new Array(w+1).join(".0");return q.map(E.split("."),x=>new Array(20-x.length).join("0")+x).reverse()});for(s&&(h=d-Math.min(a,c)),d-=1;d>=h;){if(m[0][d]>m[1][d])return 1;if(m[0][d]===m[1][d]){if(d===h)return 0;d-=1}else if(m[0][d]<m[1][d])return-1}}static map(e,i){const s=[];let a;if(Array.prototype.map)return Array.prototype.map.call(e,i);for(a=0;a<e.length;a+=1)s.push(i(e[a]));return s}static find(e,i){let s,a;if(Array.prototype.find)return Array.prototype.find.call(e,i);for(s=0,a=e.length;s<a;s+=1){const c=e[s];if(i(c,s))return c}}static assign(e,...i){const s=e;let a,c;if(Object.assign)return Object.assign(e,...i);for(a=0,c=i.length;a<c;a+=1){const d=i[a];typeof d=="object"&&d!==null&&Object.keys(d).forEach(h=>{s[h]=d[h]})}return e}static getBrowserAlias(e){return $w[e]}static getBrowserTypeByAlias(e){return zv[e]||""}}const Ue=/version\/(\d+(\.?_?\d+)+)/i,Uw=[{test:[/googlebot/i],describe(n){const e={name:"Googlebot"},i=q.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,n)||q.getFirstMatch(Ue,n);return i&&(e.version=i),e}},{test:[/opera/i],describe(n){const e={name:"Opera"},i=q.getFirstMatch(Ue,n)||q.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/opr\/|opios/i],describe(n){const e={name:"Opera"},i=q.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,n)||q.getFirstMatch(Ue,n);return i&&(e.version=i),e}},{test:[/SamsungBrowser/i],describe(n){const e={name:"Samsung Internet for Android"},i=q.getFirstMatch(Ue,n)||q.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/Whale/i],describe(n){const e={name:"NAVER Whale Browser"},i=q.getFirstMatch(Ue,n)||q.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/MZBrowser/i],describe(n){const e={name:"MZ Browser"},i=q.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,n)||q.getFirstMatch(Ue,n);return i&&(e.version=i),e}},{test:[/focus/i],describe(n){const e={name:"Focus"},i=q.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,n)||q.getFirstMatch(Ue,n);return i&&(e.version=i),e}},{test:[/swing/i],describe(n){const e={name:"Swing"},i=q.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,n)||q.getFirstMatch(Ue,n);return i&&(e.version=i),e}},{test:[/coast/i],describe(n){const e={name:"Opera Coast"},i=q.getFirstMatch(Ue,n)||q.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe(n){const e={name:"Opera Touch"},i=q.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,n)||q.getFirstMatch(Ue,n);return i&&(e.version=i),e}},{test:[/yabrowser/i],describe(n){const e={name:"Yandex Browser"},i=q.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,n)||q.getFirstMatch(Ue,n);return i&&(e.version=i),e}},{test:[/ucbrowser/i],describe(n){const e={name:"UC Browser"},i=q.getFirstMatch(Ue,n)||q.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/Maxthon|mxios/i],describe(n){const e={name:"Maxthon"},i=q.getFirstMatch(Ue,n)||q.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/epiphany/i],describe(n){const e={name:"Epiphany"},i=q.getFirstMatch(Ue,n)||q.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/puffin/i],describe(n){const e={name:"Puffin"},i=q.getFirstMatch(Ue,n)||q.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/sleipnir/i],describe(n){const e={name:"Sleipnir"},i=q.getFirstMatch(Ue,n)||q.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/k-meleon/i],describe(n){const e={name:"K-Meleon"},i=q.getFirstMatch(Ue,n)||q.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/micromessenger/i],describe(n){const e={name:"WeChat"},i=q.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,n)||q.getFirstMatch(Ue,n);return i&&(e.version=i),e}},{test:[/qqbrowser/i],describe(n){const e={name:/qqbrowserlite/i.test(n)?"QQ Browser Lite":"QQ Browser"},i=q.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,n)||q.getFirstMatch(Ue,n);return i&&(e.version=i),e}},{test:[/msie|trident/i],describe(n){const e={name:"Internet Explorer"},i=q.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/\sedg\//i],describe(n){const e={name:"Microsoft Edge"},i=q.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/edg([ea]|ios)/i],describe(n){const e={name:"Microsoft Edge"},i=q.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/vivaldi/i],describe(n){const e={name:"Vivaldi"},i=q.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/seamonkey/i],describe(n){const e={name:"SeaMonkey"},i=q.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/sailfish/i],describe(n){const e={name:"Sailfish"},i=q.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,n);return i&&(e.version=i),e}},{test:[/silk/i],describe(n){const e={name:"Amazon Silk"},i=q.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/phantom/i],describe(n){const e={name:"PhantomJS"},i=q.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/slimerjs/i],describe(n){const e={name:"SlimerJS"},i=q.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(n){const e={name:"BlackBerry"},i=q.getFirstMatch(Ue,n)||q.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/(web|hpw)[o0]s/i],describe(n){const e={name:"WebOS Browser"},i=q.getFirstMatch(Ue,n)||q.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/bada/i],describe(n){const e={name:"Bada"},i=q.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/tizen/i],describe(n){const e={name:"Tizen"},i=q.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,n)||q.getFirstMatch(Ue,n);return i&&(e.version=i),e}},{test:[/qupzilla/i],describe(n){const e={name:"QupZilla"},i=q.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,n)||q.getFirstMatch(Ue,n);return i&&(e.version=i),e}},{test:[/firefox|iceweasel|fxios/i],describe(n){const e={name:"Firefox"},i=q.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/electron/i],describe(n){const e={name:"Electron"},i=q.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/MiuiBrowser/i],describe(n){const e={name:"Miui"},i=q.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/chromium/i],describe(n){const e={name:"Chromium"},i=q.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,n)||q.getFirstMatch(Ue,n);return i&&(e.version=i),e}},{test:[/chrome|crios|crmo/i],describe(n){const e={name:"Chrome"},i=q.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/GSA/i],describe(n){const e={name:"Google Search"},i=q.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test(n){const e=!n.test(/like android/i),i=n.test(/android/i);return e&&i},describe(n){const e={name:"Android Browser"},i=q.getFirstMatch(Ue,n);return i&&(e.version=i),e}},{test:[/playstation 4/i],describe(n){const e={name:"PlayStation 4"},i=q.getFirstMatch(Ue,n);return i&&(e.version=i),e}},{test:[/safari|applewebkit/i],describe(n){const e={name:"Safari"},i=q.getFirstMatch(Ue,n);return i&&(e.version=i),e}},{test:[/.*/i],describe(n){const e=n.search("\\(")!==-1?/^(.*)\/(.*)[ \t]\((.*)/:/^(.*)\/(.*) /;return{name:q.getFirstMatch(e,n),version:q.getSecondMatch(e,n)}}}];var zw=[{test:[/Roku\/DVP/],describe(n){const e=q.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,n);return{name:bt.Roku,version:e}}},{test:[/windows phone/i],describe(n){const e=q.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,n);return{name:bt.WindowsPhone,version:e}}},{test:[/windows /i],describe(n){const e=q.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,n),i=q.getWindowsVersionName(e);return{name:bt.Windows,version:e,versionName:i}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(n){const e={name:bt.iOS},i=q.getSecondMatch(/(Version\/)(\d[\d.]+)/,n);return i&&(e.version=i),e}},{test:[/macintosh/i],describe(n){const e=q.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,n).replace(/[_\s]/g,"."),i=q.getMacOSVersionName(e),s={name:bt.MacOS,version:e};return i&&(s.versionName=i),s}},{test:[/(ipod|iphone|ipad)/i],describe(n){const e=q.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,n).replace(/[_\s]/g,".");return{name:bt.iOS,version:e}}},{test(n){const e=!n.test(/like android/i),i=n.test(/android/i);return e&&i},describe(n){const e=q.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,n),i=q.getAndroidVersionName(e),s={name:bt.Android,version:e};return i&&(s.versionName=i),s}},{test:[/(web|hpw)[o0]s/i],describe(n){const e=q.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,n),i={name:bt.WebOS};return e&&e.length&&(i.version=e),i}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(n){const e=q.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,n)||q.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,n)||q.getFirstMatch(/\bbb(\d+)/i,n);return{name:bt.BlackBerry,version:e}}},{test:[/bada/i],describe(n){const e=q.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,n);return{name:bt.Bada,version:e}}},{test:[/tizen/i],describe(n){const e=q.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,n);return{name:bt.Tizen,version:e}}},{test:[/linux/i],describe:()=>({name:bt.Linux})},{test:[/CrOS/],describe:()=>({name:bt.ChromeOS})},{test:[/PlayStation 4/],describe(n){const e=q.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,n);return{name:bt.PlayStation4,version:e}}}],Vw=[{test:[/googlebot/i],describe:()=>({type:"bot",vendor:"Google"})},{test:[/huawei/i],describe(n){const e=q.getFirstMatch(/(can-l01)/i,n)&&"Nova",i={type:tt.mobile,vendor:"Huawei"};return e&&(i.model=e),i}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe:()=>({type:tt.tablet,vendor:"Nexus"})},{test:[/ipad/i],describe:()=>({type:tt.tablet,vendor:"Apple",model:"iPad"})},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:()=>({type:tt.tablet,vendor:"Apple",model:"iPad"})},{test:[/kftt build/i],describe:()=>({type:tt.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"})},{test:[/silk/i],describe:()=>({type:tt.tablet,vendor:"Amazon"})},{test:[/tablet(?! pc)/i],describe:()=>({type:tt.tablet})},{test(n){const e=n.test(/ipod|iphone/i),i=n.test(/like (ipod|iphone)/i);return e&&!i},describe(n){const e=q.getFirstMatch(/(ipod|iphone)/i,n);return{type:tt.mobile,vendor:"Apple",model:e}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe:()=>({type:tt.mobile,vendor:"Nexus"})},{test:[/[^-]mobi/i],describe:()=>({type:tt.mobile})},{test:n=>n.getBrowserName(!0)==="blackberry",describe:()=>({type:tt.mobile,vendor:"BlackBerry"})},{test:n=>n.getBrowserName(!0)==="bada",describe:()=>({type:tt.mobile})},{test:n=>n.getBrowserName()==="windows phone",describe:()=>({type:tt.mobile,vendor:"Microsoft"})},{test(n){const e=Number(String(n.getOSVersion()).split(".")[0]);return n.getOSName(!0)==="android"&&e>=3},describe:()=>({type:tt.tablet})},{test:n=>n.getOSName(!0)==="android",describe:()=>({type:tt.mobile})},{test:n=>n.getOSName(!0)==="macos",describe:()=>({type:tt.desktop,vendor:"Apple"})},{test:n=>n.getOSName(!0)==="windows",describe:()=>({type:tt.desktop})},{test:n=>n.getOSName(!0)==="linux",describe:()=>({type:tt.desktop})},{test:n=>n.getOSName(!0)==="playstation 4",describe:()=>({type:tt.tv})},{test:n=>n.getOSName(!0)==="roku",describe:()=>({type:tt.tv})}],Ww=[{test:n=>n.getBrowserName(!0)==="microsoft edge",describe(n){if(/\sedg\//i.test(n))return{name:dr.Blink};const e=q.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,n);return{name:dr.EdgeHTML,version:e}}},{test:[/trident/i],describe(n){const e={name:dr.Trident},i=q.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:n=>n.test(/presto/i),describe(n){const e={name:dr.Presto},i=q.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test(n){const e=n.test(/gecko/i),i=n.test(/like gecko/i);return e&&!i},describe(n){const e={name:dr.Gecko},i=q.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}},{test:[/(apple)?webkit\/537\.36/i],describe:()=>({name:dr.Blink})},{test:[/(apple)?webkit/i],describe(n){const e={name:dr.WebKit},i=q.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,n);return i&&(e.version=i),e}}];class Mh{constructor(e,i=!1){if(e==null||e==="")throw new Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},i!==!0&&this.parse()}getUA(){return this._ua}test(e){return e.test(this._ua)}parseBrowser(){this.parsedResult.browser={};const e=q.find(Uw,i=>{if(typeof i.test=="function")return i.test(this);if(i.test instanceof Array)return i.test.some(s=>this.test(s));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.browser=e.describe(this.getUA())),this.parsedResult.browser}getBrowser(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()}getBrowserName(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""}getBrowserVersion(){return this.getBrowser().version}getOS(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()}parseOS(){this.parsedResult.os={};const e=q.find(zw,i=>{if(typeof i.test=="function")return i.test(this);if(i.test instanceof Array)return i.test.some(s=>this.test(s));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.os=e.describe(this.getUA())),this.parsedResult.os}getOSName(e){const{name:i}=this.getOS();return e?String(i).toLowerCase()||"":i||""}getOSVersion(){return this.getOS().version}getPlatform(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()}getPlatformType(e=!1){const{type:i}=this.getPlatform();return e?String(i).toLowerCase()||"":i||""}parsePlatform(){this.parsedResult.platform={};const e=q.find(Vw,i=>{if(typeof i.test=="function")return i.test(this);if(i.test instanceof Array)return i.test.some(s=>this.test(s));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.platform=e.describe(this.getUA())),this.parsedResult.platform}getEngine(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()}getEngineName(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""}parseEngine(){this.parsedResult.engine={};const e=q.find(Ww,i=>{if(typeof i.test=="function")return i.test(this);if(i.test instanceof Array)return i.test.some(s=>this.test(s));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.engine=e.describe(this.getUA())),this.parsedResult.engine}parse(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this}getResult(){return q.assign({},this.parsedResult)}satisfies(e){const i={};let s=0;const a={};let c=0;if(Object.keys(e).forEach(d=>{const h=e[d];typeof h=="string"?(a[d]=h,c+=1):typeof h=="object"&&(i[d]=h,s+=1)}),s>0){const d=Object.keys(i),h=q.find(d,_=>this.isOS(_));if(h){const _=this.satisfies(i[h]);if(_!==void 0)return _}const m=q.find(d,_=>this.isPlatform(_));if(m){const _=this.satisfies(i[m]);if(_!==void 0)return _}}if(c>0){const d=Object.keys(a),h=q.find(d,m=>this.isBrowser(m,!0));if(h!==void 0)return this.compareVersion(a[h])}}isBrowser(e,i=!1){const s=this.getBrowserName().toLowerCase();let a=e.toLowerCase();const c=q.getBrowserTypeByAlias(a);return i&&c&&(a=c.toLowerCase()),a===s}compareVersion(e){let i=[0],s=e,a=!1;const c=this.getBrowserVersion();if(typeof c=="string")return e[0]===">"||e[0]==="<"?(s=e.substr(1),e[1]==="="?(a=!0,s=e.substr(2)):i=[],e[0]===">"?i.push(1):i.push(-1)):e[0]==="="?s=e.substr(1):e[0]==="~"&&(a=!0,s=e.substr(1)),i.indexOf(q.compareVersions(c,s,a))>-1}isOS(e){return this.getOSName(!0)===String(e).toLowerCase()}isPlatform(e){return this.getPlatformType(!0)===String(e).toLowerCase()}isEngine(e){return this.getEngineName(!0)===String(e).toLowerCase()}is(e,i=!1){return this.isBrowser(e,i)||this.isOS(e)||this.isPlatform(e)}some(e=[]){return e.some(i=>this.is(i))}}/*!
 * Bowser - a browser detector
 * https://github.com/lancedikson/bowser
 * MIT License | (c) Dustin Diaz 2012-2015
 * MIT License | (c) Denis Demchenko 2015-2019
 */class qw{static getParser(e,i=!1){if(typeof e!="string")throw new Error("UserAgent should be a string");return new Mh(e,i)}static parse(e){return new Mh(e).getResult()}static get BROWSER_MAP(){return zv}static get ENGINE_MAP(){return dr}static get OS_MAP(){return bt}static get PLATFORMS_MAP(){return tt}}function Ra(){return Date.now()+Math.random().toString()}function ks(){throw new Error("Method must be implemented in subclass")}function Vv(n,e){return e!=null&&e.proxyUrl?e.proxyUrl+(e.proxyUrl.slice(-1)==="/"?"":"/")+n.substring(8):n}function ya(n){return n!=null&&n.callObjectBundleUrlOverride?n.callObjectBundleUrlOverride:Vv("https://c.daily.co/call-machine/versioned/".concat("0.77.0","/static/call-machine-object-bundle.js"),n)}function _a(n){try{new URL(n)}catch{return!1}return!0}const We=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Br="8.55.0",De=globalThis;function wa(n,e,i){const s=De,a=s.__SENTRY__=s.__SENTRY__||{},c=a[Br]=a[Br]||{};return c[n]||(c[n]=e())}const Hr=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,du=["debug","info","warn","error","log","assert","trace"],Sa={};function Ls(n){if(!("console"in De))return n();const e=De.console,i={},s=Object.keys(Sa);s.forEach(a=>{const c=Sa[a];i[a]=e[a],e[a]=c});try{return n()}finally{s.forEach(a=>{e[a]=i[a]})}}const be=wa("logger",function(){let n=!1;const e={enable:()=>{n=!0},disable:()=>{n=!1},isEnabled:()=>n};return Hr?du.forEach(i=>{e[i]=(...s)=>{n&&Ls(()=>{De.console[i](`Sentry Logger [${i}]:`,...s)})}}):du.forEach(i=>{e[i]=()=>{}}),e}),Fr="?",Th=/\(error: (.*)\)/,Ph=/captureMessage|captureException/;function Xo(n){return n[n.length-1]||{}}const Oh="<anonymous>";function vr(n){try{return n&&typeof n=="function"&&n.name||Oh}catch{return Oh}}function xh(n){const e=n.exception;if(e){const i=[];try{return e.values.forEach(s=>{s.stacktrace.frames&&i.push(...s.stacktrace.frames)}),i}catch{return}}}const fa={},Ah={};function $r(n,e){fa[n]=fa[n]||[],fa[n].push(e)}function Ur(n,e){if(!Ah[n]){Ah[n]=!0;try{e()}catch(i){Hr&&be.error(`Error while instrumenting ${n}`,i)}}}function vn(n,e){const i=n&&fa[n];if(i)for(const s of i)try{s(e)}catch(a){Hr&&be.error(`Error while triggering instrumentation handler.
Type: ${n}
Name: ${vr(s)}
Error:`,a)}}let $c=null;function Jw(){$c=De.onerror,De.onerror=function(n,e,i,s,a){return vn("error",{column:s,error:a,line:i,msg:n,url:e}),!!$c&&$c.apply(this,arguments)},De.onerror.__SENTRY_INSTRUMENTED__=!0}let Uc=null;function Hw(){Uc=De.onunhandledrejection,De.onunhandledrejection=function(n){return vn("unhandledrejection",n),!Uc||Uc.apply(this,arguments)},De.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}function Da(){return Wu(De),De}function Wu(n){const e=n.__SENTRY__=n.__SENTRY__||{};return e.version=e.version||Br,e[Br]=e[Br]||{}}const Wv=Object.prototype.toString;function qu(n){switch(Wv.call(n)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return zr(n,Error)}}function Ni(n,e){return Wv.call(n)===`[object ${e}]`}function qv(n){return Ni(n,"ErrorEvent")}function Lh(n){return Ni(n,"DOMError")}function Fn(n){return Ni(n,"String")}function Ju(n){return typeof n=="object"&&n!==null&&"__sentry_template_string__"in n&&"__sentry_template_values__"in n}function fu(n){return n===null||Ju(n)||typeof n!="object"&&typeof n!="function"}function Mi(n){return Ni(n,"Object")}function Ia(n){return typeof Event<"u"&&zr(n,Event)}function Fa(n){return!!(n&&n.then&&typeof n.then=="function")}function zr(n,e){try{return n instanceof e}catch{return!1}}function Jv(n){return!(typeof n!="object"||n===null||!n.__isVue&&!n._isVue)}const Hu=De;function Hv(n,e={}){if(!n)return"<unknown>";try{let i=n;const s=5,a=[];let c=0,d=0;const h=" > ",m=h.length;let _;const w=Array.isArray(e)?e:e.keyAttrs,E=!Array.isArray(e)&&e.maxStringLength||80;for(;i&&c++<s&&(_=Qw(i,w),!(_==="html"||c>1&&d+a.length*m+_.length>=E));)a.push(_),d+=_.length,i=i.parentNode;return a.reverse().join(h)}catch{return"<unknown>"}}function Qw(n,e){const i=n,s=[];if(!i||!i.tagName)return"";if(Hu.HTMLElement&&i instanceof HTMLElement&&i.dataset){if(i.dataset.sentryComponent)return i.dataset.sentryComponent;if(i.dataset.sentryElement)return i.dataset.sentryElement}s.push(i.tagName.toLowerCase());const a=e&&e.length?e.filter(d=>i.getAttribute(d)).map(d=>[d,i.getAttribute(d)]):null;if(a&&a.length)a.forEach(d=>{s.push(`[${d[0]}="${d[1]}"]`)});else{i.id&&s.push(`#${i.id}`);const d=i.className;if(d&&Fn(d)){const h=d.split(/\s+/);for(const m of h)s.push(`.${m}`)}}const c=["aria-label","type","name","title","alt"];for(const d of c){const h=i.getAttribute(d);h&&s.push(`[${d}="${h}"]`)}return s.join("")}function bi(n,e=0){return typeof n!="string"||e===0||n.length<=e?n:`${n.slice(0,e)}...`}function Nh(n,e){if(!Array.isArray(n))return"";const i=[];for(let s=0;s<n.length;s++){const a=n[s];try{Jv(a)?i.push("[VueViewModel]"):i.push(String(a))}catch{i.push("[value cannot be serialized]")}}return i.join(e)}function Gw(n,e,i=!1){return!!Fn(n)&&(Ni(e,"RegExp")?e.test(n):!!Fn(e)&&(i?n===e:n.includes(e)))}function Zo(n,e=[],i=!1){return e.some(s=>Gw(n,s,i))}function $t(n,e,i){if(!(e in n))return;const s=n[e],a=i(s);typeof a=="function"&&Qv(a,s);try{n[e]=a}catch{Hr&&be.log(`Failed to replace method "${e}" in object`,n)}}function Vr(n,e,i){try{Object.defineProperty(n,e,{value:i,writable:!0,configurable:!0})}catch{Hr&&be.log(`Failed to add non-enumerable property "${e}" to object`,n)}}function Qv(n,e){try{const i=e.prototype||{};n.prototype=e.prototype=i,Vr(n,"__sentry_original__",e)}catch{}}function Qu(n){return n.__sentry_original__}function Gv(n){if(qu(n))return{message:n.message,name:n.name,stack:n.stack,...Rh(n)};if(Ia(n)){const e={type:n.type,target:jh(n.target),currentTarget:jh(n.currentTarget),...Rh(n)};return typeof CustomEvent<"u"&&zr(n,CustomEvent)&&(e.detail=n.detail),e}return n}function jh(n){try{return e=n,typeof Element<"u"&&zr(e,Element)?Hv(n):Object.prototype.toString.call(n)}catch{return"<unknown>"}var e}function Rh(n){if(typeof n=="object"&&n!==null){const e={};for(const i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i]);return e}return{}}function Yt(n){return pu(n,new Map)}function pu(n,e){if(function(i){if(!Mi(i))return!1;try{const s=Object.getPrototypeOf(i).constructor.name;return!s||s==="Object"}catch{return!0}}(n)){const i=e.get(n);if(i!==void 0)return i;const s={};e.set(n,s);for(const a of Object.getOwnPropertyNames(n))n[a]!==void 0&&(s[a]=pu(n[a],e));return s}if(Array.isArray(n)){const i=e.get(n);if(i!==void 0)return i;const s=[];return e.set(n,s),n.forEach(a=>{s.push(pu(a,e))}),s}return n}function Ns(){return Date.now()/1e3}const $n=function(){const{performance:n}=De;if(!n||!n.now)return Ns;const e=Date.now()-n.now(),i=n.timeOrigin==null?e:n.timeOrigin;return()=>(i+n.now())/1e3}();function Zt(){const n=De,e=n.crypto||n.msCrypto;let i=()=>16*Math.random();try{if(e&&e.randomUUID)return e.randomUUID().replace(/-/g,"");e&&e.getRandomValues&&(i=()=>{const s=new Uint8Array(1);return e.getRandomValues(s),s[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,s=>(s^(15&i())>>s/4).toString(16))}function Kv(n){return n.exception&&n.exception.values?n.exception.values[0]:void 0}function fr(n){const{message:e,event_id:i}=n;if(e)return e;const s=Kv(n);return s?s.type&&s.value?`${s.type}: ${s.value}`:s.type||s.value||i||"<unknown>":i||"<unknown>"}function hu(n,e,i){const s=n.exception=n.exception||{},a=s.values=s.values||[],c=a[0]=a[0]||{};c.value||(c.value=e||""),c.type||(c.type="Error")}function Ti(n,e){const i=Kv(n);if(!i)return;const s=i.mechanism;if(i.mechanism={type:"generic",handled:!0,...s,...e},e&&"data"in e){const a={...s&&s.data,...e.data};i.mechanism.data=a}}function Dh(n){if(function(e){try{return e.__sentry_captured__}catch{}}(n))return!0;try{Vr(n,"__sentry_captured__",!0)}catch{}return!1}var Dn;function Wr(n){return new Kt(e=>{e(n)})}function ka(n){return new Kt((e,i)=>{i(n)})}(()=>{const{performance:n}=De;!n||!n.now||(n.now(),n.timing&&n.timing.navigationStart)})(),function(n){n[n.PENDING=0]="PENDING",n[n.RESOLVED=1]="RESOLVED",n[n.REJECTED=2]="REJECTED"}(Dn||(Dn={}));class Kt{constructor(e){Kt.prototype.__init.call(this),Kt.prototype.__init2.call(this),Kt.prototype.__init3.call(this),Kt.prototype.__init4.call(this),this._state=Dn.PENDING,this._handlers=[];try{e(this._resolve,this._reject)}catch(i){this._reject(i)}}then(e,i){return new Kt((s,a)=>{this._handlers.push([!1,c=>{if(e)try{s(e(c))}catch(d){a(d)}else s(c)},c=>{if(i)try{s(i(c))}catch(d){a(d)}else a(c)}]),this._executeHandlers()})}catch(e){return this.then(i=>i,e)}finally(e){return new Kt((i,s)=>{let a,c;return this.then(d=>{c=!1,a=d,e&&e()},d=>{c=!0,a=d,e&&e()}).then(()=>{c?s(a):i(a)})})}__init(){this._resolve=e=>{this._setResult(Dn.RESOLVED,e)}}__init2(){this._reject=e=>{this._setResult(Dn.REJECTED,e)}}__init3(){this._setResult=(e,i)=>{this._state===Dn.PENDING&&(Fa(i)?i.then(this._resolve,this._reject):(this._state=e,this._value=i,this._executeHandlers()))}}__init4(){this._executeHandlers=()=>{if(this._state===Dn.PENDING)return;const e=this._handlers.slice();this._handlers=[],e.forEach(i=>{i[0]||(this._state===Dn.RESOLVED&&i[1](this._value),this._state===Dn.REJECTED&&i[2](this._value),i[0]=!0)})}}}function Kw(n){const e=$n(),i={sid:Zt(),init:!0,timestamp:e,started:e,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(s){return Yt({sid:`${s.sid}`,init:s.init,started:new Date(1e3*s.started).toISOString(),timestamp:new Date(1e3*s.timestamp).toISOString(),status:s.status,errors:s.errors,did:typeof s.did=="number"||typeof s.did=="string"?`${s.did}`:void 0,duration:s.duration,abnormal_mechanism:s.abnormal_mechanism,attrs:{release:s.release,environment:s.environment,ip_address:s.ipAddress,user_agent:s.userAgent}})}(i)};return n&&Pi(i,n),i}function Pi(n,e={}){if(e.user&&(!n.ipAddress&&e.user.ip_address&&(n.ipAddress=e.user.ip_address),n.did||e.did||(n.did=e.user.id||e.user.email||e.user.username)),n.timestamp=e.timestamp||$n(),e.abnormal_mechanism&&(n.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(n.ignoreDuration=e.ignoreDuration),e.sid&&(n.sid=e.sid.length===32?e.sid:Zt()),e.init!==void 0&&(n.init=e.init),!n.did&&e.did&&(n.did=`${e.did}`),typeof e.started=="number"&&(n.started=e.started),n.ignoreDuration)n.duration=void 0;else if(typeof e.duration=="number")n.duration=e.duration;else{const i=n.timestamp-n.started;n.duration=i>=0?i:0}e.release&&(n.release=e.release),e.environment&&(n.environment=e.environment),!n.ipAddress&&e.ipAddress&&(n.ipAddress=e.ipAddress),!n.userAgent&&e.userAgent&&(n.userAgent=e.userAgent),typeof e.errors=="number"&&(n.errors=e.errors),e.status&&(n.status=e.status)}function Ih(){return Zt()}function mu(){return Zt().substring(16)}function Ba(n,e,i=2){if(!e||typeof e!="object"||i<=0)return e;if(n&&e&&Object.keys(e).length===0)return n;const s={...n};for(const a in e)Object.prototype.hasOwnProperty.call(e,a)&&(s[a]=Ba(s[a],e[a],i-1));return s}const vu="_sentrySpan";function Fh(n,e){e?Vr(n,vu,e):delete n[vu]}function Bh(n){return n[vu]}class Gu{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:Ih(),spanId:mu()}}clone(){const e=new Gu;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._requestSession=this._requestSession,e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,Fh(e,Bh(this)),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&Pi(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(e){return this._requestSession=e,this}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,i){return this._tags={...this._tags,[e]:i},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,i){return this._extra={...this._extra,[e]:i},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,i){return i===null?delete this._contexts[e]:this._contexts[e]=i,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;const i=typeof e=="function"?e(this):e,[s,a]=i instanceof gr?[i.getScopeData(),i.getRequestSession()]:Mi(i)?[e,e.requestSession]:[],{tags:c,extra:d,user:h,contexts:m,level:_,fingerprint:w=[],propagationContext:E}=s||{};return this._tags={...this._tags,...c},this._extra={...this._extra,...d},this._contexts={...this._contexts,...m},h&&Object.keys(h).length&&(this._user=h),_&&(this._level=_),w.length&&(this._fingerprint=w),E&&(this._propagationContext=E),a&&(this._requestSession=a),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._session=void 0,Fh(this,void 0),this._attachments=[],this.setPropagationContext({traceId:Ih()}),this._notifyScopeListeners(),this}addBreadcrumb(e,i){const s=typeof i=="number"?i:100;if(s<=0)return this;const a={timestamp:Ns(),...e};return this._breadcrumbs.push(a),this._breadcrumbs.length>s&&(this._breadcrumbs=this._breadcrumbs.slice(-s),this._client&&this._client.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:Bh(this)}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=Ba(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext={spanId:mu(),...e},this}getPropagationContext(){return this._propagationContext}captureException(e,i){const s=i&&i.event_id?i.event_id:Zt();if(!this._client)return be.warn("No client configured on scope - will not capture exception!"),s;const a=new Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:a,...i,event_id:s},this),s}captureMessage(e,i,s){const a=s&&s.event_id?s.event_id:Zt();if(!this._client)return be.warn("No client configured on scope - will not capture message!"),a;const c=new Error(e);return this._client.captureMessage(e,i,{originalException:e,syntheticException:c,...s,event_id:a},this),a}captureEvent(e,i){const s=i&&i.event_id?i.event_id:Zt();return this._client?(this._client.captureEvent(e,{...i,event_id:s},this),s):(be.warn("No client configured on scope - will not capture event!"),s)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}const gr=Gu;class Yw{constructor(e,i){let s,a;s=e||new gr,a=i||new gr,this._stack=[{scope:s}],this._isolationScope=a}withScope(e){const i=this._pushScope();let s;try{s=e(i)}catch(a){throw this._popScope(),a}return Fa(s)?s.then(a=>(this._popScope(),a),a=>{throw this._popScope(),a}):(this._popScope(),s)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function Oi(){const n=Wu(Da());return n.stack=n.stack||new Yw(wa("defaultCurrentScope",()=>new gr),wa("defaultIsolationScope",()=>new gr))}function Xw(n){return Oi().withScope(n)}function Zw(n,e){const i=Oi();return i.withScope(()=>(i.getStackTop().scope=n,e(n)))}function $h(n){return Oi().withScope(()=>n(Oi().getIsolationScope()))}function Ku(n){const e=Wu(n);return e.acs?e.acs:{withIsolationScope:$h,withScope:Xw,withSetScope:Zw,withSetIsolationScope:(i,s)=>$h(s),getCurrentScope:()=>Oi().getScope(),getIsolationScope:()=>Oi().getIsolationScope()}}function Qr(){return Ku(Da()).getCurrentScope()}function js(){return Ku(Da()).getIsolationScope()}function yt(){return Qr().getClient()}function e0(n){const e=n.getPropagationContext(),{traceId:i,spanId:s,parentSpanId:a}=e;return Yt({trace_id:i,span_id:s,parent_span_id:a})}function t0(n){const e=n._sentryMetrics;if(!e)return;const i={};for(const[,[s,a]]of e)(i[s]||(i[s]=[])).push(Yt(a));return i}const n0=/^sentry-/;function r0(n){const e=function(s){if(!(!s||!Fn(s)&&!Array.isArray(s)))return Array.isArray(s)?s.reduce((a,c)=>{const d=Uh(c);return Object.entries(d).forEach(([h,m])=>{a[h]=m}),a},{}):Uh(s)}(n);if(!e)return;const i=Object.entries(e).reduce((s,[a,c])=>(a.match(n0)&&(s[a.slice(7)]=c),s),{});return Object.keys(i).length>0?i:void 0}function Uh(n){return n.split(",").map(e=>e.split("=").map(i=>decodeURIComponent(i.trim()))).reduce((e,[i,s])=>(i&&s&&(e[i]=s),e),{})}let zh=!1;function i0(n){const{spanId:e,traceId:i,isRemote:s}=n.spanContext();return Yt({parent_span_id:s?e:Yu(n).parent_span_id,span_id:s?mu():e,trace_id:i})}function Vh(n){return typeof n=="number"?Wh(n):Array.isArray(n)?n[0]+n[1]/1e9:n instanceof Date?Wh(n.getTime()):$n()}function Wh(n){return n>9999999999?n/1e3:n}function Yu(n){if(function(e){return typeof e.getSpanJSON=="function"}(n))return n.getSpanJSON();try{const{spanId:e,traceId:i}=n.spanContext();if(function(s){const a=s;return!!(a.attributes&&a.startTime&&a.name&&a.endTime&&a.status)}(n)){const{attributes:s,startTime:a,name:c,endTime:d,parentSpanId:h,status:m}=n;return Yt({span_id:e,trace_id:i,data:s,description:c,parent_span_id:h,start_timestamp:Vh(a),timestamp:Vh(d)||void 0,status:s0(m),op:s["sentry.op"],origin:s["sentry.origin"],_metrics_summary:t0(n)})}return{span_id:e,trace_id:i}}catch{return{}}}function s0(n){if(n&&n.code!==0)return n.code===1?"ok":n.message||"unknown_error"}function Yv(n){return n._sentryRootSpan||n}function o0(){zh||(Ls(()=>{console.warn("[Sentry] Deprecation warning: Returning null from `beforeSendSpan` will be disallowed from SDK version 9.0.0 onwards. The callback will only support mutating spans. To drop certain spans, configure the respective integrations directly.")}),zh=!0)}const Xu="production";function Xv(n,e){const i=e.getOptions(),{publicKey:s}=e.getDsn()||{},a=Yt({environment:i.environment||Xu,release:i.release,public_key:s,trace_id:n});return e.emit("createDsc",a),a}function a0(n){const e=yt();if(!e)return{};const i=Yv(n),s=i._frozenDsc;if(s)return s;const a=i.spanContext().traceState,c=a&&a.get("sentry.dsc"),d=c&&r0(c);if(d)return d;const h=Xv(n.spanContext().traceId,e),m=Yu(i),_=m.data||{},w=_["sentry.sample_rate"];w!=null&&(h.sample_rate=`${w}`);const E=_["sentry.source"],x=m.description;return E!=="url"&&x&&(h.transaction=x),function(T){if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;const P=yt(),L=P&&P.getOptions();return!!L&&(L.enableTracing||"tracesSampleRate"in L||"tracesSampler"in L)}()&&(h.sampled=String(function(T){const{traceFlags:P}=T.spanContext();return P===1}(i))),e.emit("createDsc",h,i),h}const l0=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function ba(n,e=!1){const{host:i,path:s,pass:a,port:c,projectId:d,protocol:h,publicKey:m}=n;return`${h}://${m}${e&&a?`:${a}`:""}@${i}${c?`:${c}`:""}/${s&&`${s}/`}${d}`}function qh(n){return{protocol:n.protocol,publicKey:n.publicKey||"",pass:n.pass||"",host:n.host,port:n.port||"",path:n.path||"",projectId:n.projectId}}function c0(n){const e=typeof n=="string"?function(i){const s=l0.exec(i);if(!s)return void Ls(()=>{console.error(`Invalid Sentry Dsn: ${i}`)});const[a,c,d="",h="",m="",_=""]=s.slice(1);let w="",E=_;const x=E.split("/");if(x.length>1&&(w=x.slice(0,-1).join("/"),E=x.pop()),E){const T=E.match(/^\d+/);T&&(E=T[0])}return qh({host:h,pass:d,path:w,projectId:E,port:m,protocol:a,publicKey:c})}(n):qh(n);if(e&&function(i){if(!Hr)return!0;const{port:s,projectId:a,protocol:c}=i;return!(["protocol","publicKey","host","projectId"].find(d=>!i[d]&&(be.error(`Invalid Sentry Dsn: ${d} missing`),!0))||(a.match(/^\d+$/)?function(d){return d==="http"||d==="https"}(c)?s&&isNaN(parseInt(s,10))&&(be.error(`Invalid Sentry Dsn: Invalid port ${s}`),1):(be.error(`Invalid Sentry Dsn: Invalid protocol ${c}`),1):(be.error(`Invalid Sentry Dsn: Invalid projectId ${a}`),1)))}(e))return e}function In(n,e=100,i=1/0){try{return gu("",n,e,i)}catch(s){return{ERROR:`**non-serializable** (${s})`}}}function Zv(n,e=3,i=102400){const s=In(n,e);return a=s,function(c){return~-encodeURI(c).split(/%..|./).length}(JSON.stringify(a))>i?Zv(n,e-1,i):s;var a}function gu(n,e,i=1/0,s=1/0,a=function(){const c=typeof WeakSet=="function",d=c?new WeakSet:[];return[function(h){if(c)return!!d.has(h)||(d.add(h),!1);for(let m=0;m<d.length;m++)if(d[m]===h)return!0;return d.push(h),!1},function(h){if(c)d.delete(h);else for(let m=0;m<d.length;m++)if(d[m]===h){d.splice(m,1);break}}]}()){const[c,d]=a;if(e==null||["boolean","string"].includes(typeof e)||typeof e=="number"&&Number.isFinite(e))return e;const h=function(T,P){try{if(T==="domain"&&P&&typeof P=="object"&&P._events)return"[Domain]";if(T==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&P===global)return"[Global]";if(typeof window<"u"&&P===window)return"[Window]";if(typeof document<"u"&&P===document)return"[Document]";if(Jv(P))return"[VueViewModel]";if(Mi(L=P)&&"nativeEvent"in L&&"preventDefault"in L&&"stopPropagation"in L)return"[SyntheticEvent]";if(typeof P=="number"&&!Number.isFinite(P))return`[${P}]`;if(typeof P=="function")return`[Function: ${vr(P)}]`;if(typeof P=="symbol")return`[${String(P)}]`;if(typeof P=="bigint")return`[BigInt: ${String(P)}]`;const F=function(Y){const ne=Object.getPrototypeOf(Y);return ne?ne.constructor.name:"null prototype"}(P);return/^HTML(\w*)Element$/.test(F)?`[HTMLElement: ${F}]`:`[object ${F}]`}catch(F){return`**non-serializable** (${F})`}var L}(n,e);if(!h.startsWith("[object "))return h;if(e.__sentry_skip_normalization__)return e;const m=typeof e.__sentry_override_normalization_depth__=="number"?e.__sentry_override_normalization_depth__:i;if(m===0)return h.replace("object ","");if(c(e))return"[Circular ~]";const _=e;if(_&&typeof _.toJSON=="function")try{return gu("",_.toJSON(),m-1,s,a)}catch{}const w=Array.isArray(e)?[]:{};let E=0;const x=Gv(e);for(const T in x){if(!Object.prototype.hasOwnProperty.call(x,T))continue;if(E>=s){w[T]="[MaxProperties ~]";break}const P=x[T];w[T]=gu(T,P,m-1,s,a),E++}return d(e),w}function As(n,e=[]){return[n,e]}function u0(n,e){const[i,s]=n;return[i,[...s,e]]}function Jh(n,e){const i=n[1];for(const s of i)if(e(s,s[0].type))return!0;return!1}function yu(n){return De.__SENTRY__&&De.__SENTRY__.encodePolyfill?De.__SENTRY__.encodePolyfill(n):new TextEncoder().encode(n)}function d0(n){const[e,i]=n;let s=JSON.stringify(e);function a(c){typeof s=="string"?s=typeof c=="string"?s+c:[yu(s),c]:s.push(typeof c=="string"?yu(c):c)}for(const c of i){const[d,h]=c;if(a(`
${JSON.stringify(d)}
`),typeof h=="string"||h instanceof Uint8Array)a(h);else{let m;try{m=JSON.stringify(h)}catch{m=JSON.stringify(In(h))}a(m)}}return typeof s=="string"?s:function(c){const d=c.reduce((_,w)=>_+w.length,0),h=new Uint8Array(d);let m=0;for(const _ of c)h.set(_,m),m+=_.length;return h}(s)}function f0(n){const e=typeof n.data=="string"?yu(n.data):n.data;return[Yt({type:"attachment",length:e.length,filename:n.filename,content_type:n.contentType,attachment_type:n.attachmentType}),e]}const p0={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket",raw_security:"security"};function Hh(n){return p0[n]}function eg(n){if(!n||!n.sdk)return;const{name:e,version:i}=n.sdk;return{name:e,version:i}}function h0(n,e,i,s){const a=eg(i),c=n.type&&n.type!=="replay_event"?n.type:"event";(function(h,m){m&&(h.sdk=h.sdk||{},h.sdk.name=h.sdk.name||m.name,h.sdk.version=h.sdk.version||m.version,h.sdk.integrations=[...h.sdk.integrations||[],...m.integrations||[]],h.sdk.packages=[...h.sdk.packages||[],...m.packages||[]])})(n,i&&i.sdk);const d=function(h,m,_,w){const E=h.sdkProcessingMetadata&&h.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:h.event_id,sent_at:new Date().toISOString(),...m&&{sdk:m},...!!_&&w&&{dsn:ba(w)},...E&&{trace:Yt({...E})}}}(n,a,s,e);return delete n.sdkProcessingMetadata,As(d,[[{type:c},n]])}function _u(n,e,i,s=0){return new Kt((a,c)=>{const d=n[s];if(e===null||typeof d!="function")a(e);else{const h=d({...e},i);We&&d.id&&h===null&&be.log(`Event processor "${d.id}" dropped event`),Fa(h)?h.then(m=>_u(n,m,i,s+1).then(a)).then(null,c):_u(n,h,i,s+1).then(a).then(null,c)}})}let ea,Qh,zc;function m0(n,e){const{fingerprint:i,span:s,breadcrumbs:a,sdkProcessingMetadata:c}=e;(function(d,h){const{extra:m,tags:_,user:w,contexts:E,level:x,transactionName:T}=h,P=Yt(m);P&&Object.keys(P).length&&(d.extra={...P,...d.extra});const L=Yt(_);L&&Object.keys(L).length&&(d.tags={...L,...d.tags});const F=Yt(w);F&&Object.keys(F).length&&(d.user={...F,...d.user});const Y=Yt(E);Y&&Object.keys(Y).length&&(d.contexts={...Y,...d.contexts}),x&&(d.level=x),T&&d.type!=="transaction"&&(d.transaction=T)})(n,e),s&&function(d,h){d.contexts={trace:i0(h),...d.contexts},d.sdkProcessingMetadata={dynamicSamplingContext:a0(h),...d.sdkProcessingMetadata};const m=Yv(h),_=Yu(m).description;_&&!d.transaction&&d.type==="transaction"&&(d.transaction=_)}(n,s),function(d,h){d.fingerprint=d.fingerprint?Array.isArray(d.fingerprint)?d.fingerprint:[d.fingerprint]:[],h&&(d.fingerprint=d.fingerprint.concat(h)),d.fingerprint&&!d.fingerprint.length&&delete d.fingerprint}(n,i),function(d,h){const m=[...d.breadcrumbs||[],...h];d.breadcrumbs=m.length?m:void 0}(n,a),function(d,h){d.sdkProcessingMetadata={...d.sdkProcessingMetadata,...h}}(n,c)}function Gh(n,e){const{extra:i,tags:s,user:a,contexts:c,level:d,sdkProcessingMetadata:h,breadcrumbs:m,fingerprint:_,eventProcessors:w,attachments:E,propagationContext:x,transactionName:T,span:P}=e;ta(n,"extra",i),ta(n,"tags",s),ta(n,"user",a),ta(n,"contexts",c),n.sdkProcessingMetadata=Ba(n.sdkProcessingMetadata,h,2),d&&(n.level=d),T&&(n.transactionName=T),P&&(n.span=P),m.length&&(n.breadcrumbs=[...n.breadcrumbs,...m]),_.length&&(n.fingerprint=[...n.fingerprint,..._]),w.length&&(n.eventProcessors=[...n.eventProcessors,...w]),E.length&&(n.attachments=[...n.attachments,...E]),n.propagationContext={...n.propagationContext,...x}}function ta(n,e,i){n[e]=Ba(n[e],i,1)}function v0(n,e,i,s,a,c){const{normalizeDepth:d=3,normalizeMaxBreadth:h=1e3}=n,m={...e,event_id:e.event_id||i.event_id||Zt(),timestamp:e.timestamp||Ns()},_=i.integrations||n.integrations.map(P=>P.name);(function(P,L){const{environment:F,release:Y,dist:ne,maxValueLength:re=250}=L;P.environment=P.environment||F||Xu,!P.release&&Y&&(P.release=Y),!P.dist&&ne&&(P.dist=ne),P.message&&(P.message=bi(P.message,re));const B=P.exception&&P.exception.values&&P.exception.values[0];B&&B.value&&(B.value=bi(B.value,re));const I=P.request;I&&I.url&&(I.url=bi(I.url,re))})(m,n),function(P,L){L.length>0&&(P.sdk=P.sdk||{},P.sdk.integrations=[...P.sdk.integrations||[],...L])}(m,_),a&&a.emit("applyFrameMetadata",e),e.type===void 0&&function(P,L){const F=function(Y){const ne=De._sentryDebugIds;if(!ne)return{};const re=Object.keys(ne);return zc&&re.length===Qh||(Qh=re.length,zc=re.reduce((B,I)=>{ea||(ea={});const U=ea[I];if(U)B[U[0]]=U[1];else{const R=Y(I);for(let z=R.length-1;z>=0;z--){const X=R[z],$=X&&X.filename,te=ne[I];if($&&te){B[$]=te,ea[I]=[$,te];break}}}return B},{})),zc}(L);try{P.exception.values.forEach(Y=>{Y.stacktrace.frames.forEach(ne=>{F&&ne.filename&&(ne.debug_id=F[ne.filename])})})}catch{}}(m,n.stackParser);const w=function(P,L){if(!L)return P;const F=P?P.clone():new gr;return F.update(L),F}(s,i.captureContext);i.mechanism&&Ti(m,i.mechanism);const E=a?a.getEventProcessors():[],x=wa("globalScope",()=>new gr).getScopeData();c&&Gh(x,c.getScopeData()),w&&Gh(x,w.getScopeData());const T=[...i.attachments||[],...x.attachments];return T.length&&(i.attachments=T),m0(m,x),_u([...E,...x.eventProcessors],m,i).then(P=>(P&&function(L){const F={};try{L.exception.values.forEach(ne=>{ne.stacktrace.frames.forEach(re=>{re.debug_id&&(re.abs_path?F[re.abs_path]=re.debug_id:re.filename&&(F[re.filename]=re.debug_id),delete re.debug_id)})})}catch{}if(Object.keys(F).length===0)return;L.debug_meta=L.debug_meta||{},L.debug_meta.images=L.debug_meta.images||[];const Y=L.debug_meta.images;Object.entries(F).forEach(([ne,re])=>{Y.push({type:"sourcemap",code_file:ne,debug_id:re})})}(P),typeof d=="number"&&d>0?function(L,F,Y){if(!L)return null;const ne={...L,...L.breadcrumbs&&{breadcrumbs:L.breadcrumbs.map(re=>({...re,...re.data&&{data:In(re.data,F,Y)}}))},...L.user&&{user:In(L.user,F,Y)},...L.contexts&&{contexts:In(L.contexts,F,Y)},...L.extra&&{extra:In(L.extra,F,Y)}};return L.contexts&&L.contexts.trace&&ne.contexts&&(ne.contexts.trace=L.contexts.trace,L.contexts.trace.data&&(ne.contexts.trace.data=In(L.contexts.trace.data,F,Y))),L.spans&&(ne.spans=L.spans.map(re=>({...re,...re.data&&{data:In(re.data,F,Y)}}))),L.contexts&&L.contexts.flags&&ne.contexts&&(ne.contexts.flags=In(L.contexts.flags,3,Y)),ne}(P,d,h):P))}function Kh(n,e){return Qr().captureEvent(n,e)}function Yh(n){const e=yt(),i=js(),s=Qr(),{release:a,environment:c=Xu}=e&&e.getOptions()||{},{userAgent:d}=De.navigator||{},h=Kw({release:a,environment:c,user:s.getUser()||i.getUser(),...d&&{userAgent:d},...n}),m=i.getSession();return m&&m.status==="ok"&&Pi(m,{status:"exited"}),tg(),i.setSession(h),s.setSession(h),h}function tg(){const n=js(),e=Qr(),i=e.getSession()||n.getSession();i&&function(s,a){let c={};s.status==="ok"&&(c={status:"exited"}),Pi(s,c)}(i),ng(),n.setSession(),e.setSession()}function ng(){const n=js(),e=Qr(),i=yt(),s=e.getSession()||n.getSession();s&&i&&i.captureSession(s)}function Xh(n=!1){n?tg():ng()}function g0(n,e,i){return e||`${function(s){return`${function(a){const c=a.protocol?`${a.protocol}:`:"",d=a.port?`:${a.port}`:"";return`${c}//${a.host}${d}${a.path?`/${a.path}`:""}/api/`}(s)}${s.projectId}/envelope/`}(n)}?${function(s,a){const c={sentry_version:"7"};return s.publicKey&&(c.sentry_key=s.publicKey),a&&(c.sentry_client=`${a.name}/${a.version}`),new URLSearchParams(c).toString()}(n,i)}`}const Zh=[];function em(n,e){for(const i of e)i&&i.afterAllSetup&&i.afterAllSetup(n)}function tm(n,e,i){if(i[e.name])We&&be.log(`Integration skipped because it was already installed: ${e.name}`);else{if(i[e.name]=e,Zh.indexOf(e.name)===-1&&typeof e.setupOnce=="function"&&(e.setupOnce(),Zh.push(e.name)),e.setup&&typeof e.setup=="function"&&e.setup(n),typeof e.preprocessEvent=="function"){const s=e.preprocessEvent.bind(e);n.on("preprocessEvent",(a,c)=>s(a,c,n))}if(typeof e.processEvent=="function"){const s=e.processEvent.bind(e),a=Object.assign((c,d)=>s(c,d,n),{id:e.name});n.addEventProcessor(a)}We&&be.log(`Integration installed: ${e.name}`)}}class pn extends Error{constructor(e,i="warn"){super(e),this.message=e,this.logLevel=i}}const nm="Not capturing exception because it's already been captured.";class y0{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn?this._dsn=c0(e.dsn):We&&be.warn("No DSN provided, client will not send events."),this._dsn){const s=g0(this._dsn,e.tunnel,e._metadata?e._metadata.sdk:void 0);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:s})}const i=["enableTracing","tracesSampleRate","tracesSampler"].find(s=>s in e&&e[s]==null);i&&Ls(()=>{console.warn(`[Sentry] Deprecation warning: \`${i}\` is set to undefined, which leads to tracing being enabled. In v9, a value of \`undefined\` will result in tracing being disabled.`)})}captureException(e,i,s){const a=Zt();if(Dh(e))return We&&be.log(nm),a;const c={event_id:a,...i};return this._process(this.eventFromException(e,c).then(d=>this._captureEvent(d,c,s))),c.event_id}captureMessage(e,i,s,a){const c={event_id:Zt(),...s},d=Ju(e)?e:String(e),h=fu(e)?this.eventFromMessage(d,i,c):this.eventFromException(e,c);return this._process(h.then(m=>this._captureEvent(m,c,a))),c.event_id}captureEvent(e,i,s){const a=Zt();if(i&&i.originalException&&Dh(i.originalException))return We&&be.log(nm),a;const c={event_id:a,...i},d=(e.sdkProcessingMetadata||{}).capturedSpanScope;return this._process(this._captureEvent(e,c,d||s)),c.event_id}captureSession(e){typeof e.release!="string"?We&&be.warn("Discarded session because of missing or non-string release"):(this.sendSession(e),Pi(e,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){const i=this._transport;return i?(this.emit("flush"),this._isClientDoneProcessing(e).then(s=>i.flush(e).then(a=>s&&a))):Wr(!0)}close(e){return this.flush(e).then(i=>(this.getOptions().enabled=!1,this.emit("close"),i))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some(({name:e})=>e.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){const i=this._integrations[e.name];tm(this,e,this._integrations),i||em(this,[e])}sendEvent(e,i={}){this.emit("beforeSendEvent",e,i);let s=h0(e,this._dsn,this._options._metadata,this._options.tunnel);for(const c of i.attachments||[])s=u0(s,f0(c));const a=this.sendEnvelope(s);a&&a.then(c=>this.emit("afterSendEvent",e,c),null)}sendSession(e){const i=function(s,a,c,d){const h=eg(c);return As({sent_at:new Date().toISOString(),...h&&{sdk:h},...!!d&&a&&{dsn:ba(a)}},["aggregates"in s?[{type:"sessions"},s]:[{type:"session"},s.toJSON()]])}(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(i)}recordDroppedEvent(e,i,s){if(this._options.sendClientReports){const a=typeof s=="number"?s:1,c=`${e}:${i}`;We&&be.log(`Recording outcome: "${c}"${a>1?` (${a} times)`:""}`),this._outcomes[c]=(this._outcomes[c]||0)+a}}on(e,i){const s=this._hooks[e]=this._hooks[e]||[];return s.push(i),()=>{const a=s.indexOf(i);a>-1&&s.splice(a,1)}}emit(e,...i){const s=this._hooks[e];s&&s.forEach(a=>a(...i))}sendEnvelope(e){return this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport?this._transport.send(e).then(null,i=>(We&&be.error("Error while sending envelope:",i),i)):(We&&be.error("Transport disabled"),Wr({}))}_setupIntegrations(){const{integrations:e}=this._options;this._integrations=function(i,s){const a={};return s.forEach(c=>{c&&tm(i,c,a)}),a}(this,e),em(this,e)}_updateSessionFromEvent(e,i){let s=i.level==="fatal",a=!1;const c=i.exception&&i.exception.values;if(c){a=!0;for(const h of c){const m=h.mechanism;if(m&&m.handled===!1){s=!0;break}}}const d=e.status==="ok";(d&&e.errors===0||d&&s)&&(Pi(e,{...s&&{status:"crashed"},errors:e.errors||Number(a||s)}),this.captureSession(e))}_isClientDoneProcessing(e){return new Kt(i=>{let s=0;const a=setInterval(()=>{this._numProcessing==0?(clearInterval(a),i(!0)):(s+=1,e&&s>=e&&(clearInterval(a),i(!1)))},1)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(e,i,s=Qr(),a=js()){const c=this.getOptions(),d=Object.keys(this._integrations);return!i.integrations&&d.length>0&&(i.integrations=d),this.emit("preprocessEvent",e,i),e.type||a.setLastEventId(e.event_id||i.event_id),v0(c,e,i,s,this,a).then(h=>{if(h===null)return h;h.contexts={trace:e0(s),...h.contexts};const m=function(_,w){const E=w.getPropagationContext();return E.dsc||Xv(E.traceId,_)}(this,s);return h.sdkProcessingMetadata={dynamicSamplingContext:m,...h.sdkProcessingMetadata},h})}_captureEvent(e,i={},s){return this._processEvent(e,i,s).then(a=>a.event_id,a=>{We&&(a instanceof pn&&a.logLevel==="log"?be.log(a.message):be.warn(a))})}_processEvent(e,i,s){const a=this.getOptions(),{sampleRate:c}=a,d=im(e),h=rm(e),m=e.type||"error",_=`before send for type \`${m}\``,w=c===void 0?void 0:function(T){if(typeof T=="boolean")return Number(T);const P=typeof T=="string"?parseFloat(T):T;if(!(typeof P!="number"||isNaN(P)||P<0||P>1))return P;We&&be.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(T)} of type ${JSON.stringify(typeof T)}.`)}(c);if(h&&typeof w=="number"&&Math.random()>w)return this.recordDroppedEvent("sample_rate","error",e),ka(new pn(`Discarding event because it's not included in the random sample (sampling rate = ${c})`,"log"));const E=m==="replay_event"?"replay":m,x=(e.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this._prepareEvent(e,i,s,x).then(T=>{if(T===null)throw this.recordDroppedEvent("event_processor",E,e),new pn("An event processor returned `null`, will not send event.","log");if(i.data&&i.data.__sentry__===!0)return T;const P=function(L,F,Y,ne){const{beforeSend:re,beforeSendTransaction:B,beforeSendSpan:I}=F;if(rm(Y)&&re)return re(Y,ne);if(im(Y)){if(Y.spans&&I){const U=[];for(const R of Y.spans){const z=I(R);z?U.push(z):(o0(),L.recordDroppedEvent("before_send","span"))}Y.spans=U}if(B){if(Y.spans){const U=Y.spans.length;Y.sdkProcessingMetadata={...Y.sdkProcessingMetadata,spanCountBeforeProcessing:U}}return B(Y,ne)}}return Y}(this,a,T,i);return function(L,F){const Y=`${F} must return \`null\` or a valid event.`;if(Fa(L))return L.then(ne=>{if(!Mi(ne)&&ne!==null)throw new pn(Y);return ne},ne=>{throw new pn(`${F} rejected with ${ne}`)});if(!Mi(L)&&L!==null)throw new pn(Y);return L}(P,_)}).then(T=>{if(T===null){if(this.recordDroppedEvent("before_send",E,e),d){const F=1+(e.spans||[]).length;this.recordDroppedEvent("before_send","span",F)}throw new pn(`${_} returned \`null\`, will not send event.`,"log")}const P=s&&s.getSession();if(!d&&P&&this._updateSessionFromEvent(P,T),d){const F=(T.sdkProcessingMetadata&&T.sdkProcessingMetadata.spanCountBeforeProcessing||0)-(T.spans?T.spans.length:0);F>0&&this.recordDroppedEvent("before_send","span",F)}const L=T.transaction_info;if(d&&L&&T.transaction!==e.transaction){const F="custom";T.transaction_info={...L,source:F}}return this.sendEvent(T,i),T}).then(null,T=>{throw T instanceof pn?T:(this.captureException(T,{data:{__sentry__:!0},originalException:T}),new pn(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${T}`))})}_process(e){this._numProcessing++,e.then(i=>(this._numProcessing--,i),i=>(this._numProcessing--,i))}_clearOutcomes(){const e=this._outcomes;return this._outcomes={},Object.entries(e).map(([i,s])=>{const[a,c]=i.split(":");return{reason:a,category:c,quantity:s}})}_flushOutcomes(){We&&be.log("Flushing outcomes...");const e=this._clearOutcomes();if(e.length===0)return void(We&&be.log("No outcomes to send"));if(!this._dsn)return void(We&&be.log("No dsn provided, will not send outcomes"));We&&be.log("Sending outcomes:",e);const i=(s=e,As((a=this._options.tunnel&&ba(this._dsn))?{dsn:a}:{},[[{type:"client_report"},{timestamp:c||Ns(),discarded_events:s}]]));var s,a,c;this.sendEnvelope(i)}}function rm(n){return n.type===void 0}function im(n){return n.type==="transaction"}function _0(n){const e=[];function i(s){return e.splice(e.indexOf(s),1)[0]||Promise.resolve(void 0)}return{$:e,add:function(s){if(!(n===void 0||e.length<n))return ka(new pn("Not adding Promise because buffer limit was reached."));const a=s();return e.indexOf(a)===-1&&e.push(a),a.then(()=>i(a)).then(null,()=>i(a).then(null,()=>{})),a},drain:function(s){return new Kt((a,c)=>{let d=e.length;if(!d)return a(!0);const h=setTimeout(()=>{s&&s>0&&a(!1)},s);e.forEach(m=>{Wr(m).then(()=>{--d||(clearTimeout(h),a(!0))},c)})})}}}function w0(n,{statusCode:e,headers:i},s=Date.now()){const a={...n},c=i&&i["x-sentry-rate-limits"],d=i&&i["retry-after"];if(c)for(const h of c.trim().split(",")){const[m,_,,,w]=h.split(":",5),E=parseInt(m,10),x=1e3*(isNaN(E)?60:E);if(_)for(const T of _.split(";"))T==="metric_bucket"&&w&&!w.split(";").includes("custom")||(a[T]=s+x);else a.all=s+x}else d?a.all=s+function(h,m=Date.now()){const _=parseInt(`${h}`,10);if(!isNaN(_))return 1e3*_;const w=Date.parse(`${h}`);return isNaN(w)?6e4:w-m}(d,s):e===429&&(a.all=s+6e4);return a}function S0(n,e,i=_0(n.bufferSize||64)){let s={};return{send:function(a){const c=[];if(Jh(a,(m,_)=>{const w=Hh(_);if(function(E,x,T=Date.now()){return function(P,L){return P[L]||P.all||0}(E,x)>T}(s,w)){const E=sm(m,_);n.recordDroppedEvent("ratelimit_backoff",w,E)}else c.push(m)}),c.length===0)return Wr({});const d=As(a[0],c),h=m=>{Jh(d,(_,w)=>{const E=sm(_,w);n.recordDroppedEvent(m,Hh(w),E)})};return i.add(()=>e({body:d0(d)}).then(m=>(m.statusCode!==void 0&&(m.statusCode<200||m.statusCode>=300)&&We&&be.warn(`Sentry responded with status code ${m.statusCode} to sent event.`),s=w0(s,m),m),m=>{throw h("network_error"),m})).then(m=>m,m=>{if(m instanceof pn)return We&&be.error("Skipped sending event because buffer is full."),h("queue_overflow"),Wr({});throw m})},flush:a=>i.drain(a)}}function sm(n,e){if(e==="event"||e==="transaction")return Array.isArray(n)?n[1]:void 0}const k0=100;function Lr(n,e){const i=yt(),s=js();if(!i)return;const{beforeBreadcrumb:a=null,maxBreadcrumbs:c=k0}=i.getOptions();if(c<=0)return;const d={timestamp:Ns(),...n},h=a?Ls(()=>a(d,e)):d;h!==null&&(i.emit&&i.emit("beforeAddBreadcrumb",h,e),s.addBreadcrumb(h,c))}let om;const am=new WeakMap,b0=()=>({name:"FunctionToString",setupOnce(){om=Function.prototype.toString;try{Function.prototype.toString=function(...n){const e=Qu(this),i=am.has(yt())&&e!==void 0?e:this;return om.apply(i,n)}}catch{}},setup(n){am.set(n,!0)}}),E0=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,"undefined is not an object (evaluating 'a.L')",`can't redefine non-configurable property "solana"`,"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/],C0=(n={})=>({name:"InboundFilters",processEvent(e,i,s){const a=s.getOptions(),c=function(d={},h={}){return{allowUrls:[...d.allowUrls||[],...h.allowUrls||[]],denyUrls:[...d.denyUrls||[],...h.denyUrls||[]],ignoreErrors:[...d.ignoreErrors||[],...h.ignoreErrors||[],...d.disableErrorDefaults?[]:E0],ignoreTransactions:[...d.ignoreTransactions||[],...h.ignoreTransactions||[]],ignoreInternal:d.ignoreInternal===void 0||d.ignoreInternal}}(n,a);return function(d,h){return h.ignoreInternal&&function(m){try{return m.exception.values[0].type==="SentryError"}catch{}return!1}(d)?(We&&be.warn(`Event dropped due to being internal Sentry Error.
Event: ${fr(d)}`),!0):function(m,_){return m.type||!_||!_.length?!1:function(w){const E=[];w.message&&E.push(w.message);let x;try{x=w.exception.values[w.exception.values.length-1]}catch{}return x&&x.value&&(E.push(x.value),x.type&&E.push(`${x.type}: ${x.value}`)),E}(m).some(w=>Zo(w,_))}(d,h.ignoreErrors)?(We&&be.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${fr(d)}`),!0):function(m){return m.type||!m.exception||!m.exception.values||m.exception.values.length===0?!1:!m.message&&!m.exception.values.some(_=>_.stacktrace||_.type&&_.type!=="Error"||_.value)}(d)?(We&&be.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${fr(d)}`),!0):function(m,_){if(m.type!=="transaction"||!_||!_.length)return!1;const w=m.transaction;return!!w&&Zo(w,_)}(d,h.ignoreTransactions)?(We&&be.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${fr(d)}`),!0):function(m,_){if(!_||!_.length)return!1;const w=na(m);return!!w&&Zo(w,_)}(d,h.denyUrls)?(We&&be.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${fr(d)}.
Url: ${na(d)}`),!0):function(m,_){if(!_||!_.length)return!0;const w=na(m);return!w||Zo(w,_)}(d,h.allowUrls)?!1:(We&&be.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${fr(d)}.
Url: ${na(d)}`),!0)}(e,c)?null:e}});function na(n){try{let e;try{e=n.exception.values[0].stacktrace.frames}catch{}return e?function(i=[]){for(let s=i.length-1;s>=0;s--){const a=i[s];if(a&&a.filename!=="<anonymous>"&&a.filename!=="[native code]")return a.filename||null}return null}(e):null}catch{return We&&be.error(`Cannot extract url for event ${fr(n)}`),null}}function M0(n,e,i=250,s,a,c,d){if(!(c.exception&&c.exception.values&&d&&zr(d.originalException,Error)))return;const h=c.exception.values.length>0?c.exception.values[c.exception.values.length-1]:void 0;var m,_;h&&(c.exception.values=(m=wu(n,e,a,d.originalException,s,c.exception.values,h,0),_=i,m.map(w=>(w.value&&(w.value=bi(w.value,_)),w))))}function wu(n,e,i,s,a,c,d,h){if(c.length>=i+1)return c;let m=[...c];if(zr(s[a],Error)){lm(d,h);const _=n(e,s[a]),w=m.length;cm(_,a,w,h),m=wu(n,e,i,s[a],a,[_,...m],_,w)}return Array.isArray(s.errors)&&s.errors.forEach((_,w)=>{if(zr(_,Error)){lm(d,h);const E=n(e,_),x=m.length;cm(E,`errors[${w}]`,x,h),m=wu(n,e,i,_,a,[E,...m],E,x)}}),m}function lm(n,e){n.mechanism=n.mechanism||{type:"generic",handled:!0},n.mechanism={...n.mechanism,...n.type==="AggregateError"&&{is_exception_group:!0},exception_id:e}}function cm(n,e,i,s){n.mechanism=n.mechanism||{type:"generic",handled:!0},n.mechanism={...n.mechanism,type:"chained",source:e,exception_id:i,parent_id:s}}function Vc(n){if(!n)return{};const e=n.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!e)return{};const i=e[6]||"",s=e[8]||"";return{host:e[4],path:e[5],protocol:e[2],search:i,hash:s,relative:e[5]+i+s}}function T0(){"console"in De&&du.forEach(function(n){n in De.console&&$t(De.console,n,function(e){return Sa[n]=e,function(...i){vn("console",{args:i,level:n});const s=Sa[n];s&&s.apply(De.console,i)}})})}function P0(n){return n==="warn"?"warning":["fatal","error","warning","log","info","debug"].includes(n)?n:"log"}const O0=()=>{let n;return{name:"Dedupe",processEvent(e){if(e.type)return e;try{if(function(i,s){return s?!!(function(a,c){const d=a.message,h=c.message;return!(!d&&!h||d&&!h||!d&&h||d!==h||!dm(a,c)||!um(a,c))}(i,s)||function(a,c){const d=fm(c),h=fm(a);return!(!d||!h||d.type!==h.type||d.value!==h.value||!dm(a,c)||!um(a,c))}(i,s)):!1}(e,n))return We&&be.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{}return n=e}}};function um(n,e){let i=xh(n),s=xh(e);if(!i&&!s)return!0;if(i&&!s||!i&&s||s.length!==i.length)return!1;for(let a=0;a<s.length;a++){const c=s[a],d=i[a];if(c.filename!==d.filename||c.lineno!==d.lineno||c.colno!==d.colno||c.function!==d.function)return!1}return!0}function dm(n,e){let i=n.fingerprint,s=e.fingerprint;if(!i&&!s)return!0;if(i&&!s||!i&&s)return!1;try{return i.join("")===s.join("")}catch{return!1}}function fm(n){return n.exception&&n.exception.values&&n.exception.values[0]}function pm(n){return n===void 0?void 0:n>=400&&n<500?"warning":n>=500?"error":void 0}const Wc=De;function Su(n){return n&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(n.toString())}function x0(){if(typeof EdgeRuntime=="string")return!0;if(!function(){if(!("fetch"in Wc))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}())return!1;if(Su(Wc.fetch))return!0;let n=!1;const e=Wc.document;if(e&&typeof e.createElement=="function")try{const i=e.createElement("iframe");i.hidden=!0,e.head.appendChild(i),i.contentWindow&&i.contentWindow.fetch&&(n=Su(i.contentWindow.fetch)),e.head.removeChild(i)}catch(i){Hr&&be.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",i)}return n}function A0(n,e){const i="fetch";$r(i,n),Ur(i,()=>function(s,a=!1){a&&!x0()||$t(De,"fetch",function(c){return function(...d){const h=new Error,{method:m,url:_}=function(E){if(E.length===0)return{method:"GET",url:""};if(E.length===2){const[T,P]=E;return{url:hm(T),method:ku(P,"method")?String(P.method).toUpperCase():"GET"}}const x=E[0];return{url:hm(x),method:ku(x,"method")?String(x.method).toUpperCase():"GET"}}(d),w={args:d,fetchData:{method:m,url:_},startTimestamp:1e3*$n(),virtualError:h};return s||vn("fetch",{...w}),c.apply(De,d).then(async E=>(s?s(E):vn("fetch",{...w,endTimestamp:1e3*$n(),response:E}),E),E=>{throw vn("fetch",{...w,endTimestamp:1e3*$n(),error:E}),qu(E)&&E.stack===void 0&&(E.stack=h.stack,Vr(E,"framesToPop",1)),E})}})}(void 0,e))}function ku(n,e){return!!n&&typeof n=="object"&&!!n[e]}function hm(n){return typeof n=="string"?n:n?ku(n,"url")?n.url:n.toString?n.toString():"":""}const ra=De,lt=De;let bu=0;function mm(){return bu>0}function xi(n,e={}){if(!function(s){return typeof s=="function"}(n))return n;try{const s=n.__sentry_wrapped__;if(s)return typeof s=="function"?s:n;if(Qu(n))return n}catch{return n}const i=function(...s){try{const a=s.map(c=>xi(c,e));return n.apply(this,a)}catch(a){throw bu++,setTimeout(()=>{bu--}),function(...c){const d=Ku(Da());if(c.length===2){const[h,m]=c;return h?d.withSetScope(h,m):d.withScope(m)}d.withScope(c[0])}(c=>{var d;c.addEventProcessor(h=>(e.mechanism&&(hu(h,void 0),Ti(h,e.mechanism)),h.extra={...h.extra,arguments:s},h)),d=a,Qr().captureException(d,void 0)}),a}};try{for(const s in n)Object.prototype.hasOwnProperty.call(n,s)&&(i[s]=n[s])}catch{}Qv(i,n),Vr(n,"__sentry_wrapped__",i);try{Object.getOwnPropertyDescriptor(i,"name").configurable&&Object.defineProperty(i,"name",{get:()=>n.name})}catch{}return i}const $a=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__;function Zu(n,e){const i=ed(n,e),s={type:j0(e),value:R0(e)};return i.length&&(s.stacktrace={frames:i}),s.type===void 0&&s.value===""&&(s.value="Unrecoverable error caught"),s}function L0(n,e,i,s){const a=yt(),c=a&&a.getOptions().normalizeDepth,d=function(_){for(const w in _)if(Object.prototype.hasOwnProperty.call(_,w)){const E=_[w];if(E instanceof Error)return E}}(e),h={__serialized__:Zv(e,c)};if(d)return{exception:{values:[Zu(n,d)]},extra:h};const m={exception:{values:[{type:Ia(e)?e.constructor.name:s?"UnhandledRejection":"Error",value:D0(e,{isUnhandledRejection:s})}]},extra:h};if(i){const _=ed(n,i);_.length&&(m.exception.values[0].stacktrace={frames:_})}return m}function qc(n,e){return{exception:{values:[Zu(n,e)]}}}function ed(n,e){const i=e.stacktrace||e.stack||"",s=function(c){return c&&N0.test(c.message)?1:0}(e),a=function(c){return typeof c.framesToPop=="number"?c.framesToPop:0}(e);try{return n(i,s,a)}catch{}return[]}const N0=/Minified React error #\d+;/i;function rg(n){return typeof WebAssembly<"u"&&WebAssembly.Exception!==void 0&&n instanceof WebAssembly.Exception}function j0(n){const e=n&&n.name;return!e&&rg(n)?n.message&&Array.isArray(n.message)&&n.message.length==2?n.message[0]:"WebAssembly.Exception":e}function R0(n){const e=n&&n.message;return e?e.error&&typeof e.error.message=="string"?e.error.message:rg(n)&&Array.isArray(n.message)&&n.message.length==2?n.message[1]:e:"No error message"}function Eu(n,e,i,s,a){let c;if(qv(e)&&e.error)return qc(n,e.error);if(Lh(e)||Ni(e,"DOMException")){const d=e;if("stack"in e)c=qc(n,e);else{const h=d.name||(Lh(d)?"DOMError":"DOMException"),m=d.message?`${h}: ${d.message}`:h;c=Cu(n,m,i,s),hu(c,m)}return"code"in d&&(c.tags={...c.tags,"DOMException.code":`${d.code}`}),c}return qu(e)?qc(n,e):Mi(e)||Ia(e)?(c=L0(n,e,i,a),Ti(c,{synthetic:!0}),c):(c=Cu(n,e,i,s),hu(c,`${e}`),Ti(c,{synthetic:!0}),c)}function Cu(n,e,i,s){const a={};if(s&&i){const c=ed(n,i);c.length&&(a.exception={values:[{value:e,stacktrace:{frames:c}}]}),Ti(a,{synthetic:!0})}if(Ju(e)){const{__sentry_template_string__:c,__sentry_template_values__:d}=e;return a.logentry={message:c,params:d},a}return a.message=e,a}function D0(n,{isUnhandledRejection:e}){const i=function(a,c=40){const d=Object.keys(Gv(a));d.sort();const h=d[0];if(!h)return"[object has no keys]";if(h.length>=c)return bi(h,c);for(let m=d.length;m>0;m--){const _=d.slice(0,m).join(", ");if(!(_.length>c))return m===d.length?_:bi(_,c)}return""}(n),s=e?"promise rejection":"exception";return qv(n)?`Event \`ErrorEvent\` captured as ${s} with message \`${n.message}\``:Ia(n)?`Event \`${function(a){try{const c=Object.getPrototypeOf(a);return c?c.constructor.name:void 0}catch{}}(n)}\` (type=${n.type}) captured as ${s}`:`Object captured as ${s} with keys: ${i}`}class I0 extends y0{constructor(e){const i={parentSpanIsAlwaysRootSpan:!0,...e};(function(s,a,c=[a],d="npm"){const h=s._metadata||{};h.sdk||(h.sdk={name:`sentry.javascript.${a}`,packages:c.map(m=>({name:`${d}:@sentry/${m}`,version:Br})),version:Br}),s._metadata=h})(i,"browser",["browser"],lt.SENTRY_SDK_SOURCE||"npm"),super(i),i.sendClientReports&&lt.document&&lt.document.addEventListener("visibilitychange",()=>{lt.document.visibilityState==="hidden"&&this._flushOutcomes()})}eventFromException(e,i){return function(s,a,c,d){const h=Eu(s,a,c&&c.syntheticException||void 0,d);return Ti(h),h.level="error",c&&c.event_id&&(h.event_id=c.event_id),Wr(h)}(this._options.stackParser,e,i,this._options.attachStacktrace)}eventFromMessage(e,i="info",s){return function(a,c,d="info",h,m){const _=Cu(a,c,h&&h.syntheticException||void 0,m);return _.level=d,h&&h.event_id&&(_.event_id=h.event_id),Wr(_)}(this._options.stackParser,e,i,s,this._options.attachStacktrace)}captureUserFeedback(e){if(!this._isEnabled())return void($a&&be.warn("SDK not enabled, will not capture user feedback."));const i=function(s,{metadata:a,tunnel:c,dsn:d}){const h={event_id:s.event_id,sent_at:new Date().toISOString(),...a&&a.sdk&&{sdk:{name:a.sdk.name,version:a.sdk.version}},...!!c&&!!d&&{dsn:ba(d)}};return As(h,[function(_){return[{type:"user_report"},_]}(s)])}(e,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this.sendEnvelope(i)}_prepareEvent(e,i,s){return e.platform=e.platform||"javascript",super._prepareEvent(e,i,s)}}const F0=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Ct=De;let vm,Jc,Hc,ia;function B0(){if(!Ct.document)return;const n=vn.bind(null,"dom"),e=gm(n,!0);Ct.document.addEventListener("click",e,!1),Ct.document.addEventListener("keypress",e,!1),["EventTarget","Node"].forEach(i=>{const s=Ct[i],a=s&&s.prototype;a&&a.hasOwnProperty&&a.hasOwnProperty("addEventListener")&&($t(a,"addEventListener",function(c){return function(d,h,m){if(d==="click"||d=="keypress")try{const _=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},w=_[d]=_[d]||{refCount:0};if(!w.handler){const E=gm(n);w.handler=E,c.call(this,d,E,m)}w.refCount++}catch{}return c.call(this,d,h,m)}}),$t(a,"removeEventListener",function(c){return function(d,h,m){if(d==="click"||d=="keypress")try{const _=this.__sentry_instrumentation_handlers__||{},w=_[d];w&&(w.refCount--,w.refCount<=0&&(c.call(this,d,w.handler,m),w.handler=void 0,delete _[d]),Object.keys(_).length===0&&delete this.__sentry_instrumentation_handlers__)}catch{}return c.call(this,d,h,m)}}))})}function gm(n,e=!1){return i=>{if(!i||i._sentryCaptured)return;const s=function(c){try{return c.target}catch{return null}}(i);if(function(c,d){return c==="keypress"&&(!d||!d.tagName||d.tagName!=="INPUT"&&d.tagName!=="TEXTAREA"&&!d.isContentEditable)}(i.type,s))return;Vr(i,"_sentryCaptured",!0),s&&!s._sentryId&&Vr(s,"_sentryId",Zt());const a=i.type==="keypress"?"input":i.type;(function(c){if(c.type!==Jc)return!1;try{if(!c.target||c.target._sentryId!==Hc)return!1}catch{}return!0})(i)||(n({event:i,name:a,global:e}),Jc=i.type,Hc=s?s._sentryId:void 0),clearTimeout(vm),vm=Ct.setTimeout(()=>{Hc=void 0,Jc=void 0},1e3)}}function ig(n){const e="history";$r(e,n),Ur(e,$0)}function $0(){if(!function(){const i=ra.chrome,s=i&&i.app&&i.app.runtime,a="history"in ra&&!!ra.history.pushState&&!!ra.history.replaceState;return!s&&a}())return;const n=Ct.onpopstate;function e(i){return function(...s){const a=s.length>2?s[2]:void 0;if(a){const c=ia,d=String(a);ia=d,vn("history",{from:c,to:d})}return i.apply(this,s)}}Ct.onpopstate=function(...i){const s=Ct.location.href,a=ia;if(ia=s,vn("history",{from:a,to:s}),n)try{return n.apply(this,i)}catch{}},$t(Ct.history,"pushState",e),$t(Ct.history,"replaceState",e)}const pa={};function ym(n){pa[n]=void 0}const Ms="__sentry_xhr_v3__";function U0(){if(!Ct.XMLHttpRequest)return;const n=XMLHttpRequest.prototype;n.open=new Proxy(n.open,{apply(e,i,s){const a=new Error,c=1e3*$n(),d=Fn(s[0])?s[0].toUpperCase():void 0,h=function(_){if(Fn(_))return _;try{return _.toString()}catch{}}(s[1]);if(!d||!h)return e.apply(i,s);i[Ms]={method:d,url:h,request_headers:{}},d==="POST"&&h.match(/sentry_key/)&&(i.__sentry_own_request__=!0);const m=()=>{const _=i[Ms];if(_&&i.readyState===4){try{_.status_code=i.status}catch{}vn("xhr",{endTimestamp:1e3*$n(),startTimestamp:c,xhr:i,virtualError:a})}};return"onreadystatechange"in i&&typeof i.onreadystatechange=="function"?i.onreadystatechange=new Proxy(i.onreadystatechange,{apply:(_,w,E)=>(m(),_.apply(w,E))}):i.addEventListener("readystatechange",m),i.setRequestHeader=new Proxy(i.setRequestHeader,{apply(_,w,E){const[x,T]=E,P=w[Ms];return P&&Fn(x)&&Fn(T)&&(P.request_headers[x.toLowerCase()]=T),_.apply(w,E)}}),e.apply(i,s)}}),n.send=new Proxy(n.send,{apply(e,i,s){const a=i[Ms];return a?(s[0]!==void 0&&(a.body=s[0]),vn("xhr",{startTimestamp:1e3*$n(),xhr:i}),e.apply(i,s)):e.apply(i,s)}})}function z0(n,e=function(i){const s=pa[i];if(s)return s;let a=Ct[i];if(Su(a))return pa[i]=a.bind(Ct);const c=Ct.document;if(c&&typeof c.createElement=="function")try{const d=c.createElement("iframe");d.hidden=!0,c.head.appendChild(d);const h=d.contentWindow;h&&h[i]&&(a=h[i]),c.head.removeChild(d)}catch(d){F0&&be.warn(`Could not create sandbox iframe for ${i} check, bailing to window.${i}: `,d)}return a&&(pa[i]=a.bind(Ct))}("fetch")){let i=0,s=0;return S0(n,function(a){const c=a.body.length;i+=c,s++;const d={body:a.body,method:"POST",referrerPolicy:"origin",headers:n.headers,keepalive:i<=6e4&&s<15,...n.fetchOptions};if(!e)return ym("fetch"),ka("No fetch implementation available");try{return e(n.url,d).then(h=>(i-=c,s--,{statusCode:h.status,headers:{"x-sentry-rate-limits":h.headers.get("X-Sentry-Rate-Limits"),"retry-after":h.headers.get("Retry-After")}}))}catch(h){return ym("fetch"),i-=c,s--,ka(h)}})}function Qc(n,e,i,s){const a={filename:n,function:e==="<anonymous>"?Fr:e,in_app:!0};return i!==void 0&&(a.lineno=i),s!==void 0&&(a.colno=s),a}const V0=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,W0=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,q0=/\((\S*)(?::(\d+))(?::(\d+))\)/,J0=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,H0=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Q0=function(...n){const e=n.sort((i,s)=>i[0]-s[0]).map(i=>i[1]);return(i,s=0,a=0)=>{const c=[],d=i.split(`
`);for(let h=s;h<d.length;h++){const m=d[h];if(m.length>1024)continue;const _=Th.test(m)?m.replace(Th,"$1"):m;if(!_.match(/\S*Error: /)){for(const w of e){const E=w(_);if(E){c.push(E);break}}if(c.length>=50+a)break}}return function(h){if(!h.length)return[];const m=Array.from(h);return/sentryWrapped/.test(Xo(m).function||"")&&m.pop(),m.reverse(),Ph.test(Xo(m).function||"")&&(m.pop(),Ph.test(Xo(m).function||"")&&m.pop()),m.slice(0,50).map(_=>({..._,filename:_.filename||Xo(m).filename,function:_.function||Fr}))}(c.slice(a))}}([30,n=>{const e=V0.exec(n);if(e){const[,s,a,c]=e;return Qc(s,Fr,+a,+c)}const i=W0.exec(n);if(i){if(i[2]&&i[2].indexOf("eval")===0){const c=q0.exec(i[2]);c&&(i[2]=c[1],i[3]=c[2],i[4]=c[3])}const[s,a]=_m(i[1]||Fr,i[2]);return Qc(a,s,i[3]?+i[3]:void 0,i[4]?+i[4]:void 0)}}],[50,n=>{const e=J0.exec(n);if(e){if(e[3]&&e[3].indexOf(" > eval")>-1){const a=H0.exec(e[3]);a&&(e[1]=e[1]||"eval",e[3]=a[1],e[4]=a[2],e[5]="")}let i=e[3],s=e[1]||Fr;return[s,i]=_m(s,i),Qc(i,s,e[4]?+e[4]:void 0,e[5]?+e[5]:void 0)}}]),_m=(n,e)=>{const i=n.indexOf("safari-extension")!==-1,s=n.indexOf("safari-web-extension")!==-1;return i||s?[n.indexOf("@")!==-1?n.split("@")[0]:Fr,i?`safari-extension:${e}`:`safari-web-extension:${e}`]:[n,e]},wm=1024,G0=(n={})=>{const e={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...n};return{name:"Breadcrumbs",setup(i){var s;e.console&&function(a){const c="console";$r(c,a),Ur(c,T0)}(function(a){return function(c){if(yt()!==a)return;const d={category:"console",data:{arguments:c.args,logger:"console"},level:P0(c.level),message:Nh(c.args," ")};if(c.level==="assert"){if(c.args[0]!==!1)return;d.message=`Assertion failed: ${Nh(c.args.slice(1)," ")||"console.assert"}`,d.data.arguments=c.args.slice(1)}Lr(d,{input:c.args,level:c.level})}}(i)),e.dom&&(s=function(a,c){return function(d){if(yt()!==a)return;let h,m,_=typeof c=="object"?c.serializeAttribute:void 0,w=typeof c=="object"&&typeof c.maxStringLength=="number"?c.maxStringLength:void 0;w&&w>wm&&($a&&be.warn(`\`dom.maxStringLength\` cannot exceed 1024, but a value of ${w} was configured. Sentry will use 1024 instead.`),w=wm),typeof _=="string"&&(_=[_]);try{const x=d.event,T=function(P){return!!P&&!!P.target}(x)?x.target:x;h=Hv(T,{keyAttrs:_,maxStringLength:w}),m=function(P){if(!Hu.HTMLElement)return null;let L=P;for(let F=0;F<5;F++){if(!L)return null;if(L instanceof HTMLElement){if(L.dataset.sentryComponent)return L.dataset.sentryComponent;if(L.dataset.sentryElement)return L.dataset.sentryElement}L=L.parentNode}return null}(T)}catch{h="<unknown>"}if(h.length===0)return;const E={category:`ui.${d.name}`,message:h};m&&(E.data={"ui.component_name":m}),Lr(E,{event:d.event,name:d.name,global:d.global})}}(i,e.dom),$r("dom",s),Ur("dom",B0)),e.xhr&&function(a){$r("xhr",a),Ur("xhr",U0)}(function(a){return function(c){if(yt()!==a)return;const{startTimestamp:d,endTimestamp:h}=c,m=c.xhr[Ms];if(!d||!h||!m)return;const{method:_,url:w,status_code:E,body:x}=m,T={method:_,url:w,status_code:E},P={xhr:c.xhr,input:x,startTimestamp:d,endTimestamp:h};Lr({category:"xhr",data:T,type:"http",level:pm(E)},P)}}(i)),e.fetch&&A0(function(a){return function(c){if(yt()!==a)return;const{startTimestamp:d,endTimestamp:h}=c;if(h&&(!c.fetchData.url.match(/sentry_key/)||c.fetchData.method!=="POST"))if(c.error)Lr({category:"fetch",data:c.fetchData,level:"error",type:"http"},{data:c.error,input:c.args,startTimestamp:d,endTimestamp:h});else{const m=c.response,_={...c.fetchData,status_code:m&&m.status},w={input:c.args,response:m,startTimestamp:d,endTimestamp:h};Lr({category:"fetch",data:_,type:"http",level:pm(_.status_code)},w)}}}(i)),e.history&&ig(function(a){return function(c){if(yt()!==a)return;let d=c.from,h=c.to;const m=Vc(lt.location.href);let _=d?Vc(d):void 0;const w=Vc(h);_&&_.path||(_=m),m.protocol===w.protocol&&m.host===w.host&&(h=w.relative),m.protocol===_.protocol&&m.host===_.host&&(d=_.relative),Lr({category:"navigation",data:{from:d,to:h}})}}(i)),e.sentry&&i.on("beforeSendEvent",function(a){return function(c){yt()===a&&Lr({category:"sentry."+(c.type==="transaction"?"transaction":"event"),event_id:c.event_id,level:c.level,message:fr(c)},{event:c})}}(i))}}},K0=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],Y0=(n={})=>{const e={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...n};return{name:"BrowserApiErrors",setupOnce(){e.setTimeout&&$t(lt,"setTimeout",Sm),e.setInterval&&$t(lt,"setInterval",Sm),e.requestAnimationFrame&&$t(lt,"requestAnimationFrame",X0),e.XMLHttpRequest&&"XMLHttpRequest"in lt&&$t(XMLHttpRequest.prototype,"send",Z0);const i=e.eventTarget;i&&(Array.isArray(i)?i:K0).forEach(eS)}}};function Sm(n){return function(...e){const i=e[0];return e[0]=xi(i,{mechanism:{data:{function:vr(n)},handled:!1,type:"instrument"}}),n.apply(this,e)}}function X0(n){return function(e){return n.apply(this,[xi(e,{mechanism:{data:{function:"requestAnimationFrame",handler:vr(n)},handled:!1,type:"instrument"}})])}}function Z0(n){return function(...e){const i=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(s=>{s in i&&typeof i[s]=="function"&&$t(i,s,function(a){const c={mechanism:{data:{function:s,handler:vr(a)},handled:!1,type:"instrument"}},d=Qu(a);return d&&(c.mechanism.data.handler=vr(d)),xi(a,c)})}),n.apply(this,e)}}function eS(n){const e=lt[n],i=e&&e.prototype;i&&i.hasOwnProperty&&i.hasOwnProperty("addEventListener")&&($t(i,"addEventListener",function(s){return function(a,c,d){try{typeof c.handleEvent=="function"&&(c.handleEvent=xi(c.handleEvent,{mechanism:{data:{function:"handleEvent",handler:vr(c),target:n},handled:!1,type:"instrument"}}))}catch{}return s.apply(this,[a,xi(c,{mechanism:{data:{function:"addEventListener",handler:vr(c),target:n},handled:!1,type:"instrument"}}),d])}}),$t(i,"removeEventListener",function(s){return function(a,c,d){try{const h=c.__sentry_wrapped__;h&&s.call(this,a,h,d)}catch{}return s.call(this,a,c,d)}}))}const tS=()=>({name:"BrowserSession",setupOnce(){lt.document!==void 0?(Yh({ignoreDuration:!0}),Xh(),ig(({from:n,to:e})=>{n!==void 0&&n!==e&&(Yh({ignoreDuration:!0}),Xh())})):$a&&be.warn("Using the `browserSessionIntegration` in non-browser environments is not supported.")}}),nS=(n={})=>{const e={onerror:!0,onunhandledrejection:!0,...n};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(i){e.onerror&&(function(s){(function(a){const c="error";$r(c,a),Ur(c,Jw)})(a=>{const{stackParser:c,attachStacktrace:d}=bm();if(yt()!==s||mm())return;const{msg:h,url:m,line:_,column:w,error:E}=a,x=function(T,P,L,F){const Y=T.exception=T.exception||{},ne=Y.values=Y.values||[],re=ne[0]=ne[0]||{},B=re.stacktrace=re.stacktrace||{},I=B.frames=B.frames||[],U=F,R=L,z=Fn(P)&&P.length>0?P:function(){try{return Hu.document.location.href}catch{return""}}();return I.length===0&&I.push({colno:U,filename:z,function:Fr,in_app:!0,lineno:R}),T}(Eu(c,E||h,void 0,d,!1),m,_,w);x.level="error",Kh(x,{originalException:E,mechanism:{handled:!1,type:"onerror"}})})}(i),km("onerror")),e.onunhandledrejection&&(function(s){(function(a){const c="unhandledrejection";$r(c,a),Ur(c,Hw)})(a=>{const{stackParser:c,attachStacktrace:d}=bm();if(yt()!==s||mm())return;const h=function(_){if(fu(_))return _;try{if("reason"in _)return _.reason;if("detail"in _&&"reason"in _.detail)return _.detail.reason}catch{}return _}(a),m=fu(h)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(h)}`}]}}:Eu(c,h,void 0,d,!0);m.level="error",Kh(m,{originalException:h,mechanism:{handled:!1,type:"onunhandledrejection"}})})}(i),km("onunhandledrejection"))}}};function km(n){$a&&be.log(`Global Handler attached: ${n}`)}function bm(){const n=yt();return n&&n.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}const rS=()=>({name:"HttpContext",preprocessEvent(n){if(!lt.navigator&&!lt.location&&!lt.document)return;const e=n.request&&n.request.url||lt.location&&lt.location.href,{referrer:i}=lt.document||{},{userAgent:s}=lt.navigator||{},a={...n.request&&n.request.headers,...i&&{Referer:i},...s&&{"User-Agent":s}},c={...n.request,...e&&{url:e},headers:a};n.request=c}}),iS=(n={})=>{const e=n.limit||5,i=n.key||"cause";return{name:"LinkedErrors",preprocessEvent(s,a,c){const d=c.getOptions();M0(Zu,d.stackParser,d.maxValueLength,i,e,s,a)}}};var Em="new",Cm="loading",Mm="loaded",Mu="joining-meeting",Ir="joined-meeting",Nr="left-meeting",lr="error",sS="playable",Gc="unknown",oS="full",aS="base",Tm="no-room",lS="end-of-life",cS="connection-error",uS="iframe-ready-for-launch-config",dS="iframe-launch-config",fS="theme-updated",pS="loading",hS="load-attempt-failed",Pm="loaded",mS="started-camera",vS="camera-error",gS="joining-meeting",yS="joined-meeting",_S="left-meeting",wS="participant-joined",SS="participant-updated",kS="participant-left",bS="participant-counts-updated",ES="access-state-updated",CS="meeting-session-summary-updated",MS="meeting-session-state-updated",TS="waiting-participant-added",PS="waiting-participant-updated",OS="waiting-participant-removed",xS="track-started",AS="track-stopped",LS="transcription-started",NS="transcription-stopped",jS="transcription-error",Om="recording-started",xm="recording-stopped",RS="recording-stats",DS="recording-error",IS="recording-upload-completed",FS="recording-data",BS="app-message",$S="transcription-message",US="remote-media-player-started",zS="remote-media-player-updated",VS="remote-media-player-stopped",WS="local-screen-share-started",qS="local-screen-share-stopped",JS="local-screen-share-canceled",HS="active-speaker-change",QS="active-speaker-mode-change",GS="network-quality-change",KS="network-connection",YS="cpu-load-change",XS="face-counts-updated",sa="fullscreen",oa="exited-fullscreen",ZS="live-streaming-started",ek="live-streaming-updated",tk="live-streaming-stopped",nk="live-streaming-error",rk="lang-updated",ik="receive-settings-updated",Am="input-settings-updated",Lm="nonfatal-error",Nm="error",Kc=4096,jm=102400,Yc="iframe-call-message",Rm="local-screen-start",Dm="daily-method-update-live-streaming-endpoints",aa="transmit-log",Bn="daily-custom-track",ha={NONE:"none",BGBLUR:"background-blur",BGIMAGE:"background-image",FACE_DETECTION:"face-detection"},sg={NONE:"none",NOISE_CANCELLATION:"noise-cancellation"},Tu={PLAY:"play",PAUSE:"pause"},Xc=["jpg","png","jpeg"],sk="add-endpoints",ok="remove-endpoints",Im="sip-call-transfer";function en(){return!xe()&&typeof window<"u"&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:""}function xe(){return typeof navigator<"u"&&navigator.product&&navigator.product==="ReactNative"}function og(){return navigator&&navigator.mediaDevices&&navigator.mediaDevices.getUserMedia}function ak(){return!!(navigator&&navigator.mediaDevices&&navigator.mediaDevices.getDisplayMedia)&&(function(n,e){if(!n||!e)return!0;switch(n){case"Chrome":return e.major>=75;case"Safari":return RTCRtpTransceiver.prototype.hasOwnProperty("currentDirection")&&!(e.major===13&&e.minor===0&&e.point===0);case"Firefox":return e.major>=67}return!0}(qr(),Ea())||xe())}function Fm(){if(xe()||!document)return!1;var n=document.createElement("iframe");return!!n.requestFullscreen||!!n.webkitRequestFullscreen}var lk=function(){try{var n=document.createElement("canvas"),e=(navigator.webdriver?n.getContext("webgl2"):n.getContext("webgl2",{failIfMajorPerformanceCaveat:!0}))!=null;return n.remove(),e}catch{return!1}}();function ag(){var n=arguments.length>0&&arguments[0]!==void 0&&arguments[0];return!xe()&&!!lk&&(n?function(){return $m()?!1:["Chrome","Firefox"].includes(qr())}():function(){if($m())return!1;var e=qr();if(e==="Safari"){var i=nd();if(i.major<15||i.major===15&&i.minor<4)return!1}return e==="Chrome"?td().major>=77:e==="Firefox"?rd().major>=97:["Chrome","Firefox","Safari"].includes(e)}())}function lg(){if(xe()||cg()||typeof AudioWorkletNode>"u")return!1;switch(qr()){case"Chrome":case"Firefox":return!0;case"Safari":var n=Ea();return n.major>17||n.major===17&&n.minor>=4}return!1}function Bm(){return og()&&!function(){var n,e=qr();if(!en())return!0;switch(e){case"Chrome":return(n=td()).major&&n.major>0&&n.major<75;case"Firefox":return(n=rd()).major<91;case"Safari":return(n=nd()).major<13||n.major===13&&n.minor<1;default:return!0}}()}function cg(){return en().match(/Linux; Android/)}function $m(){var n,e=en(),i=e.match(/Mac/)&&(!xe()&&typeof window<"u"&&(n=window)!==null&&n!==void 0&&(n=n.navigator)!==null&&n!==void 0&&n.maxTouchPoints?window.navigator.maxTouchPoints:0)>=5;return!!(e.match(/Mobi/)||e.match(/Android/)||i)||!!en().match(/DailyAnd\//)||void 0}function qr(){if(typeof window<"u"){var n=en();return ug()?"Safari":n.indexOf("Edge")>-1?"Edge":n.match(/Chrome\//)?"Chrome":n.indexOf("Safari")>-1||dg()?"Safari":n.indexOf("Firefox")>-1?"Firefox":n.indexOf("MSIE")>-1||n.indexOf(".NET")>-1?"IE":"Unknown Browser"}}function Ea(){switch(qr()){case"Chrome":return td();case"Safari":return nd();case"Firefox":return rd();case"Edge":return function(){var n=0,e=0;if(typeof window<"u"){var i=en().match(/Edge\/(\d+).(\d+)/);if(i)try{n=parseInt(i[1]),e=parseInt(i[2])}catch{}}return{major:n,minor:e}}()}}function td(){var n=0,e=0,i=0,s=0,a=!1;if(typeof window<"u"){var c=en(),d=c.match(/Chrome\/(\d+).(\d+).(\d+).(\d+)/);if(d)try{n=parseInt(d[1]),e=parseInt(d[2]),i=parseInt(d[3]),s=parseInt(d[4]),a=c.indexOf("OPR/")>-1}catch{}}return{major:n,minor:e,build:i,patch:s,opera:a}}function ug(){return!!en().match(/iPad|iPhone|iPod/i)&&og()}function dg(){return en().indexOf("AppleWebKit/605.1.15")>-1}function nd(){var n=0,e=0,i=0;if(typeof window<"u"){var s=en().match(/Version\/(\d+).(\d+)(.(\d+))?/);if(s)try{n=parseInt(s[1]),e=parseInt(s[2]),i=parseInt(s[4])}catch{}else(ug()||dg())&&(n=14,e=0,i=3)}return{major:n,minor:e,point:i}}function rd(){var n=0,e=0;if(typeof window<"u"){var i=en().match(/Firefox\/(\d+).(\d+)/);if(i)try{n=parseInt(i[1]),e=parseInt(i[2])}catch{}}return{major:n,minor:e}}var fg=function(){return zt(function n(){Ut(this,n)},[{key:"addListenerForMessagesFromCallMachine",value:function(n,e,i){ks()}},{key:"addListenerForMessagesFromDailyJs",value:function(n,e,i){ks()}},{key:"sendMessageToCallMachine",value:function(n,e,i,s){ks()}},{key:"sendMessageToDailyJs",value:function(n,e){ks()}},{key:"removeListener",value:function(n){ks()}}])}();function Um(n,e){var i=Object.keys(n);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);e&&(s=s.filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable})),i.push.apply(i,s)}return i}function Zc(n){for(var e=1;e<arguments.length;e++){var i=arguments[e]!=null?arguments[e]:{};e%2?Um(Object(i),!0).forEach(function(s){Mn(n,s,i[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(i)):Um(Object(i)).forEach(function(s){Object.defineProperty(n,s,Object.getOwnPropertyDescriptor(i,s))})}return n}function pg(){try{var n=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(pg=function(){return!!n})()}var ck=function(){function n(){var e,i,s,a;return Ut(this,n),i=this,s=gn(s=n),(e=Na(i,pg()?Reflect.construct(s,[],gn(i).constructor):s.apply(i,a)))._wrappedListeners={},e._messageCallbacks={},e}return ja(n,fg),zt(n,[{key:"addListenerForMessagesFromCallMachine",value:function(e,i,s){var a=this,c=function(d){if(d.data&&d.data.what==="iframe-call-message"&&(!d.data.callClientId||d.data.callClientId===i)&&(!d.data.from||d.data.from!=="module")){var h=Zc({},d.data);if(delete h.from,h.callbackStamp&&a._messageCallbacks[h.callbackStamp]){var m=h.callbackStamp;a._messageCallbacks[m].call(s,h),delete a._messageCallbacks[m]}delete h.what,delete h.callbackStamp,e.call(s,h)}};this._wrappedListeners[e]=c,window.addEventListener("message",c)}},{key:"addListenerForMessagesFromDailyJs",value:function(e,i,s){var a=function(c){var d;if(!(!c.data||c.data.what!==Yc||!c.data.action||c.data.from&&c.data.from!=="module"||c.data.callClientId&&i&&c.data.callClientId!==i||c!=null&&(d=c.data)!==null&&d!==void 0&&d.callFrameId)){var h=c.data;e.call(s,h)}};this._wrappedListeners[e]=a,window.addEventListener("message",a)}},{key:"sendMessageToCallMachine",value:function(e,i,s,a){if(!s)throw new Error("undefined callClientId. Are you trying to use a DailyCall instance previously destroyed?");var c=Zc({},e);if(c.what=Yc,c.from="module",c.callClientId=s,i){var d=Ra();this._messageCallbacks[d]=i,c.callbackStamp=d}var h=a?a.contentWindow:window,m=this._callMachineTargetOrigin(a);m&&h.postMessage(c,m)}},{key:"sendMessageToDailyJs",value:function(e,i){e.what=Yc,e.callClientId=i,e.from="embedded",window.postMessage(e,this._targetOriginFromWindowLocation())}},{key:"removeListener",value:function(e){var i=this._wrappedListeners[e];i&&(window.removeEventListener("message",i),delete this._wrappedListeners[e])}},{key:"forwardPackagedMessageToCallMachine",value:function(e,i,s){var a=Zc({},e);a.callClientId=s;var c=i?i.contentWindow:window,d=this._callMachineTargetOrigin(i);d&&c.postMessage(a,d)}},{key:"addListenerForPackagedMessagesFromCallMachine",value:function(e,i){var s=function(a){if(a.data&&a.data.what==="iframe-call-message"&&(!a.data.callClientId||a.data.callClientId===i)&&(!a.data.from||a.data.from!=="module")){var c=a.data;e(c)}};return this._wrappedListeners[e]=s,window.addEventListener("message",s),e}},{key:"removeListenerForPackagedMessagesFromCallMachine",value:function(e){var i=this._wrappedListeners[e];i&&(window.removeEventListener("message",i),delete this._wrappedListeners[e])}},{key:"_callMachineTargetOrigin",value:function(e){return e?e.src?new URL(e.src).origin:void 0:this._targetOriginFromWindowLocation()}},{key:"_targetOriginFromWindowLocation",value:function(){return window.location.protocol==="file:"?"*":window.location.origin}}])}();function zm(n,e){var i=Object.keys(n);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);e&&(s=s.filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable})),i.push.apply(i,s)}return i}function hg(){try{var n=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(hg=function(){return!!n})()}var uk=function(){function n(){var e,i,s,a;return Ut(this,n),i=this,s=gn(s=n),e=Na(i,hg()?Reflect.construct(s,[],gn(i).constructor):s.apply(i,a)),global.callMachineToDailyJsEmitter=global.callMachineToDailyJsEmitter||new uu.EventEmitter,global.dailyJsToCallMachineEmitter=global.dailyJsToCallMachineEmitter||new uu.EventEmitter,e._wrappedListeners={},e._messageCallbacks={},e}return ja(n,fg),zt(n,[{key:"addListenerForMessagesFromCallMachine",value:function(e,i,s){this._addListener(e,global.callMachineToDailyJsEmitter,i,s,"received call machine message")}},{key:"addListenerForMessagesFromDailyJs",value:function(e,i,s){this._addListener(e,global.dailyJsToCallMachineEmitter,i,s,"received daily-js message")}},{key:"sendMessageToCallMachine",value:function(e,i,s){this._sendMessage(e,global.dailyJsToCallMachineEmitter,s,i,"sending message to call machine")}},{key:"sendMessageToDailyJs",value:function(e,i){this._sendMessage(e,global.callMachineToDailyJsEmitter,i,null,"sending message to daily-js")}},{key:"removeListener",value:function(e){var i=this._wrappedListeners[e];i&&(global.callMachineToDailyJsEmitter.removeListener("message",i),global.dailyJsToCallMachineEmitter.removeListener("message",i),delete this._wrappedListeners[e])}},{key:"_addListener",value:function(e,i,s,a,c){var d=this,h=function(m){if(m.callClientId===s){if(m.callbackStamp&&d._messageCallbacks[m.callbackStamp]){var _=m.callbackStamp;d._messageCallbacks[_].call(a,m),delete d._messageCallbacks[_]}e.call(a,m)}};this._wrappedListeners[e]=h,i.addListener("message",h)}},{key:"_sendMessage",value:function(e,i,s,a,c){var d=function(m){for(var _=1;_<arguments.length;_++){var w=arguments[_]!=null?arguments[_]:{};_%2?zm(Object(w),!0).forEach(function(E){Mn(m,E,w[E])}):Object.getOwnPropertyDescriptors?Object.defineProperties(m,Object.getOwnPropertyDescriptors(w)):zm(Object(w)).forEach(function(E){Object.defineProperty(m,E,Object.getOwnPropertyDescriptor(w,E))})}return m}({},e);if(d.callClientId=s,a){var h=Ra();this._messageCallbacks[h]=a,d.callbackStamp=h}i.emit("message",d)}}])}(),Pu="replace",Ou="shallow-merge",Vm=[Pu,Ou],dk=function(){function n(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},i=e.data,s=e.mergeStrategy,a=s===void 0?Pu:s;Ut(this,n),n._validateMergeStrategy(a),n._validateData(i,a),this.mergeStrategy=a,this.data=i}return zt(n,[{key:"isNoOp",value:function(){return n.isNoOpUpdate(this.data,this.mergeStrategy)}}],[{key:"isNoOpUpdate",value:function(e,i){return Object.keys(e).length===0&&i===Ou}},{key:"_validateMergeStrategy",value:function(e){if(!Vm.includes(e))throw Error("Unrecognized mergeStrategy provided. Options are: [".concat(Vm,"]"))}},{key:"_validateData",value:function(e,i){if(!function(h){if(h==null||Ne(h)!=="object")return!1;var m=Object.getPrototypeOf(h);return m==null||m===Object.prototype}(e))throw Error("Meeting session data must be a plain (map-like) object");var s;try{if(s=JSON.stringify(e),i===Pu){var a=JSON.parse(s);Et(a,e)||console.warn("The meeting session data provided will be modified when serialized.",a,e)}else if(i===Ou){for(var c in e)if(Object.hasOwnProperty.call(e,c)&&e[c]!==void 0){var d=JSON.parse(JSON.stringify(e[c]));Et(e[c],d)||console.warn("At least one key in the meeting session data provided will be modified when serialized.",d,e[c])}}}catch(h){throw Error("Meeting session data must be serializable to JSON: ".concat(h))}if(s.length>jm)throw Error("Meeting session data is too large (".concat(s.length," characters). Maximum size suppported is ").concat(jm,"."))}}])}();function mg(){try{var n=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(mg=function(){return!!n})()}function xu(n){var e=typeof Map=="function"?new Map:void 0;return xu=function(i){if(i===null||!function(a){try{return Function.toString.call(a).indexOf("[native code]")!==-1}catch{return typeof a=="function"}}(i))return i;if(typeof i!="function")throw new TypeError("Super expression must either be null or a function");if(e!==void 0){if(e.has(i))return e.get(i);e.set(i,s)}function s(){return function(a,c,d){if(mg())return Reflect.construct.apply(null,arguments);var h=[null];h.push.apply(h,c);var m=new(a.bind.apply(a,h));return d&&xs(m,d.prototype),m}(i,arguments,gn(this).constructor)}return s.prototype=Object.create(i.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),xs(s,i)},xu(n)}function vg(){try{var n=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(vg=function(){return!!n})()}function Wm(n){var e,i=(e=window._daily)===null||e===void 0?void 0:e.pendings;if(i){var s=i.indexOf(n);s!==-1&&i.splice(s,1)}}var fk=function(){return zt(function n(e){Ut(this,n),this._currentLoad=null,this._callClientId=e},[{key:"load",value:function(){var n,e=this,i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},s=arguments.length>1?arguments[1]:void 0,a=arguments.length>2?arguments[2]:void 0;if(this.loaded)return window._daily.instances[this._callClientId].callMachine.reset(),void s(!0);n=this._callClientId,window._daily.pendings.push(n),this._currentLoad&&this._currentLoad.cancel(),this._currentLoad=new pk(i,function(){s(!1)},function(c,d){d||Wm(e._callClientId),a(c,d)}),this._currentLoad.start()}},{key:"cancel",value:function(){this._currentLoad&&this._currentLoad.cancel(),Wm(this._callClientId)}},{key:"loaded",get:function(){return this._currentLoad&&this._currentLoad.succeeded}}])}(),pk=function(){return zt(function n(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},i=arguments.length>1?arguments[1]:void 0,s=arguments.length>2?arguments[2]:void 0;Ut(this,n),this._attemptsRemaining=3,this._currentAttempt=null,this._dailyConfig=e,this._successCallback=i,this._failureCallback=s},[{key:"start",value:function(){var n=this;if(!this._currentAttempt){var e=function(i){n._currentAttempt.cancelled||(n._attemptsRemaining--,n._failureCallback(i,n._attemptsRemaining>0),n._attemptsRemaining<=0||setTimeout(function(){n._currentAttempt.cancelled||(n._currentAttempt=new qm(n._dailyConfig,n._successCallback,e),n._currentAttempt.start())},3e3))};this._currentAttempt=new qm(this._dailyConfig,this._successCallback,e),this._currentAttempt.start()}}},{key:"cancel",value:function(){this._currentAttempt&&this._currentAttempt.cancel()}},{key:"cancelled",get:function(){return this._currentAttempt&&this._currentAttempt.cancelled}},{key:"succeeded",get:function(){return this._currentAttempt&&this._currentAttempt.succeeded}}])}(),eu=function(){function n(){return Ut(this,n),e=this,s=arguments,i=gn(i=n),Na(e,vg()?Reflect.construct(i,s||[],gn(e).constructor):i.apply(e,s));var e,i,s}return ja(n,xu(Error)),zt(n)}(),Ca=2e4,qm=function(){return zt(function e(i,s,a){Ut(this,e),this._loadAttemptImpl=xe()||!i.avoidEval?new hk(i,s,a):new mk(i,s,a)},[{key:"start",value:(n=Se(function*(){return this._loadAttemptImpl.start()}),function(){return n.apply(this,arguments)})},{key:"cancel",value:function(){this._loadAttemptImpl.cancel()}},{key:"cancelled",get:function(){return this._loadAttemptImpl.cancelled}},{key:"succeeded",get:function(){return this._loadAttemptImpl.succeeded}}]);var n}(),hk=function(){return zt(function a(c,d,h){Ut(this,a),this.cancelled=!1,this.succeeded=!1,this._networkTimedOut=!1,this._networkTimeout=null,this._iosCache=typeof iOSCallObjectBundleCache<"u"&&iOSCallObjectBundleCache,this._refetchHeaders=null,this._dailyConfig=c,this._successCallback=d,this._failureCallback=h},[{key:"start",value:(s=Se(function*(){var a=ya(this._dailyConfig);!(yield this._tryLoadFromIOSCache(a))&&this._loadFromNetwork(a)}),function(){return s.apply(this,arguments)})},{key:"cancel",value:function(){clearTimeout(this._networkTimeout),this.cancelled=!0}},{key:"_tryLoadFromIOSCache",value:(i=Se(function*(a){if(!this._iosCache)return!1;try{var c=yield this._iosCache.get(a);return!!this.cancelled||!!c&&(c.code?(Function('"use strict";'+c.code)(),this.succeeded=!0,this._successCallback(),!0):(this._refetchHeaders=c.refetchHeaders,!1))}catch{return!1}}),function(a){return i.apply(this,arguments)})},{key:"_loadFromNetwork",value:(e=Se(function*(a){var c=this;this._networkTimeout=setTimeout(function(){c._networkTimedOut=!0,c._failureCallback({msg:"Timed out (>".concat(Ca," ms) when loading call object bundle ").concat(a),type:"timeout"})},Ca);try{var d=this._refetchHeaders?{headers:this._refetchHeaders}:{},h=yield fetch(a,d);if(clearTimeout(this._networkTimeout),this.cancelled||this._networkTimedOut)throw new eu;var m=yield this._getBundleCodeFromResponse(a,h);if(this.cancelled)throw new eu;Function('"use strict";'+m)(),this._iosCache&&this._iosCache.set(a,m,h.headers),this.succeeded=!0,this._successCallback()}catch(_){if(clearTimeout(this._networkTimeout),_ instanceof eu||this.cancelled||this._networkTimedOut)return;this._failureCallback({msg:"Failed to load call object bundle ".concat(a,": ").concat(_),type:_.message})}}),function(a){return e.apply(this,arguments)})},{key:"_getBundleCodeFromResponse",value:(n=Se(function*(a,c){if(c.ok)return yield c.text();if(this._iosCache&&c.status===304)return(yield this._iosCache.renew(a,c.headers)).code;throw new Error("Received ".concat(c.status," response"))}),function(a,c){return n.apply(this,arguments)})}]);var n,e,i,s}(),mk=function(){return zt(function n(e,i,s){Ut(this,n),this.cancelled=!1,this.succeeded=!1,this._dailyConfig=e,this._successCallback=i,this._failureCallback=s,this._attemptId=Ra(),this._networkTimeout=null,this._scriptElement=null},[{key:"start",value:function(){window._dailyCallMachineLoadWaitlist||(window._dailyCallMachineLoadWaitlist=new Set);var n=ya(this._dailyConfig);(typeof document>"u"?"undefined":Ne(document))==="object"?this._startLoading(n):this._failureCallback({msg:"Call object bundle must be loaded in a DOM/web context",type:"missing context"})}},{key:"cancel",value:function(){this._stopLoading(),this.cancelled=!0}},{key:"_startLoading",value:function(n){var e=this;this._signUpForCallMachineLoadWaitlist(),this._networkTimeout=setTimeout(function(){e._stopLoading(),e._failureCallback({msg:"Timed out (>".concat(Ca," ms) when loading call object bundle ").concat(n),type:"timeout"})},Ca);var i=document.getElementsByTagName("head")[0],s=document.createElement("script");this._scriptElement=s,s.onload=function(){e._stopLoading(),e.succeeded=!0,e._successCallback()},s.onerror=function(a){e._stopLoading(),e._failureCallback({msg:"Failed to load call object bundle ".concat(a.target.src),type:a.message})},s.src=n,i.appendChild(s)}},{key:"_stopLoading",value:function(){this._withdrawFromCallMachineLoadWaitlist(),clearTimeout(this._networkTimeout),this._scriptElement&&(this._scriptElement.onload=null,this._scriptElement.onerror=null)}},{key:"_signUpForCallMachineLoadWaitlist",value:function(){window._dailyCallMachineLoadWaitlist.add(this._attemptId)}},{key:"_withdrawFromCallMachineLoadWaitlist",value:function(){window._dailyCallMachineLoadWaitlist.delete(this._attemptId)}}])}(),la=function(n,e,i){return gk(n.local,e,i)===!0},vk=function(n,e,i){return n.local.streams&&n.local.streams[e]&&n.local.streams[e].stream&&n.local.streams[e].stream["get".concat(i==="video"?"Video":"Audio","Tracks")]()[0]},Si=function(n,e,i,s){var a=yk(n,e,i,s);return a&&a.pendingTrack},gk=function(n,e,i){if(!n)return!1;var s=function(c){switch(c){case"avatar":return!0;case"staged":return c;default:return!!c}},a=n.public.subscribedTracks;return a&&a[e]?["cam-audio","cam-video","screen-video","screen-audio","rmpAudio","rmpVideo"].indexOf(i)===-1&&a[e].custom?[!0,"staged"].includes(a[e].custom)?s(a[e].custom):s(a[e].custom[i]):s(a[e][i]):!a||s(a.ALL)},yk=function(n,e,i,s){var a=Object.values(n.streams||{}).filter(function(c){return c.participantId===e&&c.type===i&&c.pendingTrack&&c.pendingTrack.kind===s}).sort(function(c,d){return new Date(d.starttime)-new Date(c.starttime)});return a&&a[0]},_k=function(n,e){var i=n.local.public.customTracks;if(i&&i[e])return i[e].track};function Jm(n,e){for(var i=e.getState(),s=0,a=["cam","screen"];s<a.length;s++)for(var c=a[s],d=0,h=["video","audio"];d<h.length;d++){var m=h[d],_=c==="cam"?m:"screen".concat(m.charAt(0).toUpperCase()+m.slice(1)),w=n.tracks[_];if(w){var E=n.local?vk(i,c,m):Si(i,n.session_id,c,m);w.state==="playable"&&(w.track=E),w.persistentTrack=E}}}function Hm(n,e){try{var i=e.getState();for(var s in n.tracks)if(!wk(s)){var a=n.tracks[s].kind;if(a){var c=n.tracks[s];if(c){var d=n.local?_k(i,s):Si(i,n.session_id,s,a);c.state==="playable"&&(n.tracks[s].track=d),c.persistentTrack=d}}else console.error("unknown type for custom track")}}catch(h){console.error(h)}}function wk(n){return["video","audio","screenVideo","screenAudio"].includes(n)}function Qm(n,e,i){var s=i.getState();if(n.local){if(n.audio)try{n.audioTrack=s.local.streams.cam.stream.getAudioTracks()[0],n.audioTrack||(n.audio=!1)}catch{}if(n.video)try{n.videoTrack=s.local.streams.cam.stream.getVideoTracks()[0],n.videoTrack||(n.video=!1)}catch{}if(n.screen)try{n.screenVideoTrack=s.local.streams.screen.stream.getVideoTracks()[0],n.screenAudioTrack=s.local.streams.screen.stream.getAudioTracks()[0],n.screenVideoTrack||n.screenAudioTrack||(n.screen=!1)}catch{}}else{var a=!0;try{var c=s.participants[n.session_id];c&&c.public&&c.public.rtcType&&c.public.rtcType.impl==="peer-to-peer"&&c.private&&!["connected","completed"].includes(c.private.peeringState)&&(a=!1)}catch(w){console.error(w)}if(!a)return n.audio=!1,n.audioTrack=!1,n.video=!1,n.videoTrack=!1,n.screen=!1,void(n.screenTrack=!1);try{if(s.streams,n.audio&&la(s,n.session_id,"cam-audio")){var d=Si(s,n.session_id,"cam","audio");d&&(e&&e.audioTrack&&e.audioTrack.id===d.id?n.audioTrack=d:d.muted||(n.audioTrack=d)),n.audioTrack||(n.audio=!1)}if(n.video&&la(s,n.session_id,"cam-video")){var h=Si(s,n.session_id,"cam","video");h&&(e&&e.videoTrack&&e.videoTrack.id===h.id?n.videoTrack=h:h.muted||(n.videoTrack=h)),n.videoTrack||(n.video=!1)}if(n.screen&&la(s,n.session_id,"screen-audio")){var m=Si(s,n.session_id,"screen","audio");m&&(e&&e.screenAudioTrack&&e.screenAudioTrack.id===m.id?n.screenAudioTrack=m:m.muted||(n.screenAudioTrack=m))}if(n.screen&&la(s,n.session_id,"screen-video")){var _=Si(s,n.session_id,"screen","video");_&&(e&&e.screenVideoTrack&&e.screenVideoTrack.id===_.id?n.screenVideoTrack=_:_.muted||(n.screenVideoTrack=_))}n.screenVideoTrack||n.screenAudioTrack||(n.screen=!1)}catch(w){console.error("unexpected error matching up tracks",w)}}}function Sk(n,e){var i=typeof Symbol<"u"&&n[Symbol.iterator]||n["@@iterator"];if(!i){if(Array.isArray(n)||(i=function(m,_){if(m){if(typeof m=="string")return Gm(m,_);var w={}.toString.call(m).slice(8,-1);return w==="Object"&&m.constructor&&(w=m.constructor.name),w==="Map"||w==="Set"?Array.from(m):w==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(w)?Gm(m,_):void 0}}(n))||e){i&&(n=i);var s=0,a=function(){};return{s:a,n:function(){return s>=n.length?{done:!0}:{done:!1,value:n[s++]}},e:function(m){throw m},f:a}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var c,d=!0,h=!1;return{s:function(){i=i.call(n)},n:function(){var m=i.next();return d=m.done,m},e:function(m){h=!0,c=m},f:function(){try{d||i.return==null||i.return()}finally{if(h)throw c}}}}function Gm(n,e){(e==null||e>n.length)&&(e=n.length);for(var i=0,s=Array(e);i<e;i++)s[i]=n[i];return s}var En=new Map,Ei=null;function kk(n,e){var i=typeof Symbol<"u"&&n[Symbol.iterator]||n["@@iterator"];if(!i){if(Array.isArray(n)||(i=function(m,_){if(m){if(typeof m=="string")return Km(m,_);var w={}.toString.call(m).slice(8,-1);return w==="Object"&&m.constructor&&(w=m.constructor.name),w==="Map"||w==="Set"?Array.from(m):w==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(w)?Km(m,_):void 0}}(n))||e){i&&(n=i);var s=0,a=function(){};return{s:a,n:function(){return s>=n.length?{done:!0}:{done:!1,value:n[s++]}},e:function(m){throw m},f:a}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var c,d=!0,h=!1;return{s:function(){i=i.call(n)},n:function(){var m=i.next();return d=m.done,m},e:function(m){h=!0,c=m},f:function(){try{d||i.return==null||i.return()}finally{if(h)throw c}}}}function Km(n,e){(e==null||e>n.length)&&(e=n.length);for(var i=0,s=Array(e);i<e;i++)s[i]=n[i];return s}var Cn=new Map,Ps=null;function bk(n){gg()?function(e){En.has(e)||(En.set(e,{}),navigator.mediaDevices.enumerateDevices().then(function(i){En.has(e)&&(En.get(e).lastDevicesString=JSON.stringify(i),Ei||(Ei=function(){var s=Se(function*(){var a,c=yield navigator.mediaDevices.enumerateDevices(),d=Sk(En.keys());try{for(d.s();!(a=d.n()).done;){var h=a.value,m=JSON.stringify(c);m!==En.get(h).lastDevicesString&&(En.get(h).lastDevicesString=m,h(c))}}catch(_){d.e(_)}finally{d.f()}});return function(){return s.apply(this,arguments)}}(),navigator.mediaDevices.addEventListener("devicechange",Ei)))}).catch(function(){}))}(n):function(e){Cn.has(e)||(Cn.set(e,{}),navigator.mediaDevices.enumerateDevices().then(function(i){Cn.has(e)&&(Cn.get(e).lastDevicesString=JSON.stringify(i),Ps||(Ps=setInterval(Se(function*(){var s,a=yield navigator.mediaDevices.enumerateDevices(),c=kk(Cn.keys());try{for(c.s();!(s=c.n()).done;){var d=s.value,h=JSON.stringify(a);h!==Cn.get(d).lastDevicesString&&(Cn.get(d).lastDevicesString=h,d(a))}}catch(m){c.e(m)}finally{c.f()}}),3e3)))}))}(n)}function Ek(n){gg()?function(e){En.has(e)&&(En.delete(e),En.size===0&&Ei&&(navigator.mediaDevices.removeEventListener("devicechange",Ei),Ei=null))}(n):function(e){Cn.has(e)&&(Cn.delete(e),Cn.size===0&&Ps&&(clearInterval(Ps),Ps=null))}(n)}function gg(){var n;return xe()||((n=navigator.mediaDevices)===null||n===void 0?void 0:n.ondevicechange)!==void 0}var Ck=new Set;function Mk(n,e){return n&&n.readyState==="live"&&!function(i,s){return i.muted&&!Ck.has(i.id)}(n)}function Ym(n,e){var i=Object.keys(n);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);e&&(s=s.filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable})),i.push.apply(i,s)}return i}function cr(n){for(var e=1;e<arguments.length;e++){var i=arguments[e]!=null?arguments[e]:{};e%2?Ym(Object(i),!0).forEach(function(s){Mn(n,s,i[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(i)):Ym(Object(i)).forEach(function(s){Object.defineProperty(n,s,Object.getOwnPropertyDescriptor(i,s))})}return n}var Ts=Object.freeze({VIDEO:"video",AUDIO:"audio",SCREEN_VIDEO:"screenVideo",SCREEN_AUDIO:"screenAudio",CUSTOM_VIDEO:"customVideo",CUSTOM_AUDIO:"customAudio"}),Tk=Object.freeze({PARTICIPANTS:"participants",STREAMING:"streaming",TRANSCRIPTION:"transcription"}),Os=Object.values(Ts),Xm=["v","a","sv","sa","cv","ca"];Object.freeze(Os.reduce(function(n,e,i){return n[e]=Xm[i],n},{})),Object.freeze(Xm.reduce(function(n,e,i){return n[e]=Os[i],n},{}));var Pk=[Ts.VIDEO,Ts.AUDIO,Ts.SCREEN_VIDEO,Ts.SCREEN_AUDIO],Zm=Object.values(Tk),ev=["p","s","t"];Object.freeze(Zm.reduce(function(n,e,i){return n[e]=ev[i],n},{})),Object.freeze(ev.reduce(function(n,e,i){return n[e]=Zm[i],n},{}));var Ok=function(){function n(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},i=e.base,s=e.byUserId,a=e.byParticipantId;Ut(this,n),this.base=i,this.byUserId=s,this.byParticipantId=a}return zt(n,[{key:"clone",value:function(){var e=new n;if(this.base instanceof ft?e.base=this.base.clone():e.base=this.base,this.byUserId!==void 0)for(var i in e.byUserId={},this.byUserId){var s=this.byUserId[i];e.byUserId[i]=s instanceof ft?s.clone():s}if(this.byParticipantId!==void 0)for(var a in e.byParticipantId={},this.byParticipantId){var c=this.byParticipantId[a];e.byParticipantId[a]=c instanceof ft?c.clone():c}return e}},{key:"toJSONObject",value:function(){var e={};if(typeof this.base=="boolean"?e.base=this.base:this.base instanceof ft&&(e.base=this.base.toJSONObject()),this.byUserId!==void 0)for(var i in e.byUserId={},this.byUserId){var s=this.byUserId[i];e.byUserId[i]=s instanceof ft?s.toJSONObject():s}if(this.byParticipantId!==void 0)for(var a in e.byParticipantId={},this.byParticipantId){var c=this.byParticipantId[a];e.byParticipantId[a]=c instanceof ft?c.toJSONObject():c}return e}},{key:"toMinifiedJSONObject",value:function(){var e={};if(this.base!==void 0&&(typeof this.base=="boolean"?e.b=this.base:e.b=this.base.toMinifiedJSONObject()),this.byUserId!==void 0)for(var i in e.u={},this.byUserId){var s=this.byUserId[i];e.u[i]=typeof s=="boolean"?s:s.toMinifiedJSONObject()}if(this.byParticipantId!==void 0)for(var a in e.p={},this.byParticipantId){var c=this.byParticipantId[a];e.p[a]=typeof c=="boolean"?c:c.toMinifiedJSONObject()}return e}},{key:"normalize",value:function(){return this.base instanceof ft&&(this.base=this.base.normalize()),this.byUserId&&(this.byUserId=Object.fromEntries(Object.entries(this.byUserId).map(function(e){var i=Mt(e,2),s=i[0],a=i[1];return[s,a instanceof ft?a.normalize():a]}))),this.byParticipantId&&(this.byParticipantId=Object.fromEntries(Object.entries(this.byParticipantId).map(function(e){var i=Mt(e,2),s=i[0],a=i[1];return[s,a instanceof ft?a.normalize():a]}))),this}}],[{key:"fromJSONObject",value:function(e){var i,s,a;if(e.base!==void 0&&(i=typeof e.base=="boolean"?e.base:ft.fromJSONObject(e.base)),e.byUserId!==void 0)for(var c in s={},e.byUserId){var d=e.byUserId[c];s[c]=typeof d=="boolean"?d:ft.fromJSONObject(d)}if(e.byParticipantId!==void 0)for(var h in a={},e.byParticipantId){var m=e.byParticipantId[h];a[h]=typeof m=="boolean"?m:ft.fromJSONObject(m)}return new n({base:i,byUserId:s,byParticipantId:a})}},{key:"fromMinifiedJSONObject",value:function(e){var i,s,a;if(e.b!==void 0&&(i=typeof e.b=="boolean"?e.b:ft.fromMinifiedJSONObject(e.b)),e.u!==void 0)for(var c in s={},e.u){var d=e.u[c];s[c]=typeof d=="boolean"?d:ft.fromMinifiedJSONObject(d)}if(e.p!==void 0)for(var h in a={},e.p){var m=e.p[h];a[h]=typeof m=="boolean"?m:ft.fromMinifiedJSONObject(m)}return new n({base:i,byUserId:s,byParticipantId:a})}},{key:"validateJSONObject",value:function(e){if(Ne(e)!=="object")return[!1,"canReceive must be an object"];for(var i=["base","byUserId","byParticipantId"],s=0,a=Object.keys(e);s<a.length;s++){var c=a[s];if(!i.includes(c))return[!1,"canReceive can only contain keys (".concat(i.join(", "),")")];if(c==="base"){var d=Mt(ft.validateJSONObject(e.base,!0),2),h=d[0],m=d[1];if(!h)return[!1,m]}else{if(Ne(e[c])!=="object")return[!1,"invalid (non-object) value for field '".concat(c,"' in canReceive")];for(var _=0,w=Object.values(e[c]);_<w.length;_++){var E=w[_],x=Mt(ft.validateJSONObject(E),2),T=x[0],P=x[1];if(!T)return[!1,P]}}}return[!0]}}])}(),ft=function(){function n(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},i=e.video,s=e.audio,a=e.screenVideo,c=e.screenAudio,d=e.customVideo,h=e.customAudio;Ut(this,n),this.video=i,this.audio=s,this.screenVideo=a,this.screenAudio=c,this.customVideo=d,this.customAudio=h}return zt(n,[{key:"clone",value:function(){var e=new n;return this.video!==void 0&&(e.video=this.video),this.audio!==void 0&&(e.audio=this.audio),this.screenVideo!==void 0&&(e.screenVideo=this.screenVideo),this.screenAudio!==void 0&&(e.screenAudio=this.screenAudio),this.customVideo!==void 0&&(e.customVideo=cr({},this.customVideo)),this.customAudio!==void 0&&(e.customAudio=cr({},this.customAudio)),e}},{key:"toJSONObject",value:function(){var e={};return this.video!==void 0&&(e.video=this.video),this.audio!==void 0&&(e.audio=this.audio),this.screenVideo!==void 0&&(e.screenVideo=this.screenVideo),this.screenAudio!==void 0&&(e.screenAudio=this.screenAudio),this.customVideo!==void 0&&(e.customVideo=cr({},this.customVideo)),this.customAudio!==void 0&&(e.customAudio=cr({},this.customAudio)),e}},{key:"toMinifiedJSONObject",value:function(){var e={};return this.video!==void 0&&(e.v=this.video),this.audio!==void 0&&(e.a=this.audio),this.screenVideo!==void 0&&(e.sv=this.screenVideo),this.screenAudio!==void 0&&(e.sa=this.screenAudio),this.customVideo!==void 0&&(e.cv=cr({},this.customVideo)),this.customAudio!==void 0&&(e.ca=cr({},this.customAudio)),e}},{key:"normalize",value:function(){function e(i,s){return i&&Object.keys(i).length===1&&i["*"]===s}return!(this.video!==!0||this.audio!==!0||this.screenVideo!==!0||this.screenAudio!==!0||!e(this.customVideo,!0)||!e(this.customAudio,!0))||(this.video!==!1||this.audio!==!1||this.screenVideo!==!1||this.screenAudio!==!1||!e(this.customVideo,!1)||!e(this.customAudio,!1))&&this}}],[{key:"fromBoolean",value:function(e){return new n({video:e,audio:e,screenVideo:e,screenAudio:e,customVideo:{"*":e},customAudio:{"*":e}})}},{key:"fromJSONObject",value:function(e){return new n({video:e.video,audio:e.audio,screenVideo:e.screenVideo,screenAudio:e.screenAudio,customVideo:e.customVideo!==void 0?cr({},e.customVideo):void 0,customAudio:e.customAudio!==void 0?cr({},e.customAudio):void 0})}},{key:"fromMinifiedJSONObject",value:function(e){return new n({video:e.v,audio:e.a,screenVideo:e.sv,screenAudio:e.sa,customVideo:e.cv,customAudio:e.ca})}},{key:"validateJSONObject",value:function(e,i){if(typeof e=="boolean")return[!0];if(Ne(e)!=="object")return[!1,"invalid (non-object, non-boolean) value in canReceive"];for(var s=Object.keys(e),a=0,c=s;a<c.length;a++){var d=c[a];if(!Os.includes(d))return[!1,"invalid media type '".concat(d,"' in canReceive")];if(Pk.includes(d)){if(typeof e[d]!="boolean")return[!1,"invalid (non-boolean) value for media type '".concat(d,"' in canReceive")]}else{if(Ne(e[d])!=="object")return[!1,"invalid (non-object) value for media type '".concat(d,"' in canReceive")];for(var h=0,m=Object.values(e[d]);h<m.length;h++)if(typeof m[h]!="boolean")return[!1,"invalid (non-boolean) value for entry within '".concat(d,"' in canReceive")];if(i&&e[d]["*"]===void 0)return[!1,`canReceive "base" permission must specify "*" as an entry within '`.concat(d,"'")]}}return i&&s.length!==Os.length?[!1,'canReceive "base" permission must specify all media types: '.concat(Os.join(", ")," (or be set to a boolean shorthand)")]:[!0]}}])}(),xk=["result"],Ak=["preserveIframe"];function tv(n,e){var i=Object.keys(n);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);e&&(s=s.filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable})),i.push.apply(i,s)}return i}function me(n){for(var e=1;e<arguments.length;e++){var i=arguments[e]!=null?arguments[e]:{};e%2?tv(Object(i),!0).forEach(function(s){Mn(n,s,i[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(i)):tv(Object(i)).forEach(function(s){Object.defineProperty(n,s,Object.getOwnPropertyDescriptor(i,s))})}return n}function yg(){try{var n=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(yg=function(){return!!n})()}function nv(n,e){var i=typeof Symbol<"u"&&n[Symbol.iterator]||n["@@iterator"];if(!i){if(Array.isArray(n)||(i=function(m,_){if(m){if(typeof m=="string")return rv(m,_);var w={}.toString.call(m).slice(8,-1);return w==="Object"&&m.constructor&&(w=m.constructor.name),w==="Map"||w==="Set"?Array.from(m):w==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(w)?rv(m,_):void 0}}(n))||e){i&&(n=i);var s=0,a=function(){};return{s:a,n:function(){return s>=n.length?{done:!0}:{done:!1,value:n[s++]}},e:function(m){throw m},f:a}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var c,d=!0,h=!1;return{s:function(){i=i.call(n)},n:function(){var m=i.next();return d=m.done,m},e:function(m){h=!0,c=m},f:function(){try{d||i.return==null||i.return()}finally{if(h)throw c}}}}function rv(n,e){(e==null||e>n.length)&&(e=n.length);for(var i=0,s=Array(e);i<e;i++)s[i]=n[i];return s}var jr={},iv="video",Lk="voice",sv=xe()?{data:{}}:{data:{},topology:"none"},ov={present:0,hidden:0},_g={maxBitrate:{min:1e5,max:25e5},maxFramerate:{min:1,max:30},scaleResolutionDownBy:{min:1,max:8}},tu=Object.keys(_g),av=["state","volume","simulcastEncodings"],lv={androidInCallNotification:{title:"string",subtitle:"string",iconName:"string",disableForCustomOverride:"boolean"},disableAutoDeviceManagement:{audio:"boolean",video:"boolean"}},Ma={id:{iconPath:"string",iconPathDarkMode:"string",label:"string",tooltip:"string",visualState:"'default' | 'sidebar-open' | 'active'"}},Au={id:{allow:"string",controlledBy:"'*' | 'owners' | string[]",csp:"string",iconURL:"string",label:"string",loading:"'eager' | 'lazy'",location:"'main' | 'sidebar'",name:"string",referrerPolicy:"string",sandbox:"string",src:"string",srcdoc:"string",shared:"string[] | 'owners' | boolean"}},Rr={customIntegrations:{validate:Tg,help:Cg()},customTrayButtons:{validate:Mg,help:"customTrayButtons should be a dictionary of the type ".concat(JSON.stringify(Ma))},url:{validate:function(n){return typeof n=="string"},help:"url should be a string"},baseUrl:{validate:function(n){return typeof n=="string"},help:"baseUrl should be a string"},token:{validate:function(n){return typeof n=="string"},help:"token should be a string",queryString:"t"},dailyConfig:{validate:function(n,e){try{return e.validateDailyConfig(n),!0}catch(i){console.error("Failed to validate dailyConfig",i)}return!1},help:"Unsupported dailyConfig. Check error logs for detailed info."},reactNativeConfig:{validate:function(n){return Pg(n,lv)},help:"reactNativeConfig should look like ".concat(JSON.stringify(lv),", all fields optional")},lang:{validate:function(n){return["da","de","en-us","en","es","fi","fr","it","jp","ka","nl","no","pl","pt","pt-BR","ru","sv","tr","user"].includes(n)},help:"language not supported. Options are: da, de, en-us, en, es, fi, fr, it, jp, ka, nl, no, pl, pt, pt-BR, ru, sv, tr, user"},userName:!0,userData:{validate:function(n){try{return wg(n),!0}catch(e){return console.error(e),!1}},help:"invalid userData type provided"},startVideoOff:!0,startAudioOff:!0,allowLocalVideo:!0,allowLocalAudio:!0,activeSpeakerMode:!0,showLeaveButton:!0,showLocalVideo:!0,showParticipantsBar:!0,showFullscreenButton:!0,showUserNameChangeUI:!0,iframeStyle:!0,customLayout:!0,cssFile:!0,cssText:!0,bodyClass:!0,videoSource:{validate:function(n,e){if(typeof n=="boolean")return e._preloadCache.allowLocalVideo=n,!0;var i;if(n instanceof MediaStreamTrack)e._sharedTracks.videoTrack=n,i={customTrack:Bn};else{if(delete e._sharedTracks.videoTrack,typeof n!="string")return console.error("videoSource must be a MediaStreamTrack, boolean, or a string"),!1;i={deviceId:n}}return e._updatePreloadCacheInputSettings({video:{settings:i}},!1),!0}},audioSource:{validate:function(n,e){if(typeof n=="boolean")return e._preloadCache.allowLocalAudio=n,!0;var i;if(n instanceof MediaStreamTrack)e._sharedTracks.audioTrack=n,i={customTrack:Bn};else{if(delete e._sharedTracks.audioTrack,typeof n!="string")return console.error("audioSource must be a MediaStreamTrack, boolean, or a string"),!1;i={deviceId:n}}return e._updatePreloadCacheInputSettings({audio:{settings:i}},!1),!0}},subscribeToTracksAutomatically:{validate:function(n,e){return e._preloadCache.subscribeToTracksAutomatically=n,!0}},theme:{validate:function(n){var e=["accent","accentText","background","backgroundAccent","baseText","border","mainAreaBg","mainAreaBgAccent","mainAreaText","supportiveText"],i=function(s){for(var a=0,c=Object.keys(s);a<c.length;a++){var d=c[a];if(!e.includes(d))return console.error('unsupported color "'.concat(d,'". Valid colors: ').concat(e.join(", "))),!1;if(!s[d].match(/^#[0-9a-f]{6}|#[0-9a-f]{3}$/i))return console.error("".concat(d,' theme color should be provided in valid hex color format. Received: "').concat(s[d],'"')),!1}return!0};return Ne(n)==="object"&&("light"in n&&"dark"in n||"colors"in n)?"light"in n&&"dark"in n?"colors"in n.light?"colors"in n.dark?i(n.light.colors)&&i(n.dark.colors):(console.error('Dark theme is missing "colors" property.',n),!1):(console.error('Light theme is missing "colors" property.',n),!1):i(n.colors):(console.error('Theme must contain either both "light" and "dark" properties, or "colors".',n),!1)},help:"unsupported theme configuration. Check error logs for detailed info."},layoutConfig:{validate:function(n){if("grid"in n){var e=n.grid;if("maxTilesPerPage"in e){if(!Number.isInteger(e.maxTilesPerPage))return console.error("grid.maxTilesPerPage should be an integer. You passed ".concat(e.maxTilesPerPage,".")),!1;if(e.maxTilesPerPage>49)return console.error("grid.maxTilesPerPage can't be larger than 49 without sacrificing browser performance. Please contact us at https://www.daily.co/contact to talk about your use case."),!1}if("minTilesPerPage"in e){if(!Number.isInteger(e.minTilesPerPage))return console.error("grid.minTilesPerPage should be an integer. You passed ".concat(e.minTilesPerPage,".")),!1;if(e.minTilesPerPage<1)return console.error("grid.minTilesPerPage can't be lower than 1."),!1;if("maxTilesPerPage"in e&&e.minTilesPerPage>e.maxTilesPerPage)return console.error("grid.minTilesPerPage can't be higher than grid.maxTilesPerPage."),!1}}return!0},help:"unsupported layoutConfig. Check error logs for detailed info."},receiveSettings:{validate:function(n){return Sg(n,{allowAllParticipantsKey:!1})},help:Eg({allowAllParticipantsKey:!1})},sendSettings:{validate:function(n,e){return!!function(i,s){try{return s.validateUpdateSendSettings(i),!0}catch(a){return console.error("Failed to validate send settings",a),!1}}(n,e)&&(e._preloadCache.sendSettings=n,!0)},help:"Invalid sendSettings provided. Check error logs for detailed info."},inputSettings:{validate:function(n,e){var i;return!!kg(n)&&(e._inputSettings||(e._inputSettings={}),bg(n,(i=e.properties)===null||i===void 0?void 0:i.dailyConfig,e._sharedTracks),e._updatePreloadCacheInputSettings(n,!0),!0)},help:Lu()},layout:{validate:function(n){return n==="custom-v1"||n==="browser"||n==="none"},help:'layout may only be set to "custom-v1"',queryString:"layout"},emb:{queryString:"emb"},embHref:{queryString:"embHref"},dailyJsVersion:{queryString:"dailyJsVersion"},proxy:{queryString:"proxy"},strictMode:!0,allowMultipleCallInstances:!0},ca={styles:{validate:function(n){for(var e in n)if(e!=="cam"&&e!=="screen")return!1;if(n.cam){for(var i in n.cam)if(i!=="div"&&i!=="video")return!1}if(n.screen){for(var s in n.screen)if(s!=="div"&&s!=="video")return!1}return!0},help:"styles format should be a subset of: { cam: {div: {}, video: {}}, screen: {div: {}, video: {}} }"},setSubscribedTracks:{validate:function(n,e){if(e._preloadCache.subscribeToTracksAutomatically)return!1;var i=[!0,!1,"staged"];if(i.includes(n)||!xe()&&n==="avatar")return!0;var s=["audio","video","screenAudio","screenVideo","rmpAudio","rmpVideo"],a=function(c){var d=arguments.length>1&&arguments[1]!==void 0&&arguments[1];for(var h in c)if(h==="custom"){if(!i.includes(c[h])&&!a(c[h],!0))return!1}else{var m=!d&&!s.includes(h),_=!i.includes(c[h]);if(m||_)return!1}return!0};return a(n)},help:"setSubscribedTracks cannot be used when setSubscribeToTracksAutomatically is enabled, and should be of the form: "+"true".concat(xe()?"":" | 'avatar'"," | false | 'staged' | { [audio: true|false|'staged'], [video: true|false|'staged'], [screenAudio: true|false|'staged'], [screenVideo: true|false|'staged'] }")},setAudio:!0,setVideo:!0,setScreenShare:{validate:function(n){return n===!1},help:"setScreenShare must be false, as it's only meant for stopping remote participants' screen shares"},eject:!0,updatePermissions:{validate:function(n){for(var e=0,i=Object.entries(n);e<i.length;e++){var s=Mt(i[e],2),a=s[0],c=s[1];switch(a){case"hasPresence":if(typeof c!="boolean")return!1;break;case"canSend":if(c instanceof Set||c instanceof Array||Array.isArray(c)){var d,h=["video","audio","screenVideo","screenAudio","customVideo","customAudio"],m=nv(c);try{for(m.s();!(d=m.n()).done;){var _=d.value;if(!h.includes(_))return!1}}catch(Y){m.e(Y)}finally{m.f()}}else if(typeof c!="boolean")return!1;(c instanceof Array||Array.isArray(c))&&(n.canSend=new Set(c));break;case"canReceive":var w=Mt(Ok.validateJSONObject(c),2),E=w[0],x=w[1];if(!E)return console.error(x),!1;break;case"canAdmin":if(c instanceof Set||c instanceof Array||Array.isArray(c)){var T,P=["participants","streaming","transcription"],L=nv(c);try{for(L.s();!(T=L.n()).done;){var F=T.value;if(!P.includes(F))return!1}}catch(Y){L.e(Y)}finally{L.f()}}else if(typeof c!="boolean")return!1;(c instanceof Array||Array.isArray(c))&&(n.canAdmin=new Set(c));break;default:return!1}}return!0},help:"updatePermissions can take hasPresence, canSend, canReceive, and canAdmin permissions. hasPresence must be a boolean. canSend can be a boolean or an Array or Set of media types (video, audio, screenVideo, screenAudio, customVideo, customAudio). canReceive must be an object specifying base, byUserId, and/or byParticipantId fields (see documentation for more details). canAdmin can be a boolean or an Array or Set of admin types (participants, streaming, transcription)."}};Promise.any||(Promise.any=function(){var n=Se(function*(e){return new Promise(function(i,s){var a=[];e.forEach(function(c){return Promise.resolve(c).then(function(d){i(d)}).catch(function(d){a.push(d),a.length===e.length&&s(a)})})})});return function(e){return n.apply(this,arguments)}}());var Nk=function(){function n(u){var g,v,S,k,N,J,K=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(Ut(this,n),S=this,k=gn(k=n),Mn(v=Na(S,yg()?Reflect.construct(k,[],gn(S).constructor):k.apply(S,N)),"startListeningForDeviceChanges",function(){bk(v.handleDeviceChange)}),Mn(v,"stopListeningForDeviceChanges",function(){Ek(v.handleDeviceChange)}),Mn(v,"handleDeviceChange",function(Ee){Ee=Ee.map(function(Ce){return JSON.parse(JSON.stringify(Ce))}),v.emitDailyJSEvent({action:"available-devices-updated",availableDevices:Ee})}),Mn(v,"handleNativeAppStateChange",function(){var Ee=Se(function*(Ce){if(Ce==="destroyed")return console.warn("App has been destroyed before leaving the meeting. Cleaning up all the resources!"),void(yield v.destroy());var Tt=Ce==="active";v.disableReactNativeAutoDeviceManagement("video")||(Tt?v.camUnmutedBeforeLosingNativeActiveState&&v.setLocalVideo(!0):(v.camUnmutedBeforeLosingNativeActiveState=v.localVideo(),v.camUnmutedBeforeLosingNativeActiveState&&v.setLocalVideo(!1)))});return function(Ce){return Ee.apply(this,arguments)}}()),Mn(v,"handleNativeAudioFocusChange",function(Ee){v.disableReactNativeAutoDeviceManagement("audio")||(v._hasNativeAudioFocus=Ee,v.toggleParticipantAudioBasedOnNativeAudioFocus(),v._hasNativeAudioFocus?v.micUnmutedBeforeLosingNativeAudioFocus&&v.setLocalAudio(!0):(v.micUnmutedBeforeLosingNativeAudioFocus=v.localAudio(),v.setLocalAudio(!1)))}),Mn(v,"handleNativeSystemScreenCaptureStop",function(){v.stopScreenShare()}),v.strictMode=K.strictMode===void 0||K.strictMode,v.allowMultipleCallInstances=(g=K.allowMultipleCallInstances)!==null&&g!==void 0&&g,Object.keys(jr).length&&(v._logDuplicateInstanceAttempt(),!v.allowMultipleCallInstances)){if(v.strictMode)throw new Error("Duplicate DailyIframe instances are not allowed");console.warn("Using strictMode: false to allow multiple call instances is now deprecated. Set `allowMultipleCallInstances: true`")}if(window._daily||(window._daily={pendings:[],instances:{}}),v.callClientId=Ra(),jr[(J=v).callClientId]=J,window._daily.instances[v.callClientId]={},v._sharedTracks={},window._daily.instances[v.callClientId].tracks=v._sharedTracks,K.dailyJsVersion=n.version(),v._iframe=u,v._callObjectMode=K.layout==="none"&&!v._iframe,v._preloadCache={subscribeToTracksAutomatically:!0,outputDeviceId:null,inputSettings:null,sendSettings:null,videoTrackForNetworkConnectivityTest:null,videoTrackForConnectionQualityTest:null},K.showLocalVideo!==void 0?v._callObjectMode?console.error("showLocalVideo is not available in call object mode"):v._showLocalVideo=!!K.showLocalVideo:v._showLocalVideo=!0,K.showParticipantsBar!==void 0?v._callObjectMode?console.error("showParticipantsBar is not available in call object mode"):v._showParticipantsBar=!!K.showParticipantsBar:v._showParticipantsBar=!0,K.customIntegrations!==void 0?v._callObjectMode?console.error("customIntegrations is not available in call object mode"):v._customIntegrations=K.customIntegrations:v._customIntegrations={},K.customTrayButtons!==void 0?v._callObjectMode?console.error("customTrayButtons is not available in call object mode"):v._customTrayButtons=K.customTrayButtons:v._customTrayButtons={},K.activeSpeakerMode!==void 0?v._callObjectMode?console.error("activeSpeakerMode is not available in call object mode"):v._activeSpeakerMode=!!K.activeSpeakerMode:v._activeSpeakerMode=!1,K.receiveSettings?v._callObjectMode?v._receiveSettings=K.receiveSettings:console.error("receiveSettings is only available in call object mode"):v._receiveSettings={},v.validateProperties(K),v.properties=me({},K),v._inputSettings||(v._inputSettings={}),v._callObjectLoader=v._callObjectMode?new fk(v.callClientId):null,v._callState=Em,v._isPreparingToJoin=!1,v._accessState={access:Gc},v._meetingSessionSummary={},v._finalSummaryOfPrevSession={},v._meetingSessionState=iu(sv,v._callObjectMode),v._nativeInCallAudioMode=iv,v._participants={},v._isScreenSharing=!1,v._participantCounts=ov,v._rmpPlayerState={},v._waitingParticipants={},v._network={threshold:"good",quality:100,networkState:"unknown",stats:{}},v._activeSpeaker={},v._localAudioLevel=0,v._isLocalAudioLevelObserverRunning=!1,v._remoteParticipantsAudioLevel={},v._isRemoteParticipantsAudioLevelObserverRunning=!1,v._maxAppMessageSize=Kc,v._messageChannel=xe()?new uk:new ck,v._iframe&&(v._iframe.requestFullscreen?v._iframe.addEventListener("fullscreenchange",function(){document.fullscreenElement===v._iframe?(v.emitDailyJSEvent({action:sa}),v.sendMessageToCallMachine({action:sa})):(v.emitDailyJSEvent({action:oa}),v.sendMessageToCallMachine({action:oa}))}):v._iframe.webkitRequestFullscreen&&v._iframe.addEventListener("webkitfullscreenchange",function(){document.webkitFullscreenElement===v._iframe?(v.emitDailyJSEvent({action:sa}),v.sendMessageToCallMachine({action:sa})):(v.emitDailyJSEvent({action:oa}),v.sendMessageToCallMachine({action:oa}))})),xe()){var ce=v.nativeUtils();ce.addAudioFocusChangeListener&&ce.removeAudioFocusChangeListener&&ce.addAppStateChangeListener&&ce.removeAppStateChangeListener&&ce.addSystemScreenCaptureStopListener&&ce.removeSystemScreenCaptureStopListener||console.warn("expected (add|remove)(AudioFocusChange|AppActiveStateChange|SystemScreenCaptureStop)Listener to be available in React Native"),v._hasNativeAudioFocus=!0,ce.addAudioFocusChangeListener(v.handleNativeAudioFocusChange),ce.addAppStateChangeListener(v.handleNativeAppStateChange),ce.addSystemScreenCaptureStopListener(v.handleNativeSystemScreenCaptureStop)}return v._callObjectMode&&v.startListeningForDeviceChanges(),v._messageChannel.addListenerForMessagesFromCallMachine(v.handleMessageFromCallMachine,v.callClientId,v),v}return ja(n,Yo),zt(n,[{key:"destroy",value:(V=Se(function*(){var u;try{yield this.leave()}catch{}var g=this._iframe;if(g){var v=g.parentElement;v&&v.removeChild(g)}if(this._messageChannel.removeListener(this.handleMessageFromCallMachine),xe()){var S=this.nativeUtils();S.removeAudioFocusChangeListener(this.handleNativeAudioFocusChange),S.removeAppStateChangeListener(this.handleNativeAppStateChange),S.removeSystemScreenCaptureStopListener(this.handleNativeSystemScreenCaptureStop)}this._callObjectMode&&this.stopListeningForDeviceChanges(),this.resetMeetingDependentVars(),this._destroyed=!0,this.emitDailyJSEvent({action:"call-instance-destroyed"}),delete jr[this.callClientId],!((u=window)===null||u===void 0||(u=u._daily)===null||u===void 0)&&u.instances&&delete window._daily.instances[this.callClientId],this.strictMode&&(this.callClientId=void 0)}),function(){return V.apply(this,arguments)})},{key:"isDestroyed",value:function(){return!!this._destroyed}},{key:"loadCss",value:function(u){var g=u.bodyClass,v=u.cssFile,S=u.cssText;return Te(),this.sendMessageToCallMachine({action:"load-css",cssFile:this.absoluteUrl(v),bodyClass:g,cssText:S}),this}},{key:"iframe",value:function(){return Te(),this._iframe}},{key:"meetingState",value:function(){return this._callState}},{key:"accessState",value:function(){return fn(this._callObjectMode,"accessState()"),this._accessState}},{key:"participants",value:function(){return this._participants}},{key:"participantCounts",value:function(){return this._participantCounts}},{key:"waitingParticipants",value:function(){return fn(this._callObjectMode,"waitingParticipants()"),this._waitingParticipants}},{key:"validateParticipantProperties",value:function(u,g){for(var v in g){if(!ca[v])throw new Error("unrecognized updateParticipant property ".concat(v));if(ca[v].validate&&!ca[v].validate(g[v],this,this._participants[u]))throw new Error(ca[v].help)}}},{key:"updateParticipant",value:function(u,g){return this._participants.local&&this._participants.local.session_id===u&&(u="local"),u&&g&&(this.validateParticipantProperties(u,g),this.sendMessageToCallMachine({action:"update-participant",id:u,properties:g})),this}},{key:"updateParticipants",value:function(u){var g=this._participants.local&&this._participants.local.session_id;for(var v in u)v===g&&(v="local"),v&&u[v]&&this.validateParticipantProperties(v,u[v]);return this.sendMessageToCallMachine({action:"update-participants",participants:u}),this}},{key:"updateWaitingParticipant",value:(O=Se(function*(){var u=this,g=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",v=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(fn(this._callObjectMode,"updateWaitingParticipant()"),ze(this._callState,"updateWaitingParticipant()"),typeof g!="string"||Ne(v)!=="object")throw new Error("updateWaitingParticipant() must take an id string and a updates object");return new Promise(function(S,k){u.sendMessageToCallMachine({action:"daily-method-update-waiting-participant",id:g,updates:v},function(N){N.error&&k(N.error),N.id||k(new Error("unknown error in updateWaitingParticipant()")),S({id:N.id})})})}),function(){return O.apply(this,arguments)})},{key:"updateWaitingParticipants",value:(ie=Se(function*(){var u=this,g=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(fn(this._callObjectMode,"updateWaitingParticipants()"),ze(this._callState,"updateWaitingParticipants()"),Ne(g)!=="object")throw new Error("updateWaitingParticipants() must take a mapping between ids and update objects");return new Promise(function(v,S){u.sendMessageToCallMachine({action:"daily-method-update-waiting-participants",updatesById:g},function(k){k.error&&S(k.error),k.ids||S(new Error("unknown error in updateWaitingParticipants()")),v({ids:k.ids})})})}),function(){return ie.apply(this,arguments)})},{key:"requestAccess",value:(ae=Se(function*(){var u=this,g=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},v=g.access,S=v===void 0?{level:oS}:v,k=g.name,N=k===void 0?"":k;return fn(this._callObjectMode,"requestAccess()"),ze(this._callState,"requestAccess()"),new Promise(function(J,K){u.sendMessageToCallMachine({action:"daily-method-request-access",access:S,name:N},function(ce){ce.error&&K(ce.error),ce.access||K(new Error("unknown error in requestAccess()")),J({access:ce.access,granted:ce.granted})})})}),function(){return ae.apply(this,arguments)})},{key:"localAudio",value:function(){return this._participants.local?!["blocked","off"].includes(this._participants.local.tracks.audio.state):null}},{key:"localVideo",value:function(){return this._participants.local?!["blocked","off"].includes(this._participants.local.tracks.video.state):null}},{key:"setLocalAudio",value:function(u){var g=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return"forceDiscardTrack"in g&&(xe()?(console.warn("forceDiscardTrack option not supported in React Native; ignoring"),g={}):u&&(console.warn("forceDiscardTrack option only supported when calling setLocalAudio(false); ignoring"),g={})),this.sendMessageToCallMachine({action:"local-audio",state:u,options:g}),this}},{key:"localScreenAudio",value:function(){return this._participants.local?!["blocked","off"].includes(this._participants.local.tracks.screenAudio.state):null}},{key:"localScreenVideo",value:function(){return this._participants.local?!["blocked","off"].includes(this._participants.local.tracks.screenVideo.state):null}},{key:"updateScreenShare",value:function(u){if(this._isScreenSharing)return this.sendMessageToCallMachine({action:"local-screen-update",options:u}),this;console.warn("There is no screen share in progress. Try calling startScreenShare first.")}},{key:"setLocalVideo",value:function(u){return this.sendMessageToCallMachine({action:"local-video",state:u}),this}},{key:"_setAllowLocalAudio",value:function(u){if(this._preloadCache.allowLocalAudio=u,this._callMachineInitialized)return this.sendMessageToCallMachine({action:"set-allow-local-audio",state:u}),this}},{key:"_setAllowLocalVideo",value:function(u){if(this._preloadCache.allowLocalVideo=u,this._callMachineInitialized)return this.sendMessageToCallMachine({action:"set-allow-local-video",state:u}),this}},{key:"getReceiveSettings",value:(Z=Se(function*(u){var g=this,v=(arguments.length>1&&arguments[1]!==void 0?arguments[1]:{}).showInheritedValues,S=v!==void 0&&v;if(fn(this._callObjectMode,"getReceiveSettings()"),!this._callMachineInitialized)return this._receiveSettings;switch(Ne(u)){case"string":return new Promise(function(k){g.sendMessageToCallMachine({action:"get-single-participant-receive-settings",id:u,showInheritedValues:S},function(N){k(N.receiveSettings)})});case"undefined":return this._receiveSettings;default:throw new Error('first argument to getReceiveSettings() must be a participant id (or "base"), or there should be no arguments')}}),function(u){return Z.apply(this,arguments)})},{key:"updateReceiveSettings",value:(he=Se(function*(u){var g=this;if(fn(this._callObjectMode,"updateReceiveSettings()"),!Sg(u,{allowAllParticipantsKey:!0}))throw new Error(Eg({allowAllParticipantsKey:!0}));return ze(this._callState,"updateReceiveSettings()","To specify receive settings earlier, use the receiveSettings config property."),new Promise(function(v){g.sendMessageToCallMachine({action:"update-receive-settings",receiveSettings:u},function(S){v({receiveSettings:S.receiveSettings})})})}),function(u){return he.apply(this,arguments)})},{key:"_prepInputSettingsForSharing",value:function(u,g){if(u){var v={};if(u.audio){var S,k,N;u.audio.settings&&(!Object.keys(u.audio.settings).length&&g||(v.audio={settings:me({},u.audio.settings)})),g&&(S=v.audio)!==null&&S!==void 0&&(S=S.settings)!==null&&S!==void 0&&S.customTrack&&(v.audio.settings={customTrack:this._sharedTracks.audioTrack});var J=((k=u.audio.processor)===null||k===void 0?void 0:k.type)==="none"&&((N=u.audio.processor)===null||N===void 0?void 0:N._isDefaultWhenNone);if(u.audio.processor&&!J){var K=me({},u.audio.processor);delete K._isDefaultWhenNone,v.audio=me(me({},v.audio),{},{processor:K})}}if(u.video){var ce,Ee,Ce;u.video.settings&&(!Object.keys(u.video.settings).length&&g||(v.video={settings:me({},u.video.settings)})),g&&(ce=v.video)!==null&&ce!==void 0&&(ce=ce.settings)!==null&&ce!==void 0&&ce.customTrack&&(v.video.settings={customTrack:this._sharedTracks.videoTrack});var Tt=((Ee=u.video.processor)===null||Ee===void 0?void 0:Ee.type)==="none"&&((Ce=u.video.processor)===null||Ce===void 0?void 0:Ce._isDefaultWhenNone);if(u.video.processor&&!Tt){var yn=me({},u.video.processor);delete yn._isDefaultWhenNone,v.video=me(me({},v.video),{},{processor:yn})}}return v}}},{key:"getInputSettings",value:function(){var u=this;return Te(),new Promise(function(g){g(u._getInputSettings())})}},{key:"_getInputSettings",value:function(){var u,g,v,S,k,N,J={processor:{type:"none",_isDefaultWhenNone:!0}};this._inputSettings?(u=((v=this._inputSettings)===null||v===void 0?void 0:v.video)||J,g=((S=this._inputSettings)===null||S===void 0?void 0:S.audio)||J):(u=((k=this._preloadCache)===null||k===void 0||(k=k.inputSettings)===null||k===void 0?void 0:k.video)||J,g=((N=this._preloadCache)===null||N===void 0||(N=N.inputSettings)===null||N===void 0?void 0:N.audio)||J);var K={audio:g,video:u};return this._prepInputSettingsForSharing(K,!0)}},{key:"_updatePreloadCacheInputSettings",value:function(u,g){var v=this._inputSettings||{},S={};if(u.video){var k,N,J;S.video={},u.video.settings?(S.video.settings={},g||u.video.settings.customTrack||(J=v.video)===null||J===void 0||!J.settings?S.video.settings=u.video.settings:S.video.settings=me(me({},v.video.settings),u.video.settings),Object.keys(S.video.settings).length||delete S.video.settings):(k=v.video)!==null&&k!==void 0&&k.settings&&(S.video.settings=v.video.settings),u.video.processor?S.video.processor=u.video.processor:(N=v.video)!==null&&N!==void 0&&N.processor&&(S.video.processor=v.video.processor)}else v.video&&(S.video=v.video);if(u.audio){var K,ce,Ee;S.audio={},u.audio.settings?(S.audio.settings={},g||u.audio.settings.customTrack||(Ee=v.audio)===null||Ee===void 0||!Ee.settings?S.audio.settings=u.audio.settings:S.audio.settings=me(me({},v.audio.settings),u.audio.settings),Object.keys(S.audio.settings).length||delete S.audio.settings):(K=v.audio)!==null&&K!==void 0&&K.settings&&(S.audio.settings=v.audio.settings),u.audio.processor?S.audio.processor=u.audio.processor:(ce=v.audio)!==null&&ce!==void 0&&ce.processor&&(S.audio.processor=v.audio.processor)}else v.audio&&(S.audio=v.audio);this._maybeUpdateInputSettings(S)}},{key:"_devicesFromInputSettings",value:function(u){var g,v,S=(u==null||(g=u.video)===null||g===void 0||(g=g.settings)===null||g===void 0?void 0:g.deviceId)||null,k=(u==null||(v=u.audio)===null||v===void 0||(v=v.settings)===null||v===void 0?void 0:v.deviceId)||null,N=this._preloadCache.outputDeviceId||null;return{camera:S?{deviceId:S}:{},mic:k?{deviceId:k}:{},speaker:N?{deviceId:N}:{}}}},{key:"updateInputSettings",value:(Re=Se(function*(u){var g=this;return Te(),kg(u)?u.video||u.audio?(bg(u,this.properties.dailyConfig,this._sharedTracks),this._callObjectMode&&!this._callMachineInitialized?(this._updatePreloadCacheInputSettings(u,!0),this._getInputSettings()):new Promise(function(v,S){g.sendMessageToCallMachine({action:"update-input-settings",inputSettings:u},function(k){if(k.error)S(k.error);else{if(k.returnPreloadCache)return g._updatePreloadCacheInputSettings(u,!0),void v(g._getInputSettings());g._maybeUpdateInputSettings(k.inputSettings),v(g._prepInputSettingsForSharing(k.inputSettings,!0))}})})):this._getInputSettings():(console.error(Lu()),Promise.reject(Lu()))}),function(u){return Re.apply(this,arguments)})},{key:"setBandwidth",value:function(u){var g=u.kbs,v=u.trackConstraints;if(Te(),this._callMachineInitialized)return this.sendMessageToCallMachine({action:"set-bandwidth",kbs:g,trackConstraints:v}),this}},{key:"getDailyLang",value:function(){var u=this;if(Te(),this._callMachineInitialized)return new Promise(function(g){u.sendMessageToCallMachine({action:"get-daily-lang"},function(v){delete v.action,delete v.callbackStamp,g(v)})})}},{key:"setDailyLang",value:function(u){return Te(),this.sendMessageToCallMachine({action:"set-daily-lang",lang:u}),this}},{key:"setProxyUrl",value:function(u){return this.sendMessageToCallMachine({action:"set-proxy-url",proxyUrl:u}),this}},{key:"setIceConfig",value:function(u){return this.sendMessageToCallMachine({action:"set-ice-config",iceConfig:u}),this}},{key:"meetingSessionSummary",value:function(){return[Nr,lr].includes(this._callState)?this._finalSummaryOfPrevSession:this._meetingSessionSummary}},{key:"getMeetingSession",value:(Oe=Se(function*(){var u=this;return console.warn("getMeetingSession() is deprecated: use meetingSessionSummary(), which will return immediately"),ze(this._callState,"getMeetingSession()"),new Promise(function(g){u.sendMessageToCallMachine({action:"get-meeting-session"},function(v){delete v.action,delete v.callbackStamp,g(v)})})}),function(){return Oe.apply(this,arguments)})},{key:"meetingSessionState",value:function(){return ze(this._callState,"meetingSessionState"),this._meetingSessionState}},{key:"setMeetingSessionData",value:function(u){var g=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"replace";fn(this._callObjectMode,"setMeetingSessionData()"),ze(this._callState,"setMeetingSessionData");try{(function(v,S){new dk({data:v,mergeStrategy:S})})(u,g)}catch(v){throw console.error(v),v}try{this.sendMessageToCallMachine({action:"set-session-data",data:u,mergeStrategy:g})}catch(v){throw new Error("Error setting meeting session data: ".concat(v))}}},{key:"setUserName",value:function(u,g){var v=this;return this.properties.userName=u,new Promise(function(S){v.sendMessageToCallMachine({action:"set-user-name",name:u??"",thisMeetingOnly:xe()||!!g&&!!g.thisMeetingOnly},function(k){delete k.action,delete k.callbackStamp,S(k)})})}},{key:"setUserData",value:(_e=Se(function*(u){var g=this;try{wg(u)}catch(v){throw console.error(v),v}if(this.properties.userData=u,this._callMachineInitialized)return new Promise(function(v){try{g.sendMessageToCallMachine({action:"set-user-data",userData:u},function(S){delete S.action,delete S.callbackStamp,v(S)})}catch(S){throw new Error("Error setting user data: ".concat(S))}})}),function(u){return _e.apply(this,arguments)})},{key:"validateAudioLevelInterval",value:function(u){if(u&&(u<100||typeof u!="number"))throw new Error("The interval must be a number greater than or equal to 100 milliseconds.")}},{key:"startLocalAudioLevelObserver",value:function(u){var g=this;if(typeof AudioWorkletNode>"u"&&!xe())throw new Error("startLocalAudioLevelObserver() is not supported on this browser");if(this.validateAudioLevelInterval(u),this._callMachineInitialized)return this._isLocalAudioLevelObserverRunning=!0,new Promise(function(v,S){g.sendMessageToCallMachine({action:"start-local-audio-level-observer",interval:u},function(k){g._isLocalAudioLevelObserverRunning=!k.error,k.error?S({error:k.error}):v()})});this._preloadCache.localAudioLevelObserver={enabled:!0,interval:u}}},{key:"isLocalAudioLevelObserverRunning",value:function(){return this._isLocalAudioLevelObserverRunning}},{key:"stopLocalAudioLevelObserver",value:function(){this._preloadCache.localAudioLevelObserver=null,this._localAudioLevel=0,this._isLocalAudioLevelObserverRunning=!1,this.sendMessageToCallMachine({action:"stop-local-audio-level-observer"})}},{key:"startRemoteParticipantsAudioLevelObserver",value:function(u){var g=this;if(this.validateAudioLevelInterval(u),this._callMachineInitialized)return this._isRemoteParticipantsAudioLevelObserverRunning=!0,new Promise(function(v,S){g.sendMessageToCallMachine({action:"start-remote-participants-audio-level-observer",interval:u},function(k){g._isRemoteParticipantsAudioLevelObserverRunning=!k.error,k.error?S({error:k.error}):v()})});this._preloadCache.remoteParticipantsAudioLevelObserver={enabled:!0,interval:u}}},{key:"isRemoteParticipantsAudioLevelObserverRunning",value:function(){return this._isRemoteParticipantsAudioLevelObserverRunning}},{key:"stopRemoteParticipantsAudioLevelObserver",value:function(){this._preloadCache.remoteParticipantsAudioLevelObserver=null,this._remoteParticipantsAudioLevel={},this._isRemoteParticipantsAudioLevelObserverRunning=!1,this.sendMessageToCallMachine({action:"stop-remote-participants-audio-level-observer"})}},{key:"startCamera",value:(pe=Se(function*(){var u=this,g=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(fn(this._callObjectMode,"startCamera()"),nu(this._callState,this._isPreparingToJoin,"startCamera()","Did you mean to use setLocalAudio() and/or setLocalVideo() instead?"),this.needsLoad())try{yield this.load(g)}catch(v){return Promise.reject(v)}else{if(this._didPreAuth){if(g.url&&g.url!==this.properties.url)return console.error("url in startCamera() is different than the one used in preAuth()"),Promise.reject();if(g.token&&g.token!==this.properties.token)return console.error("token in startCamera() is different than the one used in preAuth()"),Promise.reject()}this.validateProperties(g),this.properties=me(me({},this.properties),g)}return new Promise(function(v){u._preloadCache.inputSettings=u._prepInputSettingsForSharing(u._inputSettings,!1),u.sendMessageToCallMachine({action:"start-camera",properties:_i(u.properties,u.callClientId),preloadCache:_i(u._preloadCache,u.callClientId)},function(S){v({camera:S.camera,mic:S.mic,speaker:S.speaker})})})}),function(){return pe.apply(this,arguments)})},{key:"validateCustomTrack",value:function(u,g,v){if(v&&v.length>50)throw new Error("Custom track `trackName` must not be more than 50 characters");if(g&&g!=="music"&&g!=="speech"&&!(g instanceof Object))throw new Error("Custom track `mode` must be either `music` | `speech` | `DailyMicAudioModeSettings` or `undefined`");if(v&&["cam-audio","cam-video","screen-video","screen-audio","rmpAudio","rmpVideo","customVideoDefaults"].includes(v))throw new Error("Custom track `trackName` must not match a track name already used by daily: cam-audio, cam-video, customVideoDefaults, screen-video, screen-audio, rmpAudio, rmpVideo");if(!(u instanceof MediaStreamTrack))throw new Error("Custom tracks provided must be instances of MediaStreamTrack")}},{key:"startCustomTrack",value:function(){var u=this,g=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{track,mode,trackName};return Te(),ze(this._callState,"startCustomTrack()"),this.validateCustomTrack(g.track,g.mode,g.trackName),new Promise(function(v,S){u._sharedTracks.customTrack=g.track,g.track=Bn,u.sendMessageToCallMachine({action:"start-custom-track",properties:g},function(k){k.error?S({error:k.error}):v(k.mediaTag)})})}},{key:"stopCustomTrack",value:function(u){var g=this;return Te(),ze(this._callState,"stopCustomTrack()"),new Promise(function(v){g.sendMessageToCallMachine({action:"stop-custom-track",mediaTag:u},function(S){v(S.mediaTag)})})}},{key:"setCamera",value:function(u){var g=this;return bs(),ua(this._callMachineInitialized,"setCamera()"),new Promise(function(v){g.sendMessageToCallMachine({action:"set-camera",cameraDeviceId:u},function(S){v({device:S.device})})})}},{key:"setAudioDevice",value:(se=Se(function*(u){return bs(),this.nativeUtils().setAudioDevice(u),{deviceId:yield this.nativeUtils().getAudioDevice()}}),function(u){return se.apply(this,arguments)})},{key:"cycleCamera",value:function(){var u=this,g=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return new Promise(function(v){u.sendMessageToCallMachine({action:"cycle-camera",properties:g},function(S){v({device:S.device})})})}},{key:"cycleMic",value:function(){var u=this;return Te(),new Promise(function(g){u.sendMessageToCallMachine({action:"cycle-mic"},function(v){g({device:v.device})})})}},{key:"getCameraFacingMode",value:function(){var u=this;return bs(),new Promise(function(g){u.sendMessageToCallMachine({action:"get-camera-facing-mode"},function(v){g(v.facingMode)})})}},{key:"setInputDevicesAsync",value:(te=Se(function*(u){var g=this,v=u.audioDeviceId,S=u.videoDeviceId,k=u.audioSource,N=u.videoSource;if(Te(),k!==void 0&&(v=k),N!==void 0&&(S=N),typeof v=="boolean"&&(this._setAllowLocalAudio(v),v=void 0),typeof S=="boolean"&&(this._setAllowLocalVideo(S),S=void 0),!v&&!S)return yield this.getInputDevices();var J={};return v&&(v instanceof MediaStreamTrack?(this._sharedTracks.audioTrack=v,v=Bn,J.audio={settings:{customTrack:v}}):(delete this._sharedTracks.audioTrack,J.audio={settings:{deviceId:v}})),S&&(S instanceof MediaStreamTrack?(this._sharedTracks.videoTrack=S,S=Bn,J.video={settings:{customTrack:S}}):(delete this._sharedTracks.videoTrack,J.video={settings:{deviceId:S}})),this._callObjectMode&&this.needsLoad()?(this._updatePreloadCacheInputSettings(J,!1),this._devicesFromInputSettings(this._inputSettings)):new Promise(function(K){g.sendMessageToCallMachine({action:"set-input-devices",audioDeviceId:v,videoDeviceId:S},function(ce){if(delete ce.action,delete ce.callbackStamp,ce.returnPreloadCache)return g._updatePreloadCacheInputSettings(J,!1),void K(g._devicesFromInputSettings(g._inputSettings));K(ce)})})}),function(u){return te.apply(this,arguments)})},{key:"setOutputDeviceAsync",value:($=Se(function*(u){var g=this,v=u.outputDeviceId;return Te(),v&&(this._preloadCache.outputDeviceId=v),this._callObjectMode&&this.needsLoad()?this._devicesFromInputSettings(this._inputSettings):new Promise(function(S){g.sendMessageToCallMachine({action:"set-output-device",outputDeviceId:v},function(k){delete k.action,delete k.callbackStamp,k.returnPreloadCache?S(g._devicesFromInputSettings(g._inputSettings)):S(k)})})}),function(u){return $.apply(this,arguments)})},{key:"getInputDevices",value:(X=Se(function*(){var u=this;return this._callObjectMode&&this.needsLoad()?this._devicesFromInputSettings(this._inputSettings):new Promise(function(g){u.sendMessageToCallMachine({action:"get-input-devices"},function(v){v.returnPreloadCache?g(u._devicesFromInputSettings(u._inputSettings)):g({camera:v.camera,mic:v.mic,speaker:v.speaker})})})}),function(){return X.apply(this,arguments)})},{key:"nativeInCallAudioMode",value:function(){return bs(),this._nativeInCallAudioMode}},{key:"setNativeInCallAudioMode",value:function(u){if(bs(),[iv,Lk].includes(u)){if(u!==this._nativeInCallAudioMode)return this._nativeInCallAudioMode=u,!this.disableReactNativeAutoDeviceManagement("audio")&&ma(this._callState,this._isPreparingToJoin)&&this.nativeUtils().setAudioMode(this._nativeInCallAudioMode),this}else console.error("invalid in-call audio mode specified: ",u)}},{key:"preAuth",value:(z=Se(function*(){var u=this,g=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(fn(this._callObjectMode,"preAuth()"),nu(this._callState,this._isPreparingToJoin,"preAuth()"),this.needsLoad()&&(yield this.load(g)),!g.url)throw new Error("preAuth() requires at least a url to be provided");return this.validateProperties(g),this.properties=me(me({},this.properties),g),new Promise(function(v,S){u._preloadCache.inputSettings=u._prepInputSettingsForSharing(u._inputSettings,!1),u.sendMessageToCallMachine({action:"daily-method-preauth",properties:_i(u.properties,u.callClientId),preloadCache:_i(u._preloadCache,u.callClientId)},function(k){return k.error?S(k.error):k.access?(u._didPreAuth=!0,void v({access:k.access})):S(new Error("unknown error in preAuth()"))})})}),function(){return z.apply(this,arguments)})},{key:"load",value:(R=Se(function*(u){var g=this;if(this.needsLoad()){if(this._destroyed&&(this._logUseAfterDestroy(),this.strictMode))throw new Error("Use after destroy");if(u&&(this.validateProperties(u),this.properties=me(me({},this.properties),u)),!this._callObjectMode&&!this.properties.url)throw new Error("can't load iframe meeting because url property isn't set");return this._updateCallState(Cm),this.emitDailyJSEvent({action:pS}),this._callObjectMode?new Promise(function(v,S){g._callObjectLoader.cancel();var k=Date.now();g._callObjectLoader.load(g.properties.dailyConfig,function(N){g._bundleLoadTime=N?"no-op":Date.now()-k,g._updateCallState(Mm),N&&g.emitDailyJSEvent({action:Pm}),v()},function(N,J){if(g.emitDailyJSEvent({action:hS}),!J){g._updateCallState(lr),g.resetMeetingDependentVars();var K={action:Nm,errorMsg:N.msg,error:{type:"connection-error",msg:"Failed to load call object bundle.",details:{on:"load",sourceError:N,bundleUrl:ya(g.properties.dailyConfig)}}};g._maybeSendToSentry(K),g.emitDailyJSEvent(K),S(N.msg)}})}):(this._iframe.src=Vv(this.assembleMeetingUrl(),this.properties.dailyConfig),new Promise(function(v,S){g._loadedCallback=function(k){g._callState!==lr?(g._updateCallState(Mm),(g.properties.cssFile||g.properties.cssText)&&g.loadCss(g.properties),v()):S(k)}}))}}),function(u){return R.apply(this,arguments)})},{key:"join",value:(U=Se(function*(){var u=this,g=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this._testCallInProgress&&this.stopTestCallQuality();var v=!1;if(this.needsLoad()){this.updateIsPreparingToJoin(!0);try{yield this.load(g)}catch(S){return this.updateIsPreparingToJoin(!1),Promise.reject(S)}}else{if(v=!(!this.properties.cssFile&&!this.properties.cssText),this._didPreAuth){if(g.url&&g.url!==this.properties.url)return console.error("url in join() is different than the one used in preAuth()"),this.updateIsPreparingToJoin(!1),Promise.reject();if(g.token&&g.token!==this.properties.token)return console.error("token in join() is different than the one used in preAuth()"),this.updateIsPreparingToJoin(!1),Promise.reject()}if(g.url&&!this._callObjectMode&&g.url&&g.url!==this.properties.url)return console.error("url in join() is different than the one used in load() (".concat(this.properties.url," -> ").concat(g.url,")")),this.updateIsPreparingToJoin(!1),Promise.reject();this.validateProperties(g),this.properties=me(me({},this.properties),g)}return g.showLocalVideo!==void 0&&(this._callObjectMode?console.error("showLocalVideo is not available in callObject mode"):this._showLocalVideo=!!g.showLocalVideo),g.showParticipantsBar!==void 0&&(this._callObjectMode?console.error("showParticipantsBar is not available in callObject mode"):this._showParticipantsBar=!!g.showParticipantsBar),this._callState===Ir||this._callState===Mu?(console.warn("already joined meeting, call leave() before joining again"),void this.updateIsPreparingToJoin(!1)):(this._updateCallState(Mu,!1),this.emitDailyJSEvent({action:gS}),this._preloadCache.inputSettings=this._prepInputSettingsForSharing(this._inputSettings||{},!1),this.sendMessageToCallMachine({action:"join-meeting",properties:_i(this.properties,this.callClientId),preloadCache:_i(this._preloadCache,this.callClientId)}),new Promise(function(S,k){u._joinedCallback=function(N,J){if(u._callState!==lr){if(u._updateCallState(Ir),N)for(var K in N){if(u._callObjectMode){var ce=u._callMachine().store;Jm(N[K],ce),Hm(N[K],ce),Qm(N[K],u._participants[K],ce)}u._participants[K]=me({},N[K]),u.toggleParticipantAudioBasedOnNativeAudioFocus()}v&&u.loadCss(u.properties),S(N)}else k(J)}}))}),function(){return U.apply(this,arguments)})},{key:"leave",value:(I=Se(function*(){var u=this;return this._testCallInProgress&&this.stopTestCallQuality(),new Promise(function(g){u._callState===Nr||u._callState===lr?g():u._callObjectLoader&&!u._callObjectLoader.loaded?(u._callObjectLoader.cancel(),u._updateCallState(Nr),u.resetMeetingDependentVars(),u.emitDailyJSEvent({action:Nr}),g()):(u._resolveLeave=g,u.sendMessageToCallMachine({action:"leave-meeting"}))})}),function(){return I.apply(this,arguments)})},{key:"startScreenShare",value:(B=Se(function*(){var u=this,g=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(ua(this._callMachineInitialized,"startScreenShare()"),g.screenVideoSendSettings&&this._validateVideoSendSettings("screenVideo",g.screenVideoSendSettings),g.mediaStream&&(this._sharedTracks.screenMediaStream=g.mediaStream,g.mediaStream=Bn),typeof DailyNativeUtils<"u"&&DailyNativeUtils.isIOS!==void 0&&DailyNativeUtils.isIOS){var v=this.nativeUtils();if(yield v.isScreenBeingCaptured())return void this.emitDailyJSEvent({action:Lm,type:"screen-share-error",errorMsg:"Could not start the screen sharing. The screen is already been captured!"});v.setSystemScreenCaptureStartCallback(function(){v.setSystemScreenCaptureStartCallback(null),u.sendMessageToCallMachine({action:Rm,captureOptions:g})}),v.presentSystemScreenCapturePrompt()}else this.sendMessageToCallMachine({action:Rm,captureOptions:g})}),function(){return B.apply(this,arguments)})},{key:"stopScreenShare",value:function(){ua(this._callMachineInitialized,"stopScreenShare()"),this.sendMessageToCallMachine({action:"local-screen-stop"})}},{key:"startRecording",value:function(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},g=u.type;if(g&&g!=="cloud"&&g!=="raw-tracks"&&g!=="local")throw new Error("invalid type: ".concat(g,", allowed values 'cloud', 'raw-tracks', or 'local'"));this.sendMessageToCallMachine(me({action:"local-recording-start"},u))}},{key:"updateRecording",value:function(u){var g=u.layout,v=g===void 0?{preset:"default"}:g,S=u.instanceId;this.sendMessageToCallMachine({action:"daily-method-update-recording",layout:v,instanceId:S})}},{key:"stopRecording",value:function(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.sendMessageToCallMachine(me({action:"local-recording-stop"},u))}},{key:"startLiveStreaming",value:function(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.sendMessageToCallMachine(me({action:"daily-method-start-live-streaming"},u))}},{key:"updateLiveStreaming",value:function(u){var g=u.layout,v=g===void 0?{preset:"default"}:g,S=u.instanceId;this.sendMessageToCallMachine({action:"daily-method-update-live-streaming",layout:v,instanceId:S})}},{key:"addLiveStreamingEndpoints",value:function(u){var g=u.endpoints,v=u.instanceId;this.sendMessageToCallMachine({action:Dm,endpointsOp:sk,endpoints:g,instanceId:v})}},{key:"removeLiveStreamingEndpoints",value:function(u){var g=u.endpoints,v=u.instanceId;this.sendMessageToCallMachine({action:Dm,endpointsOp:ok,endpoints:g,instanceId:v})}},{key:"stopLiveStreaming",value:function(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.sendMessageToCallMachine(me({action:"daily-method-stop-live-streaming"},u))}},{key:"validateDailyConfig",value:function(u){u.camSimulcastEncodings&&(console.warn("camSimulcastEncodings is deprecated. Use sendSettings, found in DailyCallOptions, to provide camera simulcast settings."),this.validateSimulcastEncodings(u.camSimulcastEncodings)),u.screenSimulcastEncodings&&console.warn("screenSimulcastEncodings is deprecated. Use sendSettings, found in DailyCallOptions, to provide screen simulcast settings."),cg()&&u.noAutoDefaultDeviceChange&&console.warn("noAutoDefaultDeviceChange is not supported on Android, and will be ignored.")}},{key:"validateSimulcastEncodings",value:function(u){var g=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,v=arguments.length>2&&arguments[2]!==void 0&&arguments[2];if(u){if(!(u instanceof Array||Array.isArray(u)))throw new Error("encodings must be an Array");if(!fv(u.length,1,3))throw new Error("encodings must be an Array with between 1 to ".concat(3," layers"));for(var S=0;S<u.length;S++){var k=u[S];for(var N in this._validateEncodingLayerHasValidProperties(k),k)if(tu.includes(N)){if(typeof k[N]!="number")throw new Error("".concat(N," must be a number"));if(g){var J=g[N],K=J.min,ce=J.max;if(!fv(k[N],K,ce))throw new Error("".concat(N," value not in range. valid range: ").concat(K," to ").concat(ce))}}else if(!["active","scalabilityMode"].includes(N))throw new Error("Invalid key ".concat(N,", valid keys are:")+Object.values(tu));if(v&&!k.hasOwnProperty("maxBitrate"))throw new Error("maxBitrate is not specified")}}}},{key:"startRemoteMediaPlayer",value:(re=Se(function*(u){var g=this,v=u.url,S=u.settings,k=S===void 0?{state:Tu.PLAY}:S;try{(function(N){if(typeof N!="string")throw new Error('url parameter must be "string" type')})(v),dv(k),function(N){for(var J in N)if(!av.includes(J))throw new Error("Invalid key ".concat(J,", valid keys are: ").concat(av));N.simulcastEncodings&&this.validateSimulcastEncodings(N.simulcastEncodings,_g,!0)}(k)}catch(N){throw console.error("invalid argument Error: ".concat(N)),console.error(`startRemoteMediaPlayer arguments must be of the form:
  { url: "playback url",
  settings?:
  {state: "play"|"pause", simulcastEncodings?: [{}] } }`),N}return new Promise(function(N,J){g.sendMessageToCallMachine({action:"daily-method-start-remote-media-player",url:v,settings:k},function(K){K.error?J({error:K.error,errorMsg:K.errorMsg}):N({session_id:K.session_id,remoteMediaPlayerState:{state:K.state,settings:K.settings}})})})}),function(u){return re.apply(this,arguments)})},{key:"stopRemoteMediaPlayer",value:(ne=Se(function*(u){var g=this;if(typeof u!="string")throw new Error(" remotePlayerID must be of type string");return new Promise(function(v,S){g.sendMessageToCallMachine({action:"daily-method-stop-remote-media-player",session_id:u},function(k){k.error?S({error:k.error,errorMsg:k.errorMsg}):v()})})}),function(u){return ne.apply(this,arguments)})},{key:"updateRemoteMediaPlayer",value:(Y=Se(function*(u){var g=this,v=u.session_id,S=u.settings;try{dv(S)}catch(k){throw console.error("invalid argument Error: ".concat(k)),console.error(`updateRemoteMediaPlayer arguments must be of the form:
  session_id: "participant session",
  { settings?: {state: "play"|"pause"} }`),k}return new Promise(function(k,N){g.sendMessageToCallMachine({action:"daily-method-update-remote-media-player",session_id:v,settings:S},function(J){J.error?N({error:J.error,errorMsg:J.errorMsg}):k({session_id:J.session_id,remoteMediaPlayerState:{state:J.state,settings:J.settings}})})})}),function(u){return Y.apply(this,arguments)})},{key:"startTranscription",value:function(u){ze(this._callState,"startTranscription()"),this.sendMessageToCallMachine(me({action:"daily-method-start-transcription"},u))}},{key:"updateTranscription",value:function(u){if(ze(this._callState,"updateTranscription()"),!u)throw new Error("updateTranscription Error: options is mandatory");if(Ne(u)!=="object")throw new Error("updateTranscription Error: options must be object type");if(u.participants&&!Array.isArray(u.participants))throw new Error("updateTranscription Error: participants must be an array");this.sendMessageToCallMachine(me({action:"daily-method-update-transcription"},u))}},{key:"stopTranscription",value:function(u){if(ze(this._callState,"stopTranscription()"),u&&Ne(u)!=="object")throw new Error("stopTranscription Error: options must be object type");if(u&&!u.instanceId)throw new Error('"instanceId" not provided');this.sendMessageToCallMachine(me({action:"daily-method-stop-transcription"},u))}},{key:"startDialOut",value:(F=Se(function*(u){var g=this;ze(this._callState,"startDialOut()");var v=function(S){if(S){if(!Array.isArray(S))throw new Error("Error starting dial out: audio codec must be an array");if(S.length<=0)throw new Error("Error starting dial out: audio codec array specified but empty");S.forEach(function(k){if(typeof k!="string")throw new Error("Error starting dial out: audio codec must be a string");if(k!=="OPUS"&&k!=="PCMU"&&k!=="PCMA"&&k!=="G722")throw new Error("Error starting dial out: audio codec must be one of OPUS, PCMU, PCMA, G722")})}};if(!u.sipUri&&!u.phoneNumber)throw new Error("Error starting dial out: either a sip uri or phone number must be provided");if(u.sipUri&&u.phoneNumber)throw new Error("Error starting dial out: only one of sip uri or phone number must be provided");if(u.sipUri){if(typeof u.sipUri!="string")throw new Error("Error starting dial out: sipUri must be a string");if(!u.sipUri.startsWith("sip:"))throw new Error("Error starting dial out: Invalid SIP URI, must start with 'sip:'");if(u.video&&typeof u.video!="boolean")throw new Error("Error starting dial out: video must be a boolean value");(function(S){if(S&&(v(S.audio),S.video)){if(!Array.isArray(S.video))throw new Error("Error starting dial out: video codec must be an array");if(S.video.length<=0)throw new Error("Error starting dial out: video codec array specified but empty");S.video.forEach(function(k){if(typeof k!="string")throw new Error("Error starting dial out: video codec must be a string");if(k!=="H264"&&k!=="VP8")throw new Error("Error starting dial out: video codec must be H264 or VP8")})}})(u.codecs)}if(u.phoneNumber){if(typeof u.phoneNumber!="string")throw new Error("Error starting dial out: phoneNumber must be a string");if(!/^\+\d{1,}$/.test(u.phoneNumber))throw new Error("Error starting dial out: Invalid phone number, must be valid phone number as per E.164");u.codecs&&v(u.codecs.audio)}if(u.callerId){if(typeof u.callerId!="string")throw new Error("Error starting dial out: callerId must be a string");if(u.sipUri)throw new Error("Error starting dial out: callerId not allowed with sipUri")}if(u.displayName){if(typeof u.displayName!="string")throw new Error("Error starting dial out: displayName must be a string");if(u.displayName.length>=200)throw new Error("Error starting dial out: displayName length must be less than 200")}if(u.userId){if(typeof u.userId!="string")throw new Error("Error starting dial out: userId must be a string");if(u.userId.length>36)throw new Error("Error starting dial out: userId length must be less than or equal to 36")}return new Promise(function(S,k){g.sendMessageToCallMachine(me({action:"dialout-start"},u),function(N){N.error?k(N.error):S(N)})})}),function(u){return F.apply(this,arguments)})},{key:"stopDialOut",value:function(u){var g=this;return ze(this._callState,"stopDialOut()"),new Promise(function(v,S){g.sendMessageToCallMachine(me({action:"dialout-stop"},u),function(k){k.error?S(k.error):v(k)})})}},{key:"sipCallTransfer",value:(L=Se(function*(u){var g=this;if(ze(this._callState,"sipCallTransfer()"),!u)throw new Error("sipCallTransfer() requires a sessionId and toEndPoint");return u.useSipRefer=!1,uv(u,"sipCallTransfer"),new Promise(function(v,S){g.sendMessageToCallMachine(me({action:Im},u),function(k){k.error?S(k.error):v(k)})})}),function(u){return L.apply(this,arguments)})},{key:"sipRefer",value:(P=Se(function*(u){var g=this;if(ze(this._callState,"sipRefer()"),!u)throw new Error("sessionId and toEndPoint are mandatory parameter");return u.useSipRefer=!0,uv(u,"sipRefer"),new Promise(function(v,S){g.sendMessageToCallMachine(me({action:Im},u),function(k){k.error?S(k.error):v(k)})})}),function(u){return P.apply(this,arguments)})},{key:"sendDTMF",value:(T=Se(function*(u){var g=this;return ze(this._callState,"sendDTMF()"),function(v){var S=v.sessionId,k=v.tones;if(!S||!k)throw new Error("sessionId and tones are mandatory parameter");if(typeof S!="string"||typeof k!="string")throw new Error("sessionId and tones should be of string type");if(k.length>20)throw new Error("tones string must be upto 20 characters");var N=/[^0-9A-D*#]/g,J=k.match(N);if(J&&J[0])throw new Error("".concat(J[0]," is not valid DTMF tone"))}(u),new Promise(function(v,S){g.sendMessageToCallMachine(me({action:"send-dtmf"},u),function(k){k.error?S(k.error):v(k)})})}),function(u){return T.apply(this,arguments)})},{key:"getNetworkStats",value:function(){var u=this;return this._callState!==Ir?Promise.resolve(me({stats:{latest:{}}},this._network)):new Promise(function(g){u.sendMessageToCallMachine({action:"get-calc-stats"},function(v){g(me(me({},u._network),{},{stats:v.stats}))})})}},{key:"testWebsocketConnectivity",value:(x=Se(function*(){var u=this;if(ru(this._testCallInProgress,"testWebsocketConnectivity()"),this.needsLoad())try{yield this.load()}catch(g){return Promise.reject(g)}return new Promise(function(g,v){u.sendMessageToCallMachine({action:"test-websocket-connectivity"},function(S){S.error?v(S.error):g(S.results)})})}),function(){return x.apply(this,arguments)})},{key:"abortTestWebsocketConnectivity",value:function(){this.sendMessageToCallMachine({action:"abort-test-websocket-connectivity"})}},{key:"_validateVideoTrackForNetworkTests",value:function(u){return u?u instanceof MediaStreamTrack?!!Mk(u)||(console.error("Video track is not playable. This test needs a live video track."),!1):(console.error("Video track needs to be of type `MediaStreamTrack`."),!1):(console.error("Missing video track. You must provide a video track in order to run this test."),!1)}},{key:"testCallQuality",value:(E=Se(function*(){var u=this;Te(),fn(this._callObjectMode,"testCallQuality()"),ua(this._callMachineInitialized,"testCallQuality()",null,!0),nu(this._callState,this._isPreparingToJoin,"testCallQuality()");var g=this._testCallAlreadyInProgress,v=function(k){g||(u._testCallInProgress=k)};if(v(!0),this.needsLoad())try{var S=this._callState;yield this.load(),this._callState=S}catch(k){return v(!1),Promise.reject(k)}return new Promise(function(k){u.sendMessageToCallMachine({action:"test-call-quality",dailyJsVersion:u.properties.dailyJsVersion},function(N){var J=N.results,K=J.result,ce=fh(J,xk);if(K==="failed"){var Ee,Ce=me({},ce);(Ee=ce.error)!==null&&Ee!==void 0&&Ee.details?(ce.error.details=JSON.parse(ce.error.details),Ce.error=me(me({},Ce.error),{},{details:me({},Ce.error.details)}),Ce.error.details.duringTest="testCallQuality"):(Ce.error=Ce.error?me({},Ce.error):{},Ce.error.details={duringTest:"testCallQuality"}),u._maybeSendToSentry(Ce)}v(!1),k(me({result:K},ce))})})}),function(){return E.apply(this,arguments)})},{key:"stopTestCallQuality",value:function(){this.sendMessageToCallMachine({action:"stop-test-call-quality"})}},{key:"testConnectionQuality",value:(w=Se(function*(u){var g;xe()?(console.warn("testConnectionQuality() is deprecated: use testPeerToPeerCallQuality() instead"),g=yield this.testPeerToPeerCallQuality(u)):(console.warn("testConnectionQuality() is deprecated: use testCallQuality() instead"),g=yield this.testCallQuality());var v={result:g.result,secondsElapsed:g.secondsElapsed};return g.data&&(v.data={maxRTT:g.data.maxRoundTripTime,packetLoss:g.data.avgRecvPacketLoss}),v}),function(u){return w.apply(this,arguments)})},{key:"testPeerToPeerCallQuality",value:(_=Se(function*(u){var g=this;if(ru(this._testCallInProgress,"testPeerToPeerCallQuality()"),this.needsLoad())try{yield this.load()}catch(k){return Promise.reject(k)}var v=u.videoTrack,S=u.duration;if(!this._validateVideoTrackForNetworkTests(v))throw new Error("Video track error");return this._sharedTracks.videoTrackForConnectionQualityTest=v,new Promise(function(k,N){g.sendMessageToCallMachine({action:"test-p2p-call-quality",duration:S},function(J){J.error?N(J.error):k(J.results)})})}),function(u){return _.apply(this,arguments)})},{key:"stopTestConnectionQuality",value:function(){xe()?(console.warn("stopTestConnectionQuality() is deprecated: use testPeerToPeerCallQuality() and stopTestPeerToPeerCallQuality() instead"),this.stopTestPeerToPeerCallQuality()):(console.warn("stopTestConnectionQuality() is deprecated: use testCallQuality() and stopTestCallQuality() instead"),this.stopTestCallQuality())}},{key:"stopTestPeerToPeerCallQuality",value:function(){this.sendMessageToCallMachine({action:"stop-test-p2p-call-quality"})}},{key:"testNetworkConnectivity",value:(m=Se(function*(u){var g=this;if(ru(this._testCallInProgress,"testNetworkConnectivity()"),this.needsLoad())try{yield this.load()}catch(v){return Promise.reject(v)}if(!this._validateVideoTrackForNetworkTests(u))throw new Error("Video track error");return this._sharedTracks.videoTrackForNetworkConnectivityTest=u,new Promise(function(v,S){g.sendMessageToCallMachine({action:"test-network-connectivity"},function(k){k.error?S(k.error):v(k.results)})})}),function(u){return m.apply(this,arguments)})},{key:"abortTestNetworkConnectivity",value:function(){this.sendMessageToCallMachine({action:"abort-test-network-connectivity"})}},{key:"getCpuLoadStats",value:function(){var u=this;return new Promise(function(g){u._callState===Ir?u.sendMessageToCallMachine({action:"get-cpu-load-stats"},function(v){g(v.cpuStats)}):g({cpuLoadState:void 0,cpuLoadStateReason:void 0,stats:{}})})}},{key:"_validateEncodingLayerHasValidProperties",value:function(u){var g;if(!(((g=Object.keys(u))===null||g===void 0?void 0:g.length)>0))throw new Error("Empty encoding is not allowed. At least one of these valid keys should be specified:"+Object.values(tu))}},{key:"_validateVideoSendSettings",value:function(u,g){var v=u==="screenVideo"?["default-screen-video","detail-optimized","motion-optimized","motion-and-detail-balanced"]:["default-video","bandwidth-optimized","bandwidth-and-quality-balanced","quality-optimized","adaptive-2-layers","adaptive-3-layers"],S="Video send settings should be either an object or one of the supported presets: ".concat(v.join());if(typeof g=="string"){if(!v.includes(g))throw new Error(S)}else{if(Ne(g)!=="object")throw new Error(S);if(!g.maxQuality&&!g.encodings&&g.allowAdaptiveLayers===void 0)throw new Error("Video send settings must contain at least maxQuality, allowAdaptiveLayers or encodings attribute");if(g.maxQuality&&["low","medium","high"].indexOf(g.maxQuality)===-1)throw new Error("maxQuality must be either low, medium or high");if(g.encodings){var k=!1;switch(Object.keys(g.encodings).length){case 1:k=!g.encodings.low;break;case 2:k=!g.encodings.low||!g.encodings.medium;break;case 3:k=!g.encodings.low||!g.encodings.medium||!g.encodings.high;break;default:k=!0}if(k)throw new Error("Encodings must be defined as: low, low and medium, or low, medium and high.");g.encodings.low&&this._validateEncodingLayerHasValidProperties(g.encodings.low),g.encodings.medium&&this._validateEncodingLayerHasValidProperties(g.encodings.medium),g.encodings.high&&this._validateEncodingLayerHasValidProperties(g.encodings.high)}}}},{key:"validateUpdateSendSettings",value:function(u){var g=this;if(!u||Object.keys(u).length===0)throw new Error("Send settings must contain at least information for one track!");Object.entries(u).forEach(function(v){var S=Mt(v,2),k=S[0],N=S[1];g._validateVideoSendSettings(k,N)})}},{key:"updateSendSettings",value:function(u){var g=this;return this.validateUpdateSendSettings(u),this.needsLoad()?(this._preloadCache.sendSettings=u,{sendSettings:this._preloadCache.sendSettings}):new Promise(function(v,S){g.sendMessageToCallMachine({action:"update-send-settings",sendSettings:u},function(k){k.error?S(k.error):v(k.sendSettings)})})}},{key:"getSendSettings",value:function(){return this._sendSettings||this._preloadCache.sendSettings}},{key:"getLocalAudioLevel",value:function(){return this._localAudioLevel}},{key:"getRemoteParticipantsAudioLevel",value:function(){return this._remoteParticipantsAudioLevel}},{key:"getActiveSpeaker",value:function(){return Te(),this._activeSpeaker}},{key:"setActiveSpeakerMode",value:function(u){return Te(),this.sendMessageToCallMachine({action:"set-active-speaker-mode",enabled:u}),this}},{key:"activeSpeakerMode",value:function(){return Te(),this._activeSpeakerMode}},{key:"subscribeToTracksAutomatically",value:function(){return this._preloadCache.subscribeToTracksAutomatically}},{key:"setSubscribeToTracksAutomatically",value:function(u){return ze(this._callState,"setSubscribeToTracksAutomatically()","Use the subscribeToTracksAutomatically configuration property."),this._preloadCache.subscribeToTracksAutomatically=u,this.sendMessageToCallMachine({action:"daily-method-subscribe-to-tracks-automatically",enabled:u}),this}},{key:"enumerateDevices",value:(h=Se(function*(){var u=this;if(this._callObjectMode){var g=yield navigator.mediaDevices.enumerateDevices();return qr()==="Firefox"&&Ea().major>115&&Ea().major<123&&(g=g.filter(function(v){return v.kind!=="audiooutput"})),{devices:g.map(function(v){var S=JSON.parse(JSON.stringify(v));if(!xe()&&v.kind==="videoinput"&&v.getCapabilities){var k,N=v.getCapabilities();S.facing=(N==null||(k=N.facingMode)===null||k===void 0?void 0:k.length)>=1?N.facingMode[0]:void 0}return S})}}return new Promise(function(v){u.sendMessageToCallMachine({action:"enumerate-devices"},function(S){v({devices:S.devices})})})}),function(){return h.apply(this,arguments)})},{key:"sendAppMessage",value:function(u){var g=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"*";if(ze(this._callState,"sendAppMessage()"),JSON.stringify(u).length>this._maxAppMessageSize)throw new Error("Message data too large. Max size is "+this._maxAppMessageSize);return this.sendMessageToCallMachine({action:"app-msg",data:u,to:g}),this}},{key:"addFakeParticipant",value:function(u){return Te(),ze(this._callState,"addFakeParticipant()"),this.sendMessageToCallMachine(me({action:"add-fake-participant"},u)),this}},{key:"setShowNamesMode",value:function(u){return Bt(this._callObjectMode,"setShowNamesMode()"),Te(),u&&u!=="always"&&u!=="never"?(console.error('setShowNamesMode argument should be "always", "never", or false'),this):(this.sendMessageToCallMachine({action:"set-show-names",mode:u}),this)}},{key:"setShowLocalVideo",value:function(){var u=!(arguments.length>0&&arguments[0]!==void 0)||arguments[0];return Bt(this._callObjectMode,"setShowLocalVideo()"),Te(),ze(this._callState,"setShowLocalVideo()"),typeof u!="boolean"?(console.error("setShowLocalVideo only accepts a boolean value"),this):(this.sendMessageToCallMachine({action:"set-show-local-video",show:u}),this._showLocalVideo=u,this)}},{key:"showLocalVideo",value:function(){return Bt(this._callObjectMode,"showLocalVideo()"),Te(),this._showLocalVideo}},{key:"setShowParticipantsBar",value:function(){var u=!(arguments.length>0&&arguments[0]!==void 0)||arguments[0];return Bt(this._callObjectMode,"setShowParticipantsBar()"),Te(),ze(this._callState,"setShowParticipantsBar()"),typeof u!="boolean"?(console.error("setShowParticipantsBar only accepts a boolean value"),this):(this.sendMessageToCallMachine({action:"set-show-participants-bar",show:u}),this._showParticipantsBar=u,this)}},{key:"showParticipantsBar",value:function(){return Bt(this._callObjectMode,"showParticipantsBar()"),Te(),this._showParticipantsBar}},{key:"customIntegrations",value:function(){return Te(),Bt(this._callObjectMode,"customIntegrations()"),this._customIntegrations}},{key:"setCustomIntegrations",value:function(u){return Te(),Bt(this._callObjectMode,"setCustomIntegrations()"),ze(this._callState,"setCustomIntegrations()"),Tg(u)?(this.sendMessageToCallMachine({action:"set-custom-integrations",integrations:u}),this._customIntegrations=u,this):this}},{key:"startCustomIntegrations",value:function(u){var g=this;if(Te(),Bt(this._callObjectMode,"startCustomIntegrations()"),ze(this._callState,"startCustomIntegrations()"),Array.isArray(u)&&u.some(function(k){return typeof k!="string"})||!Array.isArray(u)&&typeof u!="string")return console.error("startCustomIntegrations() only accepts string | string[]"),this;var v=typeof u=="string"?[u]:u,S=v.filter(function(k){return!(k in g._customIntegrations)});return S.length?(console.error(`Can't find custom integration(s): "`.concat(S.join(", "),'"')),this):(this.sendMessageToCallMachine({action:"start-custom-integrations",ids:v}),this)}},{key:"stopCustomIntegrations",value:function(u){var g=this;if(Te(),Bt(this._callObjectMode,"stopCustomIntegrations()"),ze(this._callState,"stopCustomIntegrations()"),Array.isArray(u)&&u.some(function(k){return typeof k!="string"})||!Array.isArray(u)&&typeof u!="string")return console.error("stopCustomIntegrations() only accepts string | string[]"),this;var v=typeof u=="string"?[u]:u,S=v.filter(function(k){return!(k in g._customIntegrations)});return S.length?(console.error(`Can't find custom integration(s): "`.concat(S.join(", "),'"')),this):(this.sendMessageToCallMachine({action:"stop-custom-integrations",ids:v}),this)}},{key:"customTrayButtons",value:function(){return Bt(this._callObjectMode,"customTrayButtons()"),Te(),this._customTrayButtons}},{key:"updateCustomTrayButtons",value:function(u){return Bt(this._callObjectMode,"updateCustomTrayButtons()"),Te(),ze(this._callState,"updateCustomTrayButtons()"),Mg(u)?(this.sendMessageToCallMachine({action:"update-custom-tray-buttons",btns:u}),this._customTrayButtons=u,this):(console.error("updateCustomTrayButtons only accepts a dictionary of the type ".concat(JSON.stringify(Ma))),this)}},{key:"theme",value:function(){return Bt(this._callObjectMode,"theme()"),this.properties.theme}},{key:"setTheme",value:function(u){var g=this;return Bt(this._callObjectMode,"setTheme()"),new Promise(function(v,S){try{g.validateProperties({theme:u}),g.properties.theme=me({},u),g.sendMessageToCallMachine({action:"set-theme",theme:g.properties.theme});try{g.emitDailyJSEvent({action:fS,theme:g.properties.theme})}catch(k){console.log("could not emit 'theme-updated'",k)}v(g.properties.theme)}catch(k){S(k)}})}},{key:"requestFullscreen",value:(d=Se(function*(){if(Te(),this._iframe&&!document.fullscreenElement&&Fm())try{(yield this._iframe.requestFullscreen)?this._iframe.requestFullscreen():this._iframe.webkitRequestFullscreen()}catch(u){console.log("could not make video call fullscreen",u)}}),function(){return d.apply(this,arguments)})},{key:"exitFullscreen",value:function(){Te(),document.fullscreenElement?document.exitFullscreen():document.webkitFullscreenElement&&document.webkitExitFullscreen()}},{key:"getSidebarView",value:(c=Se(function*(){var u=this;return this._callObjectMode?(console.error("getSidebarView is not available in callObject mode"),Promise.resolve(null)):new Promise(function(g){u.sendMessageToCallMachine({action:"get-sidebar-view"},function(v){g(v.view)})})}),function(){return c.apply(this,arguments)})},{key:"setSidebarView",value:function(u){return this._callObjectMode?(console.error("setSidebarView is not available in callObject mode"),this):(this.sendMessageToCallMachine({action:"set-sidebar-view",view:u}),this)}},{key:"room",value:(a=Se(function*(){var u=this,g=(arguments.length>0&&arguments[0]!==void 0?arguments[0]:{}).includeRoomConfigDefaults,v=g===void 0||g;return this._accessState.access===Gc||this.needsLoad()?this.properties.url?{roomUrlPendingJoin:this.properties.url}:null:new Promise(function(S){u.sendMessageToCallMachine({action:"lib-room-info",includeRoomConfigDefaults:v},function(k){delete k.action,delete k.callbackStamp,S(k)})})}),function(){return a.apply(this,arguments)})},{key:"geo",value:(s=Se(function*(){try{var u=yield fetch("https://gs.daily.co/_ks_/x-swsl/:");return{current:(yield u.json()).geo}}catch(g){return console.error("geo lookup failed",g),{current:""}}}),function(){return s.apply(this,arguments)})},{key:"setNetworkTopology",value:(i=Se(function*(u){var g=this;return Te(),ze(this._callState,"setNetworkTopology()"),new Promise(function(v,S){g.sendMessageToCallMachine({action:"set-network-topology",opts:u},function(k){k.error?S({error:k.error}):v({workerId:k.workerId})})})}),function(u){return i.apply(this,arguments)})},{key:"getNetworkTopology",value:(e=Se(function*(){var u=this;return new Promise(function(g,v){u.needsLoad()&&g({topology:"none"}),u.sendMessageToCallMachine({action:"get-network-topology"},function(S){S.error?v({error:S.error}):g({topology:S.topology})})})}),function(){return e.apply(this,arguments)})},{key:"setPlayNewParticipantSound",value:function(u){if(Te(),typeof u!="number"&&u!==!0&&u!==!1)throw new Error("argument to setShouldPlayNewParticipantSound should be true, false, or a number, but is ".concat(u));this.sendMessageToCallMachine({action:"daily-method-set-play-ding",arg:u})}},{key:"on",value:function(u,g){return Yo.prototype.on.call(this,u,g)}},{key:"once",value:function(u,g){return Yo.prototype.once.call(this,u,g)}},{key:"off",value:function(u,g){return Yo.prototype.off.call(this,u,g)}},{key:"validateProperties",value:function(u){var g,v;if(u!=null&&(g=u.dailyConfig)!==null&&g!==void 0&&g.userMediaAudioConstraints){var S,k;xe()||console.warn("userMediaAudioConstraints is deprecated. You can override constraints with inputSettings.audio.settings, found in DailyCallOptions.");var N=u.inputSettings||{};N.audio=((S=u.inputSettings)===null||S===void 0?void 0:S.audio)||{},N.audio.settings=((k=u.inputSettings)===null||k===void 0||(k=k.audio)===null||k===void 0?void 0:k.settings)||{},N.audio.settings=me(me({},N.audio.settings),u.dailyConfig.userMediaAudioConstraints),u.inputSettings=N,delete u.dailyConfig.userMediaAudioConstraints}if(u!=null&&(v=u.dailyConfig)!==null&&v!==void 0&&v.userMediaVideoConstraints){var J,K;xe()||console.warn("userMediaVideoConstraints is deprecated. You can override constraints with inputSettings.video.settings, found in DailyCallOptions.");var ce=u.inputSettings||{};ce.video=((J=u.inputSettings)===null||J===void 0?void 0:J.video)||{},ce.video.settings=((K=u.inputSettings)===null||K===void 0||(K=K.video)===null||K===void 0?void 0:K.settings)||{},ce.video.settings=me(me({},ce.video.settings),u.dailyConfig.userMediaVideoConstraints),u.inputSettings=ce,delete u.dailyConfig.userMediaVideoConstraints}for(var Ee in u){if(!Rr[Ee])throw new Error("unrecognized property '".concat(Ee,"'"));if(Rr[Ee].validate&&!Rr[Ee].validate(u[Ee],this))throw new Error("property '".concat(Ee,"': ").concat(Rr[Ee].help))}}},{key:"assembleMeetingUrl",value:function(){var u,g,v=me(me({},this.properties),{},{emb:this.callClientId,embHref:encodeURIComponent(window.location.href),proxy:(u=this.properties.dailyConfig)!==null&&u!==void 0&&u.proxyUrl?encodeURIComponent((g=this.properties.dailyConfig)===null||g===void 0?void 0:g.proxyUrl):void 0}),S=v.url.match(/\?/)?"&":"?";return v.url+S+Object.keys(Rr).filter(function(k){return Rr[k].queryString&&v[k]!==void 0}).map(function(k){return"".concat(Rr[k].queryString,"=").concat(v[k])}).join("&")}},{key:"needsLoad",value:function(){return[Em,Cm,Nr,lr].includes(this._callState)}},{key:"sendMessageToCallMachine",value:function(u,g){if(this._destroyed&&(this._logUseAfterDestroy(),this.strictMode))throw new Error("Use after destroy");this._messageChannel.sendMessageToCallMachine(u,g,this.callClientId,this._iframe)}},{key:"forwardPackagedMessageToCallMachine",value:function(u){this._messageChannel.forwardPackagedMessageToCallMachine(u,this._iframe,this.callClientId)}},{key:"addListenerForPackagedMessagesFromCallMachine",value:function(u){return this._messageChannel.addListenerForPackagedMessagesFromCallMachine(u,this.callClientId)}},{key:"removeListenerForPackagedMessagesFromCallMachine",value:function(u){this._messageChannel.removeListenerForPackagedMessagesFromCallMachine(u)}},{key:"handleMessageFromCallMachine",value:function(u){switch(u.action){case uS:this.sendMessageToCallMachine(me({action:dS},this.properties));break;case"call-machine-initialized":this._callMachineInitialized=!0;var g={action:aa,level:"log",code:1011,stats:{event:"bundle load",time:this._bundleLoadTime==="no-op"?0:this._bundleLoadTime,preLoaded:this._bundleLoadTime==="no-op",url:ya(this.properties.dailyConfig)}};this.sendMessageToCallMachine(g),this._delayDuplicateInstanceLog&&this._logDuplicateInstanceAttempt();break;case Pm:this._loadedCallback&&(this._loadedCallback(),this._loadedCallback=null),this.emitDailyJSEvent(u);break;case yS:var v,S=me({},u);delete S.internal,this._maxAppMessageSize=((v=u.internal)===null||v===void 0?void 0:v._maxAppMessageSize)||Kc,this._joinedCallback&&(this._joinedCallback(u.participants),this._joinedCallback=null),this.emitDailyJSEvent(S);break;case wS:case SS:if(this._callState===Nr)return;if(u.participant&&u.participant.session_id){var k=u.participant.local?"local":u.participant.session_id;if(this._callObjectMode){var N=this._callMachine().store;Jm(u.participant,N),Hm(u.participant,N),Qm(u.participant,this._participants[k],N)}try{this.maybeParticipantTracksStopped(this._participants[k],u.participant),this.maybeParticipantTracksStarted(this._participants[k],u.participant),this.maybeEventRecordingStopped(this._participants[k],u.participant),this.maybeEventRecordingStarted(this._participants[k],u.participant)}catch(Is){console.error("track events error",Is)}this.compareEqualForParticipantUpdateEvent(u.participant,this._participants[k])||(this._participants[k]=me({},u.participant),this.toggleParticipantAudioBasedOnNativeAudioFocus(),this.emitDailyJSEvent(u))}break;case kS:if(u.participant&&u.participant.session_id){var J=this._participants[u.participant.session_id];J&&this.maybeParticipantTracksStopped(J,null),delete this._participants[u.participant.session_id],this.emitDailyJSEvent(u)}break;case bS:Et(this._participantCounts,u.participantCounts)||(this._participantCounts=u.participantCounts,this.emitDailyJSEvent(u));break;case ES:var K={access:u.access};u.awaitingAccess&&(K.awaitingAccess=u.awaitingAccess),Et(this._accessState,K)||(this._accessState=K,this.emitDailyJSEvent(u));break;case CS:if(u.meetingSession){this._meetingSessionSummary=u.meetingSession,this.emitDailyJSEvent(u);var ce=me(me({},u),{},{action:"meeting-session-updated"});this.emitDailyJSEvent(ce)}break;case Nm:var Ee;this._iframe&&!u.preserveIframe&&(this._iframe.src=""),this._updateCallState(lr),this.resetMeetingDependentVars(),this._loadedCallback&&(this._loadedCallback(u.errorMsg),this._loadedCallback=null),u.preserveIframe;var Ce=fh(u,Ak);Ce!=null&&(Ee=Ce.error)!==null&&Ee!==void 0&&Ee.details&&(Ce.error.details=JSON.parse(Ce.error.details)),this._maybeSendToSentry(u),this._joinedCallback&&(this._joinedCallback(null,Ce),this._joinedCallback=null),this.emitDailyJSEvent(Ce);break;case _S:this._callState!==lr&&this._updateCallState(Nr),this.resetMeetingDependentVars(),this._resolveLeave&&(this._resolveLeave(),this._resolveLeave=null),this.emitDailyJSEvent(u);break;case"selected-devices-updated":u.devices&&this.emitDailyJSEvent(u);break;case GS:var Tt=u.state,yn=u.threshold,Un=u.quality,Ke=Tt.state,_t=Tt.reasons;Ke===this._network.networkState&&Et(_t,this._network.networkStateReasons)&&yn===this._network.threshold&&Un===this._network.quality||(this._network.networkState=Ke,this._network.networkStateReasons=_t,this._network.quality=Un,this._network.threshold=yn,u.networkState=Ke,_t.length&&(u.networkStateReasons=_t),delete u.state,this.emitDailyJSEvent(u));break;case YS:u&&u.cpuLoadState&&this.emitDailyJSEvent(u);break;case XS:u&&u.faceCounts!==void 0&&this.emitDailyJSEvent(u);break;case HS:var tn=u.activeSpeaker;this._activeSpeaker.peerId!==tn.peerId&&(this._activeSpeaker.peerId=tn.peerId,this.emitDailyJSEvent({action:u.action,activeSpeaker:this._activeSpeaker}));break;case"show-local-video-changed":if(this._callObjectMode)return;var nn=u.show;this._showLocalVideo=nn,this.emitDailyJSEvent({action:u.action,show:nn});break;case QS:var rn=u.enabled;this._activeSpeakerMode!==rn&&(this._activeSpeakerMode=rn,this.emitDailyJSEvent({action:u.action,enabled:this._activeSpeakerMode}));break;case TS:case PS:case OS:this._waitingParticipants=u.allWaitingParticipants,this.emitDailyJSEvent({action:u.action,participant:u.participant});break;case ik:Et(this._receiveSettings,u.receiveSettings)||(this._receiveSettings=u.receiveSettings,this.emitDailyJSEvent({action:u.action,receiveSettings:u.receiveSettings}));break;case Am:this._maybeUpdateInputSettings(u.inputSettings);break;case"send-settings-updated":Et(this._sendSettings,u.sendSettings)||(this._sendSettings=u.sendSettings,this._preloadCache.sendSettings=null,this.emitDailyJSEvent({action:u.action,sendSettings:u.sendSettings}));break;case"local-audio-level":this._localAudioLevel=u.audioLevel,this._preloadCache.localAudioLevelObserver=null,this.emitDailyJSEvent(u);break;case"remote-participants-audio-level":this._remoteParticipantsAudioLevel=u.participantsAudioLevel,this._preloadCache.remoteParticipantsAudioLevelObserver=null,this.emitDailyJSEvent(u);break;case US:var Tn=u.session_id;this._rmpPlayerState[Tn]=u.playerState,this.emitDailyJSEvent(u);break;case VS:delete this._rmpPlayerState[u.session_id],this.emitDailyJSEvent(u);break;case zS:var zn=u.session_id,Ri=this._rmpPlayerState[zn];Ri&&this.compareEqualForRMPUpdateEvent(Ri,u.remoteMediaPlayerState)||(this._rmpPlayerState[zn]=u.remoteMediaPlayerState,this.emitDailyJSEvent(u));break;case"custom-button-click":case"sidebar-view-changed":this.emitDailyJSEvent(u);break;case MS:var Ds=this._meetingSessionState.topology!==(u.meetingSessionState&&u.meetingSessionState.topology);this._meetingSessionState=iu(u.meetingSessionState,this._callObjectMode),(this._callObjectMode||Ds)&&this.emitDailyJSEvent(u);break;case WS:this._isScreenSharing=!0,this.emitDailyJSEvent(u);break;case qS:case JS:this._isScreenSharing=!1,this.emitDailyJSEvent(u);break;case Om:case xm:case RS:case DS:case IS:case LS:case NS:case jS:case mS:case vS:case BS:case $S:case"test-completed":case KS:case FS:case ZS:case ek:case tk:case nk:case Lm:case rk:case"dialin-ready":case"dialin-connected":case"dialin-error":case"dialin-stopped":case"dialin-warning":case"dialout-connected":case"dialout-answered":case"dialout-error":case"dialout-stopped":case"dialout-warning":this.emitDailyJSEvent(u);break;case"request-fullscreen":this.requestFullscreen();break;case"request-exit-fullscreen":this.exitFullscreen()}}},{key:"maybeEventRecordingStopped",value:function(u,g){var v="record";u&&(g.local||g[v]!==!1||u[v]===g[v]||this.emitDailyJSEvent({action:xm}))}},{key:"maybeEventRecordingStarted",value:function(u,g){var v="record";u&&(g.local||g[v]!==!0||u[v]===g[v]||this.emitDailyJSEvent({action:Om}))}},{key:"_trackStatePlayable",value:function(u){return!(!u||u.state!==sS)}},{key:"_trackChanged",value:function(u,g){return(u==null?void 0:u.id)!==(g==null?void 0:g.id)}},{key:"maybeEventTrackStopped",value:function(u,g,v){var S,k,N=(S=g==null?void 0:g.tracks[u])!==null&&S!==void 0?S:null,J=(k=v==null?void 0:v.tracks[u])!==null&&k!==void 0?k:null,K=N==null?void 0:N.track;if(K){var ce=this._trackStatePlayable(N),Ee=this._trackStatePlayable(J),Ce=this._trackChanged(K,J==null?void 0:J.track);ce&&(Ee&&!Ce||this.emitDailyJSEvent({action:AS,track:K,participant:v??g,type:u}))}}},{key:"maybeEventTrackStarted",value:function(u,g,v){var S,k,N=(S=g==null?void 0:g.tracks[u])!==null&&S!==void 0?S:null,J=(k=v==null?void 0:v.tracks[u])!==null&&k!==void 0?k:null,K=J==null?void 0:J.track;if(K){var ce=this._trackStatePlayable(N),Ee=this._trackStatePlayable(J),Ce=this._trackChanged(N==null?void 0:N.track,K);Ee&&(ce&&!Ce||this.emitDailyJSEvent({action:xS,track:K,participant:v,type:u}))}}},{key:"maybeParticipantTracksStopped",value:function(u,g){if(u)for(var v in u.tracks)this.maybeEventTrackStopped(v,u,g)}},{key:"maybeParticipantTracksStarted",value:function(u,g){if(g)for(var v in g.tracks)this.maybeEventTrackStarted(v,u,g)}},{key:"compareEqualForRMPUpdateEvent",value:function(u,g){var v,S;return u.state===g.state&&((v=u.settings)===null||v===void 0?void 0:v.volume)===((S=g.settings)===null||S===void 0?void 0:S.volume)}},{key:"emitDailyJSEvent",value:function(u){try{u.callClientId=this.callClientId,this.emit(u.action,u)}catch(g){console.log("could not emit",u,g)}}},{key:"compareEqualForParticipantUpdateEvent",value:function(u,g){return!!Et(u,g)&&(!u.videoTrack||!g.videoTrack||u.videoTrack.id===g.videoTrack.id&&u.videoTrack.muted===g.videoTrack.muted&&u.videoTrack.enabled===g.videoTrack.enabled)&&(!u.audioTrack||!g.audioTrack||u.audioTrack.id===g.audioTrack.id&&u.audioTrack.muted===g.audioTrack.muted&&u.audioTrack.enabled===g.audioTrack.enabled)}},{key:"nativeUtils",value:function(){return xe()?typeof DailyNativeUtils>"u"?(console.warn("in React Native, DailyNativeUtils is expected to be available"),null):DailyNativeUtils:null}},{key:"updateIsPreparingToJoin",value:function(u){this._updateCallState(this._callState,u)}},{key:"_updateCallState",value:function(u){var g=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this._isPreparingToJoin;if(u!==this._callState||g!==this._isPreparingToJoin){var v=this._callState,S=this._isPreparingToJoin;this._callState=u,this._isPreparingToJoin=g;var k=this._callState===Ir;this.updateShowAndroidOngoingMeetingNotification(k);var N=ma(v,S),J=ma(this._callState,this._isPreparingToJoin);N!==J&&(this.updateKeepDeviceAwake(J),this.updateDeviceAudioMode(J),this.updateNoOpRecordingEnsuringBackgroundContinuity(J))}}},{key:"resetMeetingDependentVars",value:function(){this._participants={},this._participantCounts=ov,this._waitingParticipants={},this._activeSpeaker={},this._activeSpeakerMode=!1,this._didPreAuth=!1,this._accessState={access:Gc},this._finalSummaryOfPrevSession=this._meetingSessionSummary,this._meetingSessionSummary={},this._meetingSessionState=iu(sv,this._callObjectMode),this._isScreenSharing=!1,this._receiveSettings={},this._inputSettings=void 0,this._sendSettings={},this._localAudioLevel=0,this._isLocalAudioLevelObserverRunning=!1,this._remoteParticipantsAudioLevel={},this._isRemoteParticipantsAudioLevelObserverRunning=!1,this._maxAppMessageSize=Kc,this._callMachineInitialized=!1,this._bundleLoadTime=void 0,this._preloadCache}},{key:"updateKeepDeviceAwake",value:function(u){xe()&&this.nativeUtils().setKeepDeviceAwake(u,this.callClientId)}},{key:"updateDeviceAudioMode",value:function(u){if(xe()&&!this.disableReactNativeAutoDeviceManagement("audio")){var g=u?this._nativeInCallAudioMode:"idle";this.nativeUtils().setAudioMode(g)}}},{key:"updateShowAndroidOngoingMeetingNotification",value:function(u){if(xe()&&this.nativeUtils().setShowOngoingMeetingNotification){var g,v,S,k;if(this.properties.reactNativeConfig&&this.properties.reactNativeConfig.androidInCallNotification){var N=this.properties.reactNativeConfig.androidInCallNotification;g=N.title,v=N.subtitle,S=N.iconName,k=N.disableForCustomOverride}k&&(u=!1),this.nativeUtils().setShowOngoingMeetingNotification(u,g,v,S,this.callClientId)}}},{key:"updateNoOpRecordingEnsuringBackgroundContinuity",value:function(u){xe()&&this.nativeUtils().enableNoOpRecordingEnsuringBackgroundContinuity&&this.nativeUtils().enableNoOpRecordingEnsuringBackgroundContinuity(u)}},{key:"toggleParticipantAudioBasedOnNativeAudioFocus",value:function(){var u;if(xe()){var g=(u=this._callMachine())===null||u===void 0||(u=u.store)===null||u===void 0?void 0:u.getState();for(var v in g==null?void 0:g.streams){var S=g.streams[v];S&&S.pendingTrack&&S.pendingTrack.kind==="audio"&&(S.pendingTrack.enabled=this._hasNativeAudioFocus)}}}},{key:"disableReactNativeAutoDeviceManagement",value:function(u){return this.properties.reactNativeConfig&&this.properties.reactNativeConfig.disableAutoDeviceManagement&&this.properties.reactNativeConfig.disableAutoDeviceManagement[u]}},{key:"absoluteUrl",value:function(u){if(u!==void 0){var g=document.createElement("a");return g.href=u,g.href}}},{key:"sayHello",value:function(){var u="hello, world.";return console.log(u),u}},{key:"_logUseAfterDestroy",value:function(){var u=Object.values(jr)[0];if(this.needsLoad())if(u&&!u.needsLoad()){var g={action:aa,level:"error",code:this.strictMode?9995:9997};u.sendMessageToCallMachine(g)}else this.strictMode||console.error("You are are attempting to use a call instance that was previously destroyed, which is unsupported. Please remove `strictMode: false` from your constructor properties to enable strict mode to track down and fix this unsupported usage.");else{var v={action:aa,level:"error",code:this.strictMode?9995:9997};this._messageChannel.sendMessageToCallMachine(v,null,this.callClientId,this._iframe)}}},{key:"_logDuplicateInstanceAttempt",value:function(){for(var u=0,g=Object.values(jr);u<g.length;u++){var v=g[u];v._callMachineInitialized?(v.sendMessageToCallMachine({action:aa,level:"warn",code:this.allowMultipleCallInstances?9993:9992}),v._delayDuplicateInstanceLog=!1):v._delayDuplicateInstanceLog=!0}}},{key:"_maybeSendToSentry",value:function(u){var g,v,S,k;if(!((g=u.error)!==null&&g!==void 0&&g.type&&(![cS,lS,Tm].includes(u.error.type)||u.error.type===Tm&&u.error.msg.includes("deleted")))){var N=(v=this.properties)!==null&&v!==void 0&&v.url?new URL(this.properties.url):void 0,J="production";N&&N.host.includes(".staging.daily")&&(J="staging");var K,ce,Ee,Ce,Tt,yn=function(Tn){const zn=[C0(),b0(),Y0(),G0(),nS(),iS(),O0(),rS()];return Tn.autoSessionTracking!==!1&&zn.push(tS()),zn}({}).filter(function(Tn){return!["BrowserApiErrors","Breadcrumbs","GlobalHandlers"].includes(Tn.name)}),Un=new I0({dsn:"https://<EMAIL>/168844",transport:z0,stackParser:Q0,integrations:yn,environment:J}),Ke=new gr;if(Ke.setClient(Un),Un.init(),this.session_id&&Ke.setExtra("sessionId",this.session_id),this.properties){var _t=me({},this.properties);_t.userName=_t.userName?"[Filtered]":void 0,_t.userData=_t.userData?"[Filtered]":void 0,_t.token=_t.token?"[Filtered]":void 0,Ke.setExtra("properties",_t)}if(N){var tn=N.searchParams.get("domain");if(!tn){var nn=N.host.match(/(.*?)\./);tn=nn&&nn[1]||""}tn&&Ke.setTag("domain",tn)}u.error&&(Ke.setTag("fatalErrorType",u.error.type),Ke.setExtra("errorDetails",u.error.details),!((K=u.error.details)===null||K===void 0)&&K.uri&&Ke.setTag("serverAddress",u.error.details.uri),!((ce=u.error.details)===null||ce===void 0)&&ce.workerGroup&&Ke.setTag("workerGroup",u.error.details.workerGroup),!((Ee=u.error.details)===null||Ee===void 0)&&Ee.geoGroup&&Ke.setTag("geoGroup",u.error.details.geoGroup),!((Ce=u.error.details)===null||Ce===void 0)&&Ce.on&&Ke.setTag("connectionAttempt",u.error.details.on),(Tt=u.error.details)!==null&&Tt!==void 0&&Tt.bundleUrl&&(Ke.setTag("bundleUrl",u.error.details.bundleUrl),Ke.setTag("bundleError",u.error.details.sourceError.type))),Ke.setTags({callMode:this._callObjectMode?xe()?"reactNative":(S=this.properties)!==null&&S!==void 0&&(S=S.dailyConfig)!==null&&S!==void 0&&(S=S.callMode)!==null&&S!==void 0&&S.includes("prebuilt")?this.properties.dailyConfig.callMode:"custom":"prebuilt-frame",version:n.version()});var rn=((k=u.error)===null||k===void 0?void 0:k.msg)||u.errorMsg;Ke.captureException(new Error(rn))}}},{key:"_callMachine",value:function(){var u;return(u=window._daily)===null||u===void 0||(u=u.instances)===null||u===void 0||(u=u[this.callClientId])===null||u===void 0?void 0:u.callMachine}},{key:"_maybeUpdateInputSettings",value:function(u){if(!Et(this._inputSettings,u)){var g=this._getInputSettings();this._inputSettings=u;var v=this._getInputSettings();Et(g,v)||this.emitDailyJSEvent({action:Am,inputSettings:v})}}}],[{key:"supportedBrowser",value:function(){if(xe())return{supported:!0,mobile:!0,name:"React Native",version:null,supportsScreenShare:!0,supportsSfu:!0,supportsVideoProcessing:!1,supportsAudioProcessing:!1};var u=qw.getParser(en());return{supported:!!Bm(),mobile:u.getPlatformType()==="mobile",name:u.getBrowserName(),version:u.getBrowserVersion(),supportsFullscreen:!!Fm(),supportsScreenShare:!!ak(),supportsSfu:!!Bm(),supportsVideoProcessing:ag(),supportsAudioProcessing:lg()}}},{key:"version",value:function(){return"0.77.0"}},{key:"createCallObject",value:function(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return u.layout="none",new n(null,u)}},{key:"wrap",value:function(u){var g=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(Te(),!u||!u.contentWindow||typeof u.src!="string")throw new Error("DailyIframe::Wrap needs an iframe-like first argument");return g.layout||(g.customLayout?g.layout="custom-v1":g.layout="browser"),new n(u,g)}},{key:"createFrame",value:function(u,g){var v,S;Te(),u&&g?(v=u,S=g):u&&u.append?(v=u,S={}):(v=document.body,S=u||{});var k=S.iframeStyle;k||(k=v===document.body?{position:"fixed",border:"1px solid black",backgroundColor:"white",width:"375px",height:"450px",right:"1em",bottom:"1em"}:{border:0,width:"100%",height:"100%"});var N=document.createElement("iframe");window.navigator&&window.navigator.userAgent.match(/Chrome\/61\./)?N.allow="microphone, camera":N.allow="microphone; camera; autoplay; display-capture; screen-wake-lock",N.style.visibility="hidden",v.appendChild(N),N.style.visibility=null,Object.keys(k).forEach(function(J){return N.style[J]=k[J]}),S.layout||(S.customLayout?S.layout="custom-v1":S.layout="browser");try{return new n(N,S)}catch(J){throw v.removeChild(N),J}}},{key:"createTransparentFrame",value:function(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Te();var g=document.createElement("iframe");return g.allow="microphone; camera; autoplay",g.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: 0;
      pointer-events: none;
    `,document.body.appendChild(g),u.layout||(u.layout="custom-v1"),n.wrap(g,u)}},{key:"getCallInstance",value:function(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:void 0;return u?jr[u]:Object.values(jr)[0]}}]);var e,i,s,a,c,d,h,m,_,w,E,x,T,P,L,F,Y,ne,re,B,I,U,R,z,X,$,te,se,pe,_e,Oe,Re,he,Z,ae,ie,O,V}();function _i(n,e){var i={};for(var s in n)if(n[s]instanceof MediaStreamTrack)console.warn("MediaStreamTrack found in props or cache.",s),i[s]=Bn;else if(s==="dailyConfig"){if(n[s].modifyLocalSdpHook){var a=window._daily.instances[e].customCallbacks||{};a.modifyLocalSdpHook=n[s].modifyLocalSdpHook,window._daily.instances[e].customCallbacks=a,delete n[s].modifyLocalSdpHook}if(n[s].modifyRemoteSdpHook){var c=window._daily.instances[e].customCallbacks||{};c.modifyRemoteSdpHook=n[s].modifyRemoteSdpHook,window._daily.instances[e].customCallbacks=c,delete n[s].modifyRemoteSdpHook}i[s]=n[s]}else i[s]=n[s];return i}function ze(n){var e=arguments.length>2?arguments[2]:void 0;if(n!==Ir){var i="".concat(arguments.length>1&&arguments[1]!==void 0?arguments[1]:"This daily-js method"," only supported after join.");throw e&&(i+=" ".concat(e)),console.error(i),new Error(i)}}function ma(n,e){return[Mu,Ir].includes(n)||e}function nu(n,e){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"This daily-js method",s=arguments.length>3?arguments[3]:void 0;if(ma(n,e)){var a="".concat(i," not supported after joining a meeting.");throw s&&(a+=" ".concat(s)),console.error(a),new Error(a)}}function ua(n){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"This daily-js method",i=arguments.length>2?arguments[2]:void 0;if(!n){var s="".concat(e,arguments.length>3&&arguments[3]!==void 0&&arguments[3]?" requires preAuth() or startCamera() to initialize call state.":" requires preAuth(), startCamera(), or join() to initialize call state.");throw i&&(s+=" ".concat(i)),console.error(s),new Error(s)}}function ru(n){if(n){var e="A pre-call quality test is in progress. Please try ".concat(arguments.length>1&&arguments[1]!==void 0?arguments[1]:"This daily-js method"," again once testing has completed. Use stopTestCallQuality() to end it early.");throw console.error(e),new Error(e)}}function fn(n){if(!n){var e="".concat(arguments.length>1&&arguments[1]!==void 0?arguments[1]:"This daily-js method"," is only supported on custom callObject instances");throw console.error(e),new Error(e)}}function Bt(n){if(n){var e="".concat(arguments.length>1&&arguments[1]!==void 0?arguments[1]:"This daily-js method"," is only supported as part of Daily's Prebuilt");throw console.error(e),new Error(e)}}function Te(){if(xe())throw new Error("This daily-js method is not currently supported in React Native")}function bs(){if(!xe())throw new Error("This daily-js method is only supported in React Native")}function wg(n){if(n===void 0)return!0;var e;if(typeof n=="string")e=n;else try{e=JSON.stringify(n),Et(JSON.parse(e),n)||console.warn("The userData provided will be modified when serialized.")}catch(i){throw Error("userData must be serializable to JSON: ".concat(i))}if(e.length>4096)throw Error("userData is too large (".concat(e.length," characters). Maximum size suppported is ").concat(4096,"."));return!0}function Sg(n,e){for(var i=e.allowAllParticipantsKey,s=function(E){var x=["local"];return i||x.push("*"),E&&!x.includes(E)},a=function(E){return!!(E.layer===void 0||Number.isInteger(E.layer)&&E.layer>=0||E.layer==="inherit")},c=function(E){return!!E&&!(E.video&&!a(E.video))&&!(E.screenVideo&&!a(E.screenVideo))},d=0,h=Object.entries(n);d<h.length;d++){var m=Mt(h[d],2),_=m[0],w=m[1];if(!s(_)||!c(w))return!1}return!0}function kg(n){if(Ne(n)!=="object")return!1;for(var e=0,i=Object.entries(n);e<i.length;e++){var s=Mt(i[e],2),a=s[0],c=s[1];switch(a){case"video":if(Ne(c)!=="object")return!1;for(var d=0,h=Object.entries(c);d<h.length;d++){var m=Mt(h[d],2),_=m[0],w=m[1];switch(_){case"processor":if(!Rk(w))return!1;break;case"settings":if(!cv(w))return!1;break;default:return!1}}break;case"audio":if(Ne(c)!=="object")return!1;for(var E=0,x=Object.entries(c);E<x.length;E++){var T=Mt(x[E],2),P=T[0],L=T[1];switch(P){case"processor":if(!jk(L))return!1;break;case"settings":if(!cv(L))return!1;break;default:return!1}}break;default:return!1}}return!0}function bg(n,e,i){var s,a=[];n.video&&n.video.processor&&(ag((s=e==null?void 0:e.useLegacyVideoProcessor)!==null&&s!==void 0&&s)||(n.video.settings?delete n.video.processor:delete n.video,a.push("video"))),n.audio&&n.audio.processor&&(lg()||(n.audio.settings?delete n.audio.processor:delete n.audio,a.push("audio"))),a.length>0&&console.error("Ignoring settings for browser- or platform-unsupported input processor(s): ".concat(a.join(", "))),n.audio&&n.audio.settings&&(n.audio.settings.customTrack?(i.audioTrack=n.audio.settings.customTrack,n.audio.settings={customTrack:Bn}):delete i.audioTrack),n.video&&n.video.settings&&(n.video.settings.customTrack?(i.videoTrack=n.video.settings.customTrack,n.video.settings={customTrack:Bn}):delete i.videoTrack)}function jk(n){if(xe())return console.warn("Video processing is not yet supported in React Native"),!1;var e=["type"];return!!n&&Ne(n)==="object"&&(Object.keys(n).filter(function(i){return!e.includes(i)}).forEach(function(i){console.warn("invalid key inputSettings -> audio -> processor : ".concat(i)),delete n[i]}),!!function(i){return typeof i!="string"?!1:Object.values(sg).includes(i)?!0:(console.error("inputSettings audio processor type invalid"),!1)}(n.type))}function Rk(n){if(xe())return console.warn("Video processing is not yet supported in React Native"),!1;var e=["type","config"];return!n||Ne(n)!=="object"||!function(i){return typeof i!="string"?!1:Object.values(ha).includes(i)?!0:(console.error("inputSettings video processor type invalid"),!1)}(n.type)||n.config&&(Ne(n.config)!=="object"||!function(i,s){var a=Object.keys(s);if(a.length===0)return!0;var c="invalid object in inputSettings -> video -> processor -> config";switch(i){case ha.BGBLUR:return a.length>1||a[0]!=="strength"?(console.error(c),!1):!(typeof s.strength!="number"||s.strength<=0||s.strength>1||isNaN(s.strength))||(console.error("".concat(c,"; expected: {0 < strength <= 1}, got: ").concat(s.strength)),!1);case ha.BGIMAGE:return!(s.source!==void 0&&!function(d){if(d.source==="default")return d.type="default",!0;if(d.source instanceof ArrayBuffer)return!0;if(_a(d.source))return d.type="url",!!function(_){var w=new URL(_),E=w.pathname;if(w.protocol==="data:")try{var x=E.substring(E.indexOf(":")+1,E.indexOf(";")).split("/")[1];return Xc.includes(x)}catch(P){return console.error("failed to deduce blob content type",P),!1}var T=E.split(".").at(-1).toLowerCase().trim();return Xc.includes(T)}(d.source)||(console.error("invalid image type; supported types: [".concat(Xc.join(", "),"]")),!1);return h=d.source,m=Number(h),isNaN(m)||!Number.isInteger(m)||m<=0||m>10?(console.error("invalid image selection; must be an int, > 0, <= ".concat(10)),!1):(d.type="daily-preselect",!0);var h,m}(s));default:return!0}}(n.type,n.config))?!1:(Object.keys(n).filter(function(i){return!e.includes(i)}).forEach(function(i){console.warn("invalid key inputSettings -> video -> processor : ".concat(i)),delete n[i]}),!0)}function cv(n){return Ne(n)==="object"&&(!n.customTrack||n.customTrack instanceof MediaStreamTrack)}function Lu(){var n=Object.values(ha).join(" | "),e=Object.values(sg).join(" | ");return"inputSettings must be of the form: { video?: { processor?: { type: [ ".concat(n," ], config?: {} } }, audio?: { processor: {type: [ ").concat(e," ] } } }")}function Eg(n){var e=n.allowAllParticipantsKey;return"receiveSettings must be of the form { [<remote participant id> | ".concat(aS).concat(e?' | "'.concat("*",'"'):"","]: ")+'{ [video: [{ layer: [<non-negative integer> | "inherit"] } | "inherit"]], [screenVideo: [{ layer: [<non-negative integer> | "inherit"] } | "inherit"]] }}}'}function Cg(){return"customIntegrations should be an object of type ".concat(JSON.stringify(Au),".")}function Mg(n){if(n&&Ne(n)!=="object"||Array.isArray(n))return console.error("customTrayButtons should be an Object of the type ".concat(JSON.stringify(Ma),".")),!1;if(n)for(var e=0,i=Object.entries(n);e<i.length;e++)for(var s=Mt(i[e],1)[0],a=0,c=Object.entries(n[s]);a<c.length;a++){var d=Mt(c[a],2),h=d[0],m=d[1],_=Ma.id[h];if(!_)return console.error("customTrayButton does not support key ".concat(h)),!1;switch(h){case"iconPath":case"iconPathDarkMode":if(!_a(m))return console.error("customTrayButton ".concat(h," should be a url.")),!1;break;case"visualState":if(!["default","sidebar-open","active"].includes(m))return console.error("customTrayButton ".concat(h," should be ").concat(_,". Got: ").concat(m)),!1;break;default:if(Ne(m)!==_)return console.error("customTrayButton ".concat(h," should be a ").concat(_,".")),!1}}return!0}function Tg(n){if(!n||n&&Ne(n)!=="object"||Array.isArray(n))return console.error(Cg()),!1;for(var e=function(E){return"".concat(E," should be ").concat(Au.id[E])},i=function(E,x){return console.error("customIntegration ".concat(E,": ").concat(x))},s=0,a=Object.entries(n);s<a.length;s++){var c=Mt(a[s],1)[0];if(!("label"in n[c]))return i(c,"label is required"),!1;if(!("location"in n[c]))return i(c,"location is required"),!1;if(!("src"in n[c])&&!("srcdoc"in n[c]))return i(c,"src or srcdoc is required"),!1;for(var d=0,h=Object.entries(n[c]);d<h.length;d++){var m=Mt(h[d],2),_=m[0],w=m[1];switch(_){case"allow":case"csp":case"name":case"referrerPolicy":case"sandbox":if(typeof w!="string")return i(c,e(_)),!1;break;case"iconURL":if(!_a(w))return i(c,"".concat(_," should be a url")),!1;break;case"src":if("srcdoc"in n[c])return i(c,"cannot have both src and srcdoc"),!1;if(!_a(w))return i(c,'src "'.concat(w,'" is not a valid URL')),!1;break;case"srcdoc":if("src"in n[c])return i(c,"cannot have both src and srcdoc"),!1;if(typeof w!="string")return i(c,e(_)),!1;break;case"location":if(!["main","sidebar"].includes(w))return i(c,e(_)),!1;break;case"controlledBy":if(w!=="*"&&w!=="owners"&&(!Array.isArray(w)||w.some(function(E){return typeof E!="string"})))return i(c,e(_)),!1;break;case"shared":if((!Array.isArray(w)||w.some(function(E){return typeof E!="string"}))&&w!=="owners"&&typeof w!="boolean")return i(c,e(_)),!1;break;default:if(!Au.id[_])return console.error("customIntegration does not support key ".concat(_)),!1}}}return!0}function Pg(n,e){if(e===void 0)return!1;switch(Ne(e)){case"string":return Ne(n)===e;case"object":if(Ne(n)!=="object")return!1;for(var i in n)if(!Pg(n[i],e[i]))return!1;return!0;default:return!1}}function uv(n,e){var i=n.sessionId,s=n.toEndPoint,a=n.callerId,c=n.useSipRefer;if(!i||!s)throw new Error("".concat(e,"() requires a sessionId and toEndPoint"));if(typeof i!="string"||typeof s!="string")throw new Error("Invalid paramater: sessionId and toEndPoint must be of type string");if(c&&!s.startsWith("sip:"))throw new Error('"toEndPoint" must be a "sip" address');if(!s.startsWith("sip:")&&!s.startsWith("+"))throw new Error("toEndPoint: ".concat(s,' must starts with either "sip:" or "+"'));if(a&&typeof a!="string")throw new Error("callerId must be of type string");if(a&&!s.startsWith("+"))throw new Error("callerId is only valid when transferring to a PSTN number")}function dv(n){if(Ne(n)!=="object")throw new Error('RemoteMediaPlayerSettings: must be "object" type');if(n.state&&!Object.values(Tu).includes(n.state))throw new Error("Invalid value for RemoteMediaPlayerSettings.state, valid values are: "+JSON.stringify(Tu));if(n.volume){if(typeof n.volume!="number")throw new Error('RemoteMediaPlayerSettings.volume: must be "number" type');if(n.volume<0||n.volume>2)throw new Error("RemoteMediaPlayerSettings.volume: must be between 0.0 - 2.0")}}function fv(n,e,i){return!(typeof n!="number"||n<e||n>i)}function iu(n,e){return n&&!e&&delete n.data,n}function Dk(n){return n&&n.__esModule?n.default:n}function Og(n,e,i,s){Object.defineProperty(n,e,{get:i,set:s,enumerable:!0,configurable:!0})}var xg={};Og(xg,"DailyRTVIMessageType",()=>Nu);Og(xg,"DailyTransport",()=>mr);class hn{static floatTo16BitPCM(e){const i=new ArrayBuffer(e.length*2),s=new DataView(i);let a=0;for(let c=0;c<e.length;c++,a+=2){let d=Math.max(-1,Math.min(1,e[c]));s.setInt16(a,d<0?d*32768:d*32767,!0)}return i}static mergeBuffers(e,i){const s=new Uint8Array(e.byteLength+i.byteLength);return s.set(new Uint8Array(e),0),s.set(new Uint8Array(i),e.byteLength),s.buffer}_packData(e,i){return[new Uint8Array([i,i>>8]),new Uint8Array([i,i>>8,i>>16,i>>24])][e]}pack(e,i){if(i!=null&&i.bitsPerSample)if(i!=null&&i.channels){if(!(i!=null&&i.data))throw new Error('Missing "data"')}else throw new Error('Missing "channels"');else throw new Error('Missing "bitsPerSample"');const{bitsPerSample:s,channels:a,data:c}=i,d=["RIFF",this._packData(1,52),"WAVE","fmt ",this._packData(1,16),this._packData(0,1),this._packData(0,a.length),this._packData(1,e),this._packData(1,e*a.length*s/8),this._packData(0,a.length*s/8),this._packData(0,s),"data",this._packData(1,a[0].length*a.length*s/8),c],h=new Blob(d,{type:"audio/mpeg"}),m=URL.createObjectURL(h);return{blob:h,url:m,channelCount:a.length,sampleRate:e,duration:c.byteLength/(a.length*e*2)}}}globalThis.WavPacker=hn;const pv=[4186.01,4434.92,4698.63,4978.03,5274.04,5587.65,5919.91,6271.93,6644.88,7040,7458.62,7902.13],Ik=["C","C#","D","D#","E","F","F#","G","G#","A","A#","B"],hr=[],id=[];for(let n=1;n<=8;n++)for(let e=0;e<pv.length;e++){const i=pv[e];hr.push(i/Math.pow(2,8-n)),id.push(Ik[e]+n)}const Ta=[32,2e3],hv=hr.filter((n,e)=>hr[e]>Ta[0]&&hr[e]<Ta[1]),Fk=id.filter((n,e)=>hr[e]>Ta[0]&&hr[e]<Ta[1]);class ji{static getFrequencies(e,i,s,a="frequency",c=-100,d=-30){s||(s=new Float32Array(e.frequencyBinCount),e.getFloatFrequencyData(s));const h=i/2,m=1/s.length*h;let _,w,E;if(a==="music"||a==="voice"){const P=a==="voice"?hv:hr,L=Array(P.length).fill(c);for(let F=0;F<s.length;F++){const Y=F*m,ne=s[F];for(let re=P.length-1;re>=0;re--)if(Y>P[re]){L[re]=Math.max(L[re],ne);break}}_=L,w=a==="voice"?hv:hr,E=a==="voice"?Fk:id}else _=Array.from(s),w=_.map((P,L)=>m*L),E=w.map(P=>`${P.toFixed(2)} Hz`);const x=_.map(P=>Math.max(0,Math.min((P-c)/(d-c),1)));return{values:new Float32Array(x),frequencies:w,labels:E}}constructor(e,i=null){if(this.fftResults=[],i){const{length:s,sampleRate:a}=i,c=new OfflineAudioContext({length:s,sampleRate:a}),d=c.createBufferSource();d.buffer=i;const h=c.createAnalyser();h.fftSize=8192,h.smoothingTimeConstant=.1,d.connect(h);const m=1/60,_=s/a,w=E=>{const x=m*E;x<_&&c.suspend(x).then(()=>{const T=new Float32Array(h.frequencyBinCount);h.getFloatFrequencyData(T),this.fftResults.push(T),w(E+1)}),E===1?c.startRendering():c.resume()};d.start(0),w(1),this.audio=e,this.context=c,this.analyser=h,this.sampleRate=a,this.audioBuffer=i}else{const s=new AudioContext,a=s.createMediaElementSource(e),c=s.createAnalyser();c.fftSize=8192,c.smoothingTimeConstant=.1,a.connect(c),c.connect(s.destination),this.audio=e,this.context=s,this.analyser=c,this.sampleRate=this.context.sampleRate,this.audioBuffer=null}}getFrequencies(e="frequency",i=-100,s=-30){let a=null;if(this.audioBuffer&&this.fftResults.length){const c=this.audio.currentTime/this.audio.duration,d=Math.min(c*this.fftResults.length|0,this.fftResults.length-1);a=this.fftResults[d]}return ji.getFrequencies(this.analyser,this.sampleRate,a,e,i,s)}async resumeIfSuspended(){return this.context.state==="suspended"&&await this.context.resume(),!0}}globalThis.AudioAnalysis=ji;const Bk=`
class StreamProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    this.hasStarted = false;
    this.hasInterrupted = false;
    this.outputBuffers = [];
    this.bufferLength = 128;
    this.write = { buffer: new Float32Array(this.bufferLength), trackId: null };
    this.writeOffset = 0;
    this.trackSampleOffsets = {};
    this.port.onmessage = (event) => {
      if (event.data) {
        const payload = event.data;
        if (payload.event === 'write') {
          const int16Array = payload.buffer;
          const float32Array = new Float32Array(int16Array.length);
          for (let i = 0; i < int16Array.length; i++) {
            float32Array[i] = int16Array[i] / 0x8000; // Convert Int16 to Float32
          }
          this.writeData(float32Array, payload.trackId);
        } else if (
          payload.event === 'offset' ||
          payload.event === 'interrupt'
        ) {
          const requestId = payload.requestId;
          const trackId = this.write.trackId;
          const offset = this.trackSampleOffsets[trackId] || 0;
          this.port.postMessage({
            event: 'offset',
            requestId,
            trackId,
            offset,
          });
          if (payload.event === 'interrupt') {
            this.hasInterrupted = true;
          }
        } else {
          throw new Error(\`Unhandled event "\${payload.event}"\`);
        }
      }
    };
  }

  writeData(float32Array, trackId = null) {
    let { buffer } = this.write;
    let offset = this.writeOffset;
    for (let i = 0; i < float32Array.length; i++) {
      buffer[offset++] = float32Array[i];
      if (offset >= buffer.length) {
        this.outputBuffers.push(this.write);
        this.write = { buffer: new Float32Array(this.bufferLength), trackId };
        buffer = this.write.buffer;
        offset = 0;
      }
    }
    this.writeOffset = offset;
    return true;
  }

  process(inputs, outputs, parameters) {
    const output = outputs[0];
    const outputChannelData = output[0];
    const outputBuffers = this.outputBuffers;
    if (this.hasInterrupted) {
      this.port.postMessage({ event: 'stop' });
      return false;
    } else if (outputBuffers.length) {
      this.hasStarted = true;
      const { buffer, trackId } = outputBuffers.shift();
      for (let i = 0; i < outputChannelData.length; i++) {
        outputChannelData[i] = buffer[i] || 0;
      }
      if (trackId) {
        this.trackSampleOffsets[trackId] =
          this.trackSampleOffsets[trackId] || 0;
        this.trackSampleOffsets[trackId] += buffer.length;
      }
      return true;
    } else if (this.hasStarted) {
      this.port.postMessage({ event: 'stop' });
      return false;
    } else {
      return true;
    }
  }
}

registerProcessor('stream_processor', StreamProcessor);
`,$k=new Blob([Bk],{type:"application/javascript"}),Uk=URL.createObjectURL($k),zk=Uk;class Vk{constructor({sampleRate:e=44100}={}){this.scriptSrc=zk,this.sampleRate=e,this.context=null,this.stream=null,this.analyser=null,this.trackSampleOffsets={},this.interruptedTrackIds={}}async connect(){this.context=new AudioContext({sampleRate:this.sampleRate}),this._speakerID&&this.context.setSinkId(this._speakerID),this.context.state==="suspended"&&await this.context.resume();try{await this.context.audioWorklet.addModule(this.scriptSrc)}catch(i){throw console.error(i),new Error(`Could not add audioWorklet module: ${this.scriptSrc}`)}const e=this.context.createAnalyser();return e.fftSize=8192,e.smoothingTimeConstant=.1,this.analyser=e,!0}getFrequencies(e="frequency",i=-100,s=-30){if(!this.analyser)throw new Error("Not connected, please call .connect() first");return ji.getFrequencies(this.analyser,this.sampleRate,null,e,i,s)}async updateSpeaker(e){const i=this._speakerID;if(this._speakerID=e,this.context)try{e==="default"?await this.context.setSinkId():await this.context.setSinkId(e)}catch(s){console.error(`Could not set sinkId to ${e}: ${s}`),this._speakerID=i}}_start(){const e=new AudioWorkletNode(this.context,"stream_processor");return e.connect(this.context.destination),e.port.onmessage=i=>{const{event:s}=i.data;if(s==="stop")e.disconnect(),this.stream=null;else if(s==="offset"){const{requestId:a,trackId:c,offset:d}=i.data,h=d/this.sampleRate;this.trackSampleOffsets[a]={trackId:c,offset:d,currentTime:h}}},this.analyser.disconnect(),e.connect(this.analyser),this.stream=e,!0}add16BitPCM(e,i="default"){if(typeof i!="string")throw new Error("trackId must be a string");if(this.interruptedTrackIds[i])return;this.stream||this._start();let s;if(e instanceof Int16Array)s=e;else if(e instanceof ArrayBuffer)s=new Int16Array(e);else throw new Error("argument must be Int16Array or ArrayBuffer");return this.stream.port.postMessage({event:"write",buffer:s,trackId:i}),s}async getTrackSampleOffset(e=!1){if(!this.stream)return null;const i=crypto.randomUUID();this.stream.port.postMessage({event:e?"interrupt":"offset",requestId:i});let s;for(;!s;)s=this.trackSampleOffsets[i],await new Promise(c=>setTimeout(()=>c(),1));const{trackId:a}=s;return e&&a&&(this.interruptedTrackIds[a]=!0),s}async interrupt(){return this.getTrackSampleOffset(!0)}}globalThis.WavStreamPlayer=Vk;const Wk=`
class AudioProcessor extends AudioWorkletProcessor {

  constructor() {
    super();
    this.port.onmessage = this.receive.bind(this);
    this.initialize();
  }

  initialize() {
    this.foundAudio = false;
    this.recording = false;
    this.chunks = [];
  }

  /**
   * Concatenates sampled chunks into channels
   * Format is chunk[Left[], Right[]]
   */
  readChannelData(chunks, channel = -1, maxChannels = 9) {
    let channelLimit;
    if (channel !== -1) {
      if (chunks[0] && chunks[0].length - 1 < channel) {
        throw new Error(
          \`Channel \${channel} out of range: max \${chunks[0].length}\`
        );
      }
      channelLimit = channel + 1;
    } else {
      channel = 0;
      channelLimit = Math.min(chunks[0] ? chunks[0].length : 1, maxChannels);
    }
    const channels = [];
    for (let n = channel; n < channelLimit; n++) {
      const length = chunks.reduce((sum, chunk) => {
        return sum + chunk[n].length;
      }, 0);
      const buffers = chunks.map((chunk) => chunk[n]);
      const result = new Float32Array(length);
      let offset = 0;
      for (let i = 0; i < buffers.length; i++) {
        result.set(buffers[i], offset);
        offset += buffers[i].length;
      }
      channels[n] = result;
    }
    return channels;
  }

  /**
   * Combines parallel audio data into correct format,
   * channels[Left[], Right[]] to float32Array[LRLRLRLR...]
   */
  formatAudioData(channels) {
    if (channels.length === 1) {
      // Simple case is only one channel
      const float32Array = channels[0].slice();
      const meanValues = channels[0].slice();
      return { float32Array, meanValues };
    } else {
      const float32Array = new Float32Array(
        channels[0].length * channels.length
      );
      const meanValues = new Float32Array(channels[0].length);
      for (let i = 0; i < channels[0].length; i++) {
        const offset = i * channels.length;
        let meanValue = 0;
        for (let n = 0; n < channels.length; n++) {
          float32Array[offset + n] = channels[n][i];
          meanValue += channels[n][i];
        }
        meanValues[i] = meanValue / channels.length;
      }
      return { float32Array, meanValues };
    }
  }

  /**
   * Converts 32-bit float data to 16-bit integers
   */
  floatTo16BitPCM(float32Array) {
    const buffer = new ArrayBuffer(float32Array.length * 2);
    const view = new DataView(buffer);
    let offset = 0;
    for (let i = 0; i < float32Array.length; i++, offset += 2) {
      let s = Math.max(-1, Math.min(1, float32Array[i]));
      view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
    }
    return buffer;
  }

  /**
   * Retrieves the most recent amplitude values from the audio stream
   * @param {number} channel
   */
  getValues(channel = -1) {
    const channels = this.readChannelData(this.chunks, channel);
    const { meanValues } = this.formatAudioData(channels);
    return { meanValues, channels };
  }

  /**
   * Exports chunks as an audio/wav file
   */
  export() {
    const channels = this.readChannelData(this.chunks);
    const { float32Array, meanValues } = this.formatAudioData(channels);
    const audioData = this.floatTo16BitPCM(float32Array);
    return {
      meanValues: meanValues,
      audio: {
        bitsPerSample: 16,
        channels: channels,
        data: audioData,
      },
    };
  }

  receive(e) {
    const { event, id } = e.data;
    let receiptData = {};
    switch (event) {
      case 'start':
        this.recording = true;
        break;
      case 'stop':
        this.recording = false;
        break;
      case 'clear':
        this.initialize();
        break;
      case 'export':
        receiptData = this.export();
        break;
      case 'read':
        receiptData = this.getValues();
        break;
      default:
        break;
    }
    // Always send back receipt
    this.port.postMessage({ event: 'receipt', id, data: receiptData });
  }

  sendChunk(chunk) {
    const channels = this.readChannelData([chunk]);
    const { float32Array, meanValues } = this.formatAudioData(channels);
    const rawAudioData = this.floatTo16BitPCM(float32Array);
    const monoAudioData = this.floatTo16BitPCM(meanValues);
    this.port.postMessage({
      event: 'chunk',
      data: {
        mono: monoAudioData,
        raw: rawAudioData,
      },
    });
  }

  process(inputList, outputList, parameters) {
    // Copy input to output (e.g. speakers)
    // Note that this creates choppy sounds with Mac products
    const sourceLimit = Math.min(inputList.length, outputList.length);
    for (let inputNum = 0; inputNum < sourceLimit; inputNum++) {
      const input = inputList[inputNum];
      const output = outputList[inputNum];
      const channelCount = Math.min(input.length, output.length);
      for (let channelNum = 0; channelNum < channelCount; channelNum++) {
        input[channelNum].forEach((sample, i) => {
          output[channelNum][i] = sample;
        });
      }
    }
    const inputs = inputList[0];
    // There's latency at the beginning of a stream before recording starts
    // Make sure we actually receive audio data before we start storing chunks
    let sliceIndex = 0;
    if (!this.foundAudio) {
      for (const channel of inputs) {
        sliceIndex = 0; // reset for each channel
        if (this.foundAudio) {
          break;
        }
        if (channel) {
          for (const value of channel) {
            if (value !== 0) {
              // find only one non-zero entry in any channel
              this.foundAudio = true;
              break;
            } else {
              sliceIndex++;
            }
          }
        }
      }
    }
    if (inputs && inputs[0] && this.foundAudio && this.recording) {
      // We need to copy the TypedArray, because the \`process\`
      // internals will reuse the same buffer to hold each input
      const chunk = inputs.map((input) => input.slice(sliceIndex));
      this.chunks.push(chunk);
      this.sendChunk(chunk);
    }
    return true;
  }
}

registerProcessor('audio_processor', AudioProcessor);
`,qk=new Blob([Wk],{type:"application/javascript"}),Jk=URL.createObjectURL(qk),Ag=Jk;class Hk{constructor({sampleRate:e=44100,outputToSpeakers:i=!1,debug:s=!1}={}){this.scriptSrc=Ag,this.sampleRate=e,this.outputToSpeakers=i,this.debug=!!s,this._deviceChangeCallback=null,this._devices=[],this.deviceSelection=null,this.stream=null,this.processor=null,this.source=null,this.node=null,this.recording=!1,this._lastEventId=0,this.eventReceipts={},this.eventTimeout=5e3,this._chunkProcessor=()=>{},this._chunkProcessorSize=void 0,this._chunkProcessorBuffer={raw:new ArrayBuffer(0),mono:new ArrayBuffer(0)}}static async decode(e,i=44100,s=-1){const a=new AudioContext({sampleRate:i});let c,d;if(e instanceof Blob){if(s!==-1)throw new Error('Can not specify "fromSampleRate" when reading from Blob');d=e,c=await d.arrayBuffer()}else if(e instanceof ArrayBuffer){if(s!==-1)throw new Error('Can not specify "fromSampleRate" when reading from ArrayBuffer');c=e,d=new Blob([c],{type:"audio/wav"})}else{let w,E;if(e instanceof Int16Array){E=e,w=new Float32Array(e.length);for(let L=0;L<e.length;L++)w[L]=e[L]/32768}else if(e instanceof Float32Array)w=e;else if(e instanceof Array)w=new Float32Array(e);else throw new Error('"audioData" must be one of: Blob, Float32Arrray, Int16Array, ArrayBuffer, Array<number>');if(s===-1)throw new Error('Must specify "fromSampleRate" when reading from Float32Array, In16Array or Array');if(s<3e3)throw new Error('Minimum "fromSampleRate" is 3000 (3kHz)');E||(E=hn.floatTo16BitPCM(w));const x={bitsPerSample:16,channels:[w],data:E};d=new hn().pack(s,x).blob,c=await d.arrayBuffer()}const h=await a.decodeAudioData(c),m=h.getChannelData(0),_=URL.createObjectURL(d);return{blob:d,url:_,values:m,audioBuffer:h}}log(){return this.debug&&this.log(...arguments),!0}getSampleRate(){return this.sampleRate}getStatus(){return this.processor?this.recording?"recording":"paused":"ended"}async _event(e,i={},s=null){if(s=s||this.processor,!s)throw new Error("Can not send events without recording first");const a={event:e,id:this._lastEventId++,data:i};s.port.postMessage(a);const c=new Date().valueOf();for(;!this.eventReceipts[a.id];){if(new Date().valueOf()-c>this.eventTimeout)throw new Error(`Timeout waiting for "${e}" event`);await new Promise(h=>setTimeout(()=>h(!0),1))}const d=this.eventReceipts[a.id];return delete this.eventReceipts[a.id],d}listenForDeviceChange(e){if(e===null&&this._deviceChangeCallback)navigator.mediaDevices.removeEventListener("devicechange",this._deviceChangeCallback),this._deviceChangeCallback=null;else if(e!==null){let i=0,s=[];const a=d=>d.map(h=>h.deviceId).sort().join(","),c=async()=>{let d=++i;const h=await this.listDevices();d===i&&a(s)!==a(h)&&(s=h,e(h.slice()))};navigator.mediaDevices.addEventListener("devicechange",c),c(),this._deviceChangeCallback=c}return!0}async requestPermission(){const e=await navigator.permissions.query({name:"microphone"});if(e.state==="denied")window.alert("You must grant microphone access to use this feature.");else if(e.state==="prompt")try{(await navigator.mediaDevices.getUserMedia({audio:!0})).getTracks().forEach(a=>a.stop())}catch{window.alert("You must grant microphone access to use this feature.")}return!0}async listDevices(){if(!navigator.mediaDevices||!("enumerateDevices"in navigator.mediaDevices))throw new Error("Could not request user devices");return await this.requestPermission(),(await navigator.mediaDevices.enumerateDevices()).filter(s=>s.kind==="audioinput")}async begin(e){var h;if(this.processor)throw new Error("Already connected: please call .end() to start a new session");if(!navigator.mediaDevices||!("getUserMedia"in navigator.mediaDevices))throw new Error("Could not request user media");e=e??((h=this.deviceSelection)==null?void 0:h.deviceId);try{const m={audio:!0};e&&(m.audio={deviceId:{exact:e}}),this.stream=await navigator.mediaDevices.getUserMedia(m)}catch{throw new Error("Could not start media stream")}this.listDevices().then(m=>{e=this.stream.getAudioTracks()[0].getSettings().deviceId,console.log("find current device",m,e,this.stream.getAudioTracks()[0].getSettings()),this.deviceSelection=m.find(_=>_.deviceId===e),console.log("current device",this.deviceSelection)});const i=new AudioContext({sampleRate:this.sampleRate}),s=i.createMediaStreamSource(this.stream);try{await i.audioWorklet.addModule(this.scriptSrc)}catch(m){throw console.error(m),new Error(`Could not add audioWorklet module: ${this.scriptSrc}`)}const a=new AudioWorkletNode(i,"audio_processor");a.port.onmessage=m=>{const{event:_,id:w,data:E}=m.data;if(_==="receipt")this.eventReceipts[w]=E;else if(_==="chunk")if(this._chunkProcessorSize){const x=this._chunkProcessorBuffer;this._chunkProcessorBuffer={raw:hn.mergeBuffers(x.raw,E.raw),mono:hn.mergeBuffers(x.mono,E.mono)},this._chunkProcessorBuffer.mono.byteLength>=this._chunkProcessorSize&&(this._chunkProcessor(this._chunkProcessorBuffer),this._chunkProcessorBuffer={raw:new ArrayBuffer(0),mono:new ArrayBuffer(0)})}else this._chunkProcessor(E)};const c=s.connect(a),d=i.createAnalyser();return d.fftSize=8192,d.smoothingTimeConstant=.1,c.connect(d),this.outputToSpeakers&&(console.warn(`Warning: Output to speakers may affect sound quality,
especially due to system audio feedback preventative measures.
use only for debugging`),d.connect(i.destination)),this.source=s,this.node=c,this.analyser=d,this.processor=a,console.log("begin completed"),!0}getFrequencies(e="frequency",i=-100,s=-30){if(!this.processor)throw new Error("Session ended: please call .begin() first");return ji.getFrequencies(this.analyser,this.sampleRate,null,e,i,s)}async pause(){if(this.processor){if(!this.recording)throw new Error("Already paused: please call .record() first")}else throw new Error("Session ended: please call .begin() first");return this._chunkProcessorBuffer.raw.byteLength&&this._chunkProcessor(this._chunkProcessorBuffer),this.log("Pausing ..."),await this._event("stop"),this.recording=!1,!0}async record(e=()=>{},i=8192){if(this.processor){if(this.recording)throw new Error("Already recording: please call .pause() first");if(typeof e!="function")throw new Error("chunkProcessor must be a function")}else throw new Error("Session ended: please call .begin() first");return this._chunkProcessor=e,this._chunkProcessorSize=i,this._chunkProcessorBuffer={raw:new ArrayBuffer(0),mono:new ArrayBuffer(0)},this.log("Recording ..."),await this._event("start"),this.recording=!0,!0}async clear(){if(!this.processor)throw new Error("Session ended: please call .begin() first");return await this._event("clear"),!0}async read(){if(!this.processor)throw new Error("Session ended: please call .begin() first");return this.log("Reading ..."),await this._event("read")}async save(e=!1){if(!this.processor)throw new Error("Session ended: please call .begin() first");if(!e&&this.recording)throw new Error("Currently recording: please call .pause() first, or call .save(true) to force");this.log("Exporting ...");const i=await this._event("export");return new hn().pack(this.sampleRate,i.audio)}async end(){if(!this.processor)throw new Error("Session ended: please call .begin() first");const e=this.processor;this.log("Stopping ..."),await this._event("stop"),this.recording=!1,this.stream.getTracks().forEach(d=>d.stop()),this.log("Exporting ...");const s=await this._event("export",{},e);return this.processor.disconnect(),this.source.disconnect(),this.node.disconnect(),this.analyser.disconnect(),this.stream=null,this.processor=null,this.source=null,this.node=null,new hn().pack(this.sampleRate,s.audio)}async quit(){return this.listenForDeviceChange(null),this.deviceSelection=null,this.processor&&await this.end(),!0}}globalThis.WavRecorder=Hk;class Qk{constructor({sampleRate:e=44100,outputToSpeakers:i=!1,debug:s=!1}={}){this.scriptSrc=Ag,this.sampleRate=e,this.outputToSpeakers=i,this.debug=!!s,this.stream=null,this.processor=null,this.source=null,this.node=null,this.recording=!1,this._lastEventId=0,this.eventReceipts={},this.eventTimeout=5e3,this._chunkProcessor=()=>{},this._chunkProcessorSize=void 0,this._chunkProcessorBuffer={raw:new ArrayBuffer(0),mono:new ArrayBuffer(0)}}log(){return this.debug&&this.log(...arguments),!0}getSampleRate(){return this.sampleRate}getStatus(){return this.processor?this.recording?"recording":"paused":"ended"}async _event(e,i={},s=null){if(s=s||this.processor,!s)throw new Error("Can not send events without recording first");const a={event:e,id:this._lastEventId++,data:i};s.port.postMessage(a);const c=new Date().valueOf();for(;!this.eventReceipts[a.id];){if(new Date().valueOf()-c>this.eventTimeout)throw new Error(`Timeout waiting for "${e}" event`);await new Promise(h=>setTimeout(()=>h(!0),1))}const d=this.eventReceipts[a.id];return delete this.eventReceipts[a.id],d}async begin(e){if(this.processor)throw new Error("Already connected: please call .end() to start a new session");if(!e||e.kind!=="audio")throw new Error("No audio track provided");this.stream=new MediaStream([e]);const i=new AudioContext({sampleRate:this.sampleRate}),s=i.createMediaStreamSource(this.stream);try{await i.audioWorklet.addModule(this.scriptSrc)}catch(h){throw console.error(h),new Error(`Could not add audioWorklet module: ${this.scriptSrc}`)}const a=new AudioWorkletNode(i,"audio_processor");a.port.onmessage=h=>{const{event:m,id:_,data:w}=h.data;if(m==="receipt")this.eventReceipts[_]=w;else if(m==="chunk")if(this._chunkProcessorSize){const E=this._chunkProcessorBuffer;this._chunkProcessorBuffer={raw:hn.mergeBuffers(E.raw,w.raw),mono:hn.mergeBuffers(E.mono,w.mono)},this._chunkProcessorBuffer.mono.byteLength>=this._chunkProcessorSize&&(this._chunkProcessor(this._chunkProcessorBuffer),this._chunkProcessorBuffer={raw:new ArrayBuffer(0),mono:new ArrayBuffer(0)})}else this._chunkProcessor(w)};const c=s.connect(a),d=i.createAnalyser();return d.fftSize=8192,d.smoothingTimeConstant=.1,c.connect(d),this.outputToSpeakers&&(console.warn(`Warning: Output to speakers may affect sound quality,
especially due to system audio feedback preventative measures.
use only for debugging`),d.connect(i.destination)),this.source=s,this.node=c,this.analyser=d,this.processor=a,!0}getFrequencies(e="frequency",i=-100,s=-30){if(!this.processor)throw new Error("Session ended: please call .begin() first");return ji.getFrequencies(this.analyser,this.sampleRate,null,e,i,s)}async pause(){if(this.processor){if(!this.recording)throw new Error("Already paused: please call .record() first")}else throw new Error("Session ended: please call .begin() first");return this._chunkProcessorBuffer.raw.byteLength&&this._chunkProcessor(this._chunkProcessorBuffer),this.log("Pausing ..."),await this._event("stop"),this.recording=!1,!0}async record(e=()=>{},i=8192){if(this.processor){if(this.recording)throw new Error("Already recording: HELLO please call .pause() first");if(typeof e!="function")throw new Error("chunkProcessor must be a function")}else throw new Error("Session ended: please call .begin() first");return this._chunkProcessor=e,this._chunkProcessorSize=i,this._chunkProcessorBuffer={raw:new ArrayBuffer(0),mono:new ArrayBuffer(0)},this.log("Recording ..."),await this._event("start"),this.recording=!0,!0}async clear(){if(!this.processor)throw new Error("Session ended: please call .begin() first");return await this._event("clear"),!0}async read(){if(!this.processor)throw new Error("Session ended: please call .begin() first");return this.log("Reading ..."),await this._event("read")}async save(e=!1){if(!this.processor)throw new Error("Session ended: please call .begin() first");if(!e&&this.recording)throw new Error("Currently recording: please call .pause() first, or call .save(true) to force");this.log("Exporting ...");const i=await this._event("export");return new hn().pack(this.sampleRate,i.audio)}async end(){if(!this.processor)throw new Error("Session ended: please call .begin() first");const e=this.processor;this.log("Stopping ..."),await this._event("stop"),this.recording=!1,this.log("Exporting ...");const i=await this._event("export",{},e);return this.processor.disconnect(),this.source.disconnect(),this.node.disconnect(),this.analyser.disconnect(),this.stream=null,this.processor=null,this.source=null,this.node=null,new hn().pack(this.sampleRate,i.audio)}async quit(){return this.listenForDeviceChange(null),this.processor&&await this.end(),!0}}globalThis.WavRecorder=WavRecorder;var Lg={};Lg=JSON.parse('{"name":"@pipecat-ai/daily-transport","version":"1.0.0","license":"BSD-2-Clause","main":"dist/index.js","module":"dist/index.module.js","types":"dist/index.d.ts","source":"src/index.ts","repository":{"type":"git","url":"git+https://github.com/pipecat-ai/pipecat-client-web-transports.git"},"files":["dist","package.json","README.md"],"scripts":{"build":"parcel build --no-cache","dev":"parcel watch","lint":"eslint . --ext ts --report-unused-disable-directives --max-warnings 0"},"devDependencies":{"@pipecat-ai/client-js":"^1.0.0","eslint":"9.11.1","eslint-config-prettier":"^9.1.0","eslint-plugin-simple-import-sort":"^12.1.1"},"peerDependencies":{"@pipecat-ai/client-js":"~1.0.0"},"dependencies":{"@daily-co/daily-js":"^0.77.0"},"description":"Pipecat Daily Transport Package","author":"Daily.co","bugs":{"url":"https://github.com/pipecat-ai/pipecat-client-web-transports/issues"},"homepage":"https://github.com/pipecat-ai/pipecat-client-web-transports/blob/main/transports/daily-webrtc/README.md"}');var Nu;(function(n){n.AUDIO_BUFFERING_STARTED="audio-buffering-started",n.AUDIO_BUFFERING_STOPPED="audio-buffering-stopped"})(Nu||(Nu={}));class Gk{constructor(e){this._daily=e,this._proxy=new Proxy(this._daily,{get:(i,s,a)=>{if(typeof i[s]=="function"){let c;switch(String(s)){case"preAuth":c="Calls to preAuth() are disabled. Please use Transport.preAuth()";break;case"startCamera":c="Calls to startCamera() are disabled. Please use PipecatClient.initDevices()";break;case"join":c="Calls to join() are disabled. Please use PipecatClient.connect()";break;case"leave":c="Calls to leave() are disabled. Please use PipecatClient.disconnect()";break;case"destroy":c="Calls to destroy() are disabled.";break}return c?()=>{throw new Error(c)}:(...d)=>i[s](...d)}return Reflect.get(i,s,a)}})}get proxy(){return this._proxy}}class mr extends Ev{constructor(e={}){super(),this._botId="",this._selectedCam={},this._selectedMic={},this._selectedSpeaker={},this._currentAudioTrack=null,this._audioQueue=[],this._callbacks={};const{bufferLocalAudioUntilBotReady:i,...s}=e;this._dailyFactoryOptions=s,this._bufferLocalAudioUntilBotReady=i||!1,this._daily=Nk.createCallObject({...this._dailyFactoryOptions,allowMultipleCallInstances:!0}),this._dailyWrapper=new Gk(this._daily)}setupRecorder(){this._mediaStreamRecorder=new Qk({sampleRate:mr.RECORDER_SAMPLE_RATE})}handleUserAudioStream(e){this._audioQueue.push(e)}flushAudioQueue(){if(this._audioQueue.length!==0)for(Be.debug(`Will flush audio queue: ${this._audioQueue.length}`);this._audioQueue.length>0;){const i=[];for(;i.length<10&&this._audioQueue.length>0;){const s=this._audioQueue.shift();s&&i.push(s)}i.length>0&&this._sendAudioBatch(i)}}_sendAudioBatch(e){const s={id:"raw-audio-batch",label:"rtvi-ai",type:"raw-audio-batch",data:{base64AudioBatch:e.map(a=>{const c=new Uint8Array(a);return btoa(String.fromCharCode(...c))}),sampleRate:mr.RECORDER_SAMPLE_RATE,numChannels:1}};this.sendMessage(s)}initialize(e,i){this._bufferLocalAudioUntilBotReady&&this.setupRecorder(),this._callbacks=e.callbacks??{},this._onMessage=i,(this._dailyFactoryOptions.startVideoOff==null||e.enableCam!=null)&&(this._dailyFactoryOptions.startVideoOff=!(e.enableCam??!1)),(this._dailyFactoryOptions.startAudioOff==null||e.enableMic!=null)&&(this._dailyFactoryOptions.startAudioOff=!(e.enableMic??!0)),this.attachEventListeners(),this.state="disconnected",Be.debug("[Daily Transport] Initialized",Dk(Lg).version)}get dailyCallClient(){return this._dailyWrapper.proxy}get state(){return this._state}set state(e){var i,s;this._state!==e&&(this._state=e,(s=(i=this._callbacks).onTransportStateChanged)==null||s.call(i,e))}getSessionInfo(){return this._daily.meetingSessionSummary()}async getAllCams(){const{devices:e}=await this._daily.enumerateDevices();return e.filter(i=>i.kind==="videoinput")}updateCam(e){this._daily.setInputDevicesAsync({videoDeviceId:e}).then(i=>{this._selectedCam=i.camera})}get selectedCam(){return this._selectedCam}async getAllMics(){const{devices:e}=await this._daily.enumerateDevices();return e.filter(i=>i.kind==="audioinput")}updateMic(e){this._daily.setInputDevicesAsync({audioDeviceId:e}).then(i=>{this._selectedMic=i.mic})}get selectedMic(){return this._selectedMic}async getAllSpeakers(){const{devices:e}=await this._daily.enumerateDevices();return e.filter(i=>i.kind==="audiooutput")}updateSpeaker(e){this._daily.setOutputDeviceAsync({outputDeviceId:e}).then(i=>{this._selectedSpeaker=i.speaker})}get selectedSpeaker(){return this._selectedSpeaker}enableMic(e){this._daily.setLocalAudio(e)}get isMicEnabled(){return this._daily.localAudio()}enableCam(e){this._daily.setLocalVideo(e)}get isCamEnabled(){return this._daily.localVideo()}enableScreenShare(e){e?this._daily.startScreenShare():this._daily.stopScreenShare()}get isSharingScreen(){return this._daily.localScreenAudio()||this._daily.localScreenVideo()}tracks(){var a,c,d,h,m,_,w,E,x,T,P,L,F,Y,ne,re;const e=this._daily.participants()??{},i=e==null?void 0:e[this._botId],s={local:{audio:(d=(c=(a=e==null?void 0:e.local)==null?void 0:a.tracks)==null?void 0:c.audio)==null?void 0:d.persistentTrack,screenAudio:(_=(m=(h=e==null?void 0:e.local)==null?void 0:h.tracks)==null?void 0:m.screenAudio)==null?void 0:_.persistentTrack,screenVideo:(x=(E=(w=e==null?void 0:e.local)==null?void 0:w.tracks)==null?void 0:E.screenVideo)==null?void 0:x.persistentTrack,video:(L=(P=(T=e==null?void 0:e.local)==null?void 0:T.tracks)==null?void 0:P.video)==null?void 0:L.persistentTrack}};return i&&(s.bot={audio:(Y=(F=i==null?void 0:i.tracks)==null?void 0:F.audio)==null?void 0:Y.persistentTrack,video:(re=(ne=i==null?void 0:i.tracks)==null?void 0:ne.video)==null?void 0:re.persistentTrack}),s}async startRecording(){var e,i;try{Be.info("[Daily Transport] Initializing recording"),await this._mediaStreamRecorder.record(s=>{this.handleUserAudioStream(s.mono)},mr.RECORDER_CHUNK_SIZE),(i=(e=this._callbacks).onAudioBufferingStarted)==null||i.call(e),Be.info("[Daily Transport] Recording Initialized")}catch(s){s.message.includes("Already recording")||Be.error("Error starting recording",s)}}async preAuth(e){this._dailyFactoryOptions=e,await this._daily.preAuth(e)}async initDevices(){var d,h,m,_,w,E,x,T,P,L,F,Y;if(!this._daily)throw new mn("Transport instance not initialized");this.state="initializing";const e=await this._daily.startCamera(this._dailyFactoryOptions),{devices:i}=await this._daily.enumerateDevices(),s=i.filter(ne=>ne.kind==="videoinput"),a=i.filter(ne=>ne.kind==="audioinput"),c=i.filter(ne=>ne.kind==="audiooutput");this._selectedCam=e.camera,this._selectedMic=e.mic,this._selectedSpeaker=e.speaker,(h=(d=this._callbacks).onAvailableCamsUpdated)==null||h.call(d,s),(_=(m=this._callbacks).onAvailableMicsUpdated)==null||_.call(m,a),(E=(w=this._callbacks).onAvailableSpeakersUpdated)==null||E.call(w,c),(T=(x=this._callbacks).onCamUpdated)==null||T.call(x,e.camera),(L=(P=this._callbacks).onMicUpdated)==null||L.call(P,e.mic),(Y=(F=this._callbacks).onSpeakerUpdated)==null||Y.call(F,e.speaker),this._daily.isLocalAudioLevelObserverRunning()||await this._daily.startLocalAudioLevelObserver(100),this._daily.isRemoteParticipantsAudioLevelObserverRunning()||await this._daily.startRemoteParticipantsAudioLevelObserver(100),this.state="initialized"}_validateConnectionParams(e){if(e==null)return;if(typeof e!="object")throw new mn("Invalid connection parameters");const i=e;return i.room_url&&(i.url=i.room_url,delete i.room_url),i.token||delete i.token,i}async _connect(e){var i,s,a;if(!this._daily)throw new mn("Transport instance not initialized");e&&(this._dailyFactoryOptions={...this._dailyFactoryOptions,...e}),this.state="connecting";try{await this._daily.join(this._dailyFactoryOptions)}catch(c){throw Be.error("Failed to join room",c),this.state="error",new gv}(i=this._abortController)!=null&&i.signal.aborted||(this.state="connected",(a=(s=this._callbacks).onConnected)==null||a.call(s))}async sendReadyMessage(){return new Promise(e=>{var c,d;const i=()=>{const h=navigator.userAgent;return/iPad|iPhone|iPod/.test(h)||/Macintosh/.test(h)&&"ontouchend"in document},s=()=>{this.state="ready",this.flushAudioQueue(),this.sendMessage(Xt.clientReady()),this.stopRecording(),e()};for(const h in this._daily.participants()){const m=this._daily.participants()[h];if(!m.local&&((d=(c=m.tracks)==null?void 0:c.audio)!=null&&d.persistentTrack)){s(),e();return}}const a=h=>{var m;(m=h.participant)!=null&&m.local||(this._daily.off("track-started",a),i()?(Be.debug("[Daily Transport] iOS device detected, adding 0.5 second delay before sending ready message"),setTimeout(s,500)):s())};this._daily.on("track-started",a)})}stopRecording(){var e,i;this._mediaStreamRecorder&&this._mediaStreamRecorder.getStatus()!=="ended"&&(this._mediaStreamRecorder.end(),(i=(e=this._callbacks).onAudioBufferingStopped)==null||i.call(e))}attachEventListeners(){this._daily.on("available-devices-updated",this.handleAvailableDevicesUpdated.bind(this)),this._daily.on("selected-devices-updated",this.handleSelectedDevicesUpdated.bind(this)),this._daily.on("track-started",this.handleTrackStarted.bind(this)),this._daily.on("track-stopped",this.handleTrackStopped.bind(this)),this._daily.on("participant-joined",this.handleParticipantJoined.bind(this)),this._daily.on("participant-left",this.handleParticipantLeft.bind(this)),this._daily.on("local-audio-level",this.handleLocalAudioLevel.bind(this)),this._daily.on("remote-participants-audio-level",this.handleRemoteAudioLevel.bind(this)),this._daily.on("app-message",this.handleAppMessage.bind(this)),this._daily.on("left-meeting",this.handleLeftMeeting.bind(this)),this._daily.on("error",this.handleFatalError.bind(this)),this._daily.on("nonfatal-error",this.handleNonFatalError.bind(this))}async _disconnect(){this.state="disconnecting",this._daily.stopLocalAudioLevelObserver(),this._daily.stopRemoteParticipantsAudioLevelObserver(),this._audioQueue=[],this._currentAudioTrack=null,this.stopRecording(),await this._daily.leave()}sendMessage(e){this._daily.sendAppMessage(e,"*")}handleAppMessage(e){e.data.label==="rtvi-ai"&&this._onMessage({id:e.data.id,type:e.data.type,data:e.data.data})}handleAvailableDevicesUpdated(e){var i,s,a,c,d,h;(s=(i=this._callbacks).onAvailableCamsUpdated)==null||s.call(i,e.availableDevices.filter(m=>m.kind==="videoinput")),(c=(a=this._callbacks).onAvailableMicsUpdated)==null||c.call(a,e.availableDevices.filter(m=>m.kind==="audioinput")),(h=(d=this._callbacks).onAvailableSpeakersUpdated)==null||h.call(d,e.availableDevices.filter(m=>m.kind==="audiooutput"))}handleSelectedDevicesUpdated(e){var i,s,a,c,d,h,m,_,w;((i=this._selectedCam)==null?void 0:i.deviceId)!==e.devices.camera&&(this._selectedCam=e.devices.camera,(a=(s=this._callbacks).onCamUpdated)==null||a.call(s,e.devices.camera)),((c=this._selectedMic)==null?void 0:c.deviceId)!==e.devices.mic&&(this._selectedMic=e.devices.mic,(h=(d=this._callbacks).onMicUpdated)==null||h.call(d,e.devices.mic)),((m=this._selectedSpeaker)==null?void 0:m.deviceId)!==e.devices.speaker&&(this._selectedSpeaker=e.devices.speaker,(w=(_=this._callbacks).onSpeakerUpdated)==null||w.call(_,e.devices.speaker))}async handleLocalAudioTrack(e){if(this.state=="ready"||!this._bufferLocalAudioUntilBotReady)return;switch(this._mediaStreamRecorder.getStatus()){case"ended":await this._mediaStreamRecorder.begin(e),await this.startRecording();break;case"paused":await this.startRecording();break;case"recording":default:this._currentAudioTrack!==e?(await this._mediaStreamRecorder.end(),await this._mediaStreamRecorder.begin(e),await this.startRecording()):Be.warn("track-started event received for current track and already recording");break}this._currentAudioTrack=e}handleTrackStarted(e){var i,s,a,c,d;e.type==="screenAudio"||e.type==="screenVideo"?(s=(i=this._callbacks).onScreenTrackStarted)==null||s.call(i,e.track,e.participant?Dr(e.participant):void 0):((a=e.participant)!=null&&a.local&&e.track.kind==="audio"&&this.handleLocalAudioTrack(e.track),(d=(c=this._callbacks).onTrackStarted)==null||d.call(c,e.track,e.participant?Dr(e.participant):void 0))}handleTrackStopped(e){var i,s,a,c;e.type==="screenAudio"||e.type==="screenVideo"?(s=(i=this._callbacks).onScreenTrackStopped)==null||s.call(i,e.track,e.participant?Dr(e.participant):void 0):(c=(a=this._callbacks).onTrackStopped)==null||c.call(a,e.track,e.participant?Dr(e.participant):void 0)}handleParticipantJoined(e){var s,a,c,d;const i=Dr(e.participant);(a=(s=this._callbacks).onParticipantJoined)==null||a.call(s,i),!i.local&&(this._botId=e.participant.session_id,(d=(c=this._callbacks).onBotConnected)==null||d.call(c,i))}handleParticipantLeft(e){var s,a,c,d;const i=Dr(e.participant);(a=(s=this._callbacks).onParticipantLeft)==null||a.call(s,i),!i.local&&(this._botId="",(d=(c=this._callbacks).onBotDisconnected)==null||d.call(c,i))}handleLocalAudioLevel(e){var i,s;(s=(i=this._callbacks).onLocalAudioLevel)==null||s.call(i,e.audioLevel)}handleRemoteAudioLevel(e){var a,c;const i=this._daily.participants(),s=Object.keys(e.participantsAudioLevel);for(let d=0;d<s.length;d++){const h=s[d],m=e.participantsAudioLevel[h];(c=(a=this._callbacks).onRemoteAudioLevel)==null||c.call(a,m,Dr(i[h]))}}handleLeftMeeting(){var e,i;this.state="disconnected",this._botId="",(i=(e=this._callbacks).onDisconnected)==null||i.call(e)}handleFatalError(e){var i,s;Be.error("Daily fatal error",e.errorMsg),this.state="error",this._botId="",(s=(i=this._callbacks).onError)==null||s.call(i,Xt.error(e.errorMsg,!0))}handleNonFatalError(e){var i,s;switch(e.type){case"screen-share-error":(s=(i=this._callbacks).onScreenShareError)==null||s.call(i,e.errorMsg);break}}}mr.RECORDER_SAMPLE_RATE=16e3;mr.RECORDER_CHUNK_SIZE=512;const Dr=n=>({id:n.user_id,local:n.local,name:n.user_name}),Rs=new Li({transport:new mr,enableMic:!0,enableCam:!1});Rs.on("transportStateChanged",n=>{console.log("Transport state changed:",n)});Rs.on("connected",()=>{console.log("Client connected successfully")});Rs.on("disconnected",()=>{console.log("Client disconnected")});Rs.on("error",n=>{console.error("Client error:",n)});function Kk({children:n}){return Le.jsx(Rv,{client:Rs,children:n})}function Yk(){const n=Uu(),e=Vu(),i=["connected","ready"].includes(e),[s,a]=ke.useState(null),c=async()=>{if(!n){console.error("Pipecat client is not initialized"),a("客户端未初始化");return}a(null),console.log("Current transport state:",e);try{if(i)console.log("Disconnecting..."),await n.disconnect();else{console.log("Connecting to:","https://su.guiyunai.fun/api/connect");try{await navigator.mediaDevices.getUserMedia({audio:!0}),console.log("Microphone permission granted")}catch(d){console.error("Microphone permission denied:",d),a("需要麦克风权限才能进行语音对话");return}await n.connect({endpoint:"https://su.guiyunai.fun/api/connect",config:{enableMic:!0,enableCam:!1}}),console.log("Connection initiated")}}catch(d){console.error("Connection error:",d),a(`连接错误: ${d instanceof Error?d.message:"未知错误"}`)}};return Le.jsxs("div",{className:"controls",children:[Le.jsx("button",{className:i?"disconnect-btn":"connect-btn",onClick:c,disabled:!n||["connecting","disconnecting"].includes(e),children:e==="connecting"?"连接中...":e==="disconnecting"?"断开中...":i?"断开连接":"连接"}),s&&Le.jsx("div",{style:{color:"red",marginTop:"10px",fontSize:"14px"},children:s}),Le.jsxs("div",{style:{marginTop:"10px",fontSize:"12px",color:"#666"},children:["状态: ",e]})]})}function Xk(){const n=Vu();return Le.jsxs("div",{className:"status",children:["Status: ",Le.jsx("span",{children:n})]})}function Zk(){const n=ke.useRef(null),e=Uu(),i=ke.useCallback(s=>{if(!n.current)return;const a=document.createElement("div");a.textContent=`${new Date().toISOString()} - ${s}`,s.startsWith("User: ")?a.style.color="#2196F3":s.startsWith("Bot: ")&&(a.style.color="#4CAF50"),n.current.appendChild(a),n.current.scrollTop=n.current.scrollHeight},[]);return Gt(ve.TransportStateChanged,ke.useCallback(s=>{i(`Transport state changed: ${s}`)},[i])),Gt(ve.BotConnected,ke.useCallback(s=>{i(`Bot connected: ${JSON.stringify(s)}`)},[i])),Gt(ve.BotDisconnected,ke.useCallback(s=>{i(`Bot disconnected: ${JSON.stringify(s)}`)},[i])),Gt(ve.TrackStarted,ke.useCallback((s,a)=>{i(`Track started: ${s.kind} from ${(a==null?void 0:a.name)||"unknown"}`)},[i])),Gt(ve.TrackStopped,ke.useCallback((s,a)=>{i(`Track stopped: ${s.kind} from ${(a==null?void 0:a.name)||"unknown"}`)},[i])),Gt(ve.BotReady,ke.useCallback(()=>{var a,c;if(i("Bot ready"),!e)return;const s=e.tracks();i(`Available tracks: ${JSON.stringify({local:{audio:!!s.local.audio,video:!!s.local.video},bot:{audio:!!((a=s.bot)!=null&&a.audio),video:!!((c=s.bot)!=null&&c.video)}})}`)},[e,i])),Gt(ve.UserTranscript,ke.useCallback(s=>{s.final&&i(`User: ${s.text}`)},[i])),Gt(ve.BotTranscript,ke.useCallback(s=>{i(`Bot: ${s.text}`)},[i])),Le.jsxs("div",{className:"debug-panel",children:[Le.jsx("h3",{children:"Debug Info"}),Le.jsx("div",{ref:n,className:"debug-log"})]})}function eb(){const e=Vu()!=="disconnected";return Le.jsx("div",{className:"bot-container",children:Le.jsx("div",{className:"video-container",children:e&&Le.jsx(Iv,{participant:"bot",fit:"cover"})})})}function tb(){return Le.jsxs("div",{className:"app",children:[Le.jsxs("div",{className:"status-bar",children:[Le.jsx(Xk,{}),Le.jsx(Yk,{})]}),Le.jsx("div",{className:"main-content",children:Le.jsx(eb,{})}),Le.jsx(Zk,{}),Le.jsx(Dv,{})]})}function nb(){return Le.jsx(Kk,{children:Le.jsx(tb,{})})}F_.createRoot(document.getElementById("root")).render(Le.jsx(Pa.StrictMode,{children:Le.jsx(nb,{})}));
