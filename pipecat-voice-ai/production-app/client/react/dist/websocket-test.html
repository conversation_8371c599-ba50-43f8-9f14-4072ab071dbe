<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket AI 聊天测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chat-container {
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            overflow-y: auto;
            background: #fafafa;
            margin-bottom: 20px;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            max-width: 80%;
        }
        .message.user {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        .message.assistant {
            background: #e9ecef;
            color: #333;
        }
        .message.system {
            background: #fff3cd;
            color: #856404;
            text-align: center;
            font-style: italic;
        }
        .input-container {
            display: flex;
            gap: 10px;
        }
        input[type="text"] {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
        }
        .status.disconnected {
            background: #ffebee;
            color: #c62828;
        }
        .status.connected {
            background: #e8f5e8;
            color: #2e7d32;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 WebSocket AI 聊天测试</h1>
        <p>直接通过WebSocket与Groq AI对话</p>
        
        <div id="status" class="status disconnected">状态: 未连接</div>
        
        <div id="chatContainer" class="chat-container">
            <div class="message system">等待连接WebSocket服务器...</div>
        </div>
        
        <div class="input-container">
            <input type="text" id="messageInput" placeholder="输入你的消息..." disabled>
            <button id="sendBtn" onclick="sendMessage()" disabled>发送</button>
            <button id="connectBtn" onclick="toggleConnection()">连接</button>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        
        function addMessage(content, type = 'system') {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = content;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        function updateStatus(connected) {
            const statusEl = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const messageInput = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            
            isConnected = connected;
            
            if (connected) {
                statusEl.className = 'status connected';
                statusEl.textContent = '状态: 已连接';
                connectBtn.textContent = '断开连接';
                messageInput.disabled = false;
                sendBtn.disabled = false;
                messageInput.focus();
            } else {
                statusEl.className = 'status disconnected';
                statusEl.textContent = '状态: 未连接';
                connectBtn.textContent = '连接';
                messageInput.disabled = true;
                sendBtn.disabled = true;
            }
        }
        
        function connect() {
            if (ws) {
                ws.close();
            }
            
            // 连接到WebSocket服务器
            const wsUrl = 'ws://su.guiyunai.fun:8765';
            addMessage(`正在连接到 ${wsUrl}...`);
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function(event) {
                addMessage('WebSocket连接成功！');
                updateStatus(true);
            };
            
            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    if (data.type === 'text' && data.content) {
                        addMessage(data.content, 'assistant');
                    }
                } catch (e) {
                    // 如果不是JSON，直接显示文本
                    addMessage(event.data, 'assistant');
                }
            };
            
            ws.onclose = function(event) {
                addMessage(`连接已关闭 (代码: ${event.code})`);
                updateStatus(false);
                ws = null;
            };
            
            ws.onerror = function(error) {
                addMessage(`连接错误: ${error.message || '未知错误'}`);
                updateStatus(false);
            };
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
            updateStatus(false);
        }
        
        function toggleConnection() {
            if (isConnected) {
                disconnect();
            } else {
                connect();
            }
        }
        
        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message || !ws || ws.readyState !== WebSocket.OPEN) {
                return;
            }
            
            // 显示用户消息
            addMessage(message, 'user');
            
            // 发送到服务器
            const data = {
                type: 'text',
                content: message
            };
            ws.send(JSON.stringify(data));
            
            // 清空输入框
            messageInput.value = '';
        }
        
        // 回车发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // 页面加载完成后自动连接
        window.addEventListener('load', function() {
            addMessage('页面加载完成，点击"连接"按钮开始聊天');
        });
    </script>
</body>
</html>
