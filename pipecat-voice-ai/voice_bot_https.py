#!/usr/bin/env python3

"""
HTTPS版本的中文语音对话机器人

基于01-say-one-thing.py，添加SSL支持以解决WebRTC的HTTPS要求
"""

import argparse
import asyncio
import os
import sys
from typing import Dict

import uvicorn
from dotenv import load_dotenv
from fastapi import BackgroundTasks, FastAPI
from fastapi.responses import RedirectResponse
from loguru import logger

from pipecat.frames.frames import EndFrame, TTSSpeakFrame
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineTask
from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext
from pipecat.services.openai.llm import OpenAILLMService
from pipecat.services.openai.stt import OpenAISTTService
from pipecat.services.openai.tts import OpenAITTSService
from pipecat.transports.base_transport import BaseTransport, TransportParams
from pipecat.transports.network.small_webrtc import SmallWebRTCTransport
from pipecat.transports.network.webrtc_connection import SmallWebRTCConnection
from pipecat.audio.vad.silero import SileroVADAnalyzer
from pipecat.transcriptions.language import Language

load_dotenv(override=True)


async def run_voice_bot(transport: BaseTransport, args: argparse.Namespace, handle_sigint: bool):
    """运行中文语音对话机器人"""
    logger.info("🤖 启动中文语音对话机器人")

    # 配置语音转文字服务 (STT) - 使用OpenAI Whisper，支持中文
    stt = OpenAISTTService(
        api_key=os.getenv("GROQ_API_KEY"),  # 使用Groq API密钥
        base_url="https://api.groq.com/openai/v1",  # Groq的OpenAI兼容端点
        model="whisper-large-v3",  # Groq的Whisper模型
        language=Language.ZH,  # 中文语言设置
    )

    # 配置大语言模型服务 (LLM) - 使用Groq的Llama模型
    llm = OpenAILLMService(
        api_key=os.getenv("GROQ_API_KEY"),
        base_url="https://api.groq.com/openai/v1",
        model="llama3-8b-8192",  # 使用Groq支持的模型名称
    )

    # 配置文字转语音服务 (TTS) - 使用OpenAI TTS
    tts = OpenAITTSService(
        api_key=os.getenv("GROQ_API_KEY"),  # 尝试使用Groq API
        base_url="https://api.groq.com/openai/v1",
        voice="alloy",  # 使用alloy语音
    )

    # 配置LLM上下文管理器
    context = OpenAILLMContext(
        messages=[
            {
                "role": "system",
                "content": "你是一个友好的中文AI助手。请用简洁、自然的中文回答用户的问题。回答要简短明了，不超过50字。"
            }
        ]
    )
    context_aggregator = llm.create_context_aggregator(context)

    # 创建处理管道：语音输入 -> 文字 -> AI处理 -> 语音输出
    pipeline = Pipeline([
        transport.input(),           # 接收音频输入
        stt,                        # 语音转文字
        context_aggregator.user(),  # 用户上下文聚合
        llm,                        # AI语言模型处理
        tts,                        # 文字转语音
        transport.output(),         # 输出音频
        context_aggregator.assistant(),  # 助手上下文聚合
    ])

    task = PipelineTask(pipeline)

    # 当客户端连接时的欢迎消息
    @transport.event_handler("on_client_connected")
    async def on_client_connected(transport, client):
        logger.info(f"客户端已连接: {client}")
        await task.queue_frames([
            TTSSpeakFrame("你好！我是您的中文语音助手。请开始说话，我会用语音回答您的问题。"),
            EndFrame()
        ])

    @transport.event_handler("on_client_disconnected")
    async def on_client_disconnected(transport, client):
        logger.info(f"客户端已断开连接: {client}")

    runner = PipelineRunner(handle_sigint=handle_sigint)
    await runner.run(task)


def create_https_app():
    """创建支持HTTPS的FastAPI应用"""
    from pipecat_ai_small_webrtc_prebuilt.frontend import SmallWebRTCPrebuiltUI
    
    app = FastAPI(title="中文语音对话机器人")
    
    # 存储WebRTC连接
    pcs_map: Dict[str, SmallWebRTCConnection] = {}
    
    # 挂载前端界面
    app.mount("/client", SmallWebRTCPrebuiltUI)
    
    @app.get("/", include_in_schema=False)
    async def root_redirect():
        return RedirectResponse(url="/client/")
    
    @app.post("/api/offer")
    async def offer(request: dict, background_tasks: BackgroundTasks):
        """处理WebRTC offer请求"""
        pc_id = request.get("pc_id")
        
        if pc_id and pc_id in pcs_map:
            # 重用现有连接
            pipecat_connection = pcs_map[pc_id]
            await pipecat_connection.restart(
                sdp=request["sdp"],
                type=request["type"],
                restart_pc=request.get("restart_pc", False),
            )
        else:
            # 创建新连接
            pipecat_connection = SmallWebRTCConnection()
            await pipecat_connection.initialize(sdp=request["sdp"], type=request["type"])
            
            @pipecat_connection.event_handler("closed")
            async def handle_disconnected(webrtc_connection: SmallWebRTCConnection):
                logger.info(f"WebRTC连接已关闭: {webrtc_connection.pc_id}")
                pcs_map.pop(webrtc_connection.pc_id, None)
            
            # 创建传输参数
            params = TransportParams(
                audio_out_enabled=True,
                audio_in_enabled=True,
                vad_enabled=True,
                vad_analyzer=SileroVADAnalyzer(),
            )
            
            # 创建传输并启动机器人
            transport = SmallWebRTCTransport(params=params, webrtc_connection=pipecat_connection)
            
            # 创建模拟的args对象
            class Args:
                def __init__(self):
                    self.transport = "webrtc"
                    self.host = "0.0.0.0"
                    self.port = 7860
            
            args = Args()
            background_tasks.add_task(run_voice_bot, transport, args, False)
        
        answer = pipecat_connection.get_answer()
        pcs_map[answer["pc_id"]] = pipecat_connection
        
        return answer
    
    return app


def main():
    """主函数"""
    logger.info("🚀 启动HTTPS版本的中文语音对话机器人")
    
    # 检查SSL证书
    ssl_certfile = "/etc/letsencrypt/live/su.guiyunai.fun/fullchain.pem"
    ssl_keyfile = "/etc/letsencrypt/live/su.guiyunai.fun/privkey.pem"
    
    if not os.path.exists(ssl_certfile) or not os.path.exists(ssl_keyfile):
        logger.error("❌ SSL证书文件不存在")
        logger.error(f"证书文件: {ssl_certfile}")
        logger.error(f"密钥文件: {ssl_keyfile}")
        sys.exit(1)
    
    logger.info("✅ SSL证书检查通过")
    
    # 创建FastAPI应用
    app = create_https_app()
    
    # 启动HTTPS服务器
    logger.info("🔒 启动HTTPS WebRTC服务器")
    logger.info("📱 访问地址: https://su.guiyunai.fun:7860")
    logger.info("⚠️  请确保防火墙开放7860端口")
    
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=7860,
            ssl_keyfile=ssl_keyfile,
            ssl_certfile=ssl_certfile,
            log_level="info"
        )
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器运行错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
