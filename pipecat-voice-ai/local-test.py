#!/usr/bin/env python3

"""
本地音频测试 - 验证Groq TTS是否工作

不需要Daily.co，直接输出到本地音频
"""

import asyncio
import os
import sys

from dotenv import load_dotenv
from loguru import logger

from pipecat.frames.frames import EndFrame, TTSSpeakFrame
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineTask
from pipecat.services.openai.tts import OpenAITTSService
from pipecat.transports.local.audio import LocalAudioTransport, LocalAudioTransportParams

load_dotenv(override=True)


async def main():
    """主函数"""
    logger.info("启动本地音频测试")
    
    # 检查必要的环境变量
    groq_api_key = os.getenv("GROQ_API_KEY")
    
    if not groq_api_key:
        logger.error("缺少GROQ_API_KEY环境变量")
        sys.exit(1)

    # 配置本地音频传输
    transport = LocalAudioTransport(
        params=LocalAudioTransportParams(
            audio_out_enabled=True,
            audio_in_enabled=False
        )
    )

    # 配置TTS服务 - 使用OpenAI兼容的Groq API
    tts = OpenAITTSService(
        api_key=groq_api_key,
        base_url="https://api.groq.com/openai/v1",
        voice="alloy",
    )

    # 创建管道
    pipeline = Pipeline([tts, transport.output()])
    task = PipelineTask(pipeline)

    # 运行机器人
    runner = PipelineRunner()
    
    logger.info("开始播放测试音频...")
    
    # 直接播放测试消息
    await task.queue_frames([
        TTSSpeakFrame("你好！这是Groq AI语音合成测试。如果你能听到这段话，说明配置成功了！"), 
        EndFrame()
    ])
    
    await runner.run(task)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试已停止")
    except Exception as e:
        logger.error(f"测试错误: {e}")
        sys.exit(1)
