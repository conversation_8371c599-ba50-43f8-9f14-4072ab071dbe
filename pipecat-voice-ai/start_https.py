#!/usr/bin/env python3

"""
HTTPS版本的中文语音对话机器人启动器

解决WebRTC需要HTTPS的问题
"""

import argparse
import os
import ssl
import sys

from dotenv import load_dotenv
from loguru import logger

# 导入主程序的运行函数
from pipecat.examples.run import main as pipecat_main

# 导入我们的机器人函数
import importlib.util
spec = importlib.util.spec_from_file_location("voice_bot", "01-say-one-thing.py")
voice_bot = importlib.util.module_from_spec(spec)
spec.loader.exec_module(voice_bot)

load_dotenv(override=True)


def create_ssl_context():
    """创建SSL上下文"""
    try:
        ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        ssl_context.load_cert_chain(
            certfile="/etc/letsencrypt/live/su.guiyunai.fun/fullchain.pem",
            keyfile="/etc/letsencrypt/live/su.guiyunai.fun/privkey.pem"
        )
        logger.info("✅ SSL证书加载成功")
        return ssl_context
    except Exception as e:
        logger.error(f"❌ SSL证书加载失败: {e}")
        return None


def patch_uvicorn_for_ssl():
    """为Uvicorn添加SSL支持"""
    import uvicorn
    from pipecat.transports.network.fastapi_websocket import FastAPIWebsocketTransport
    
    # 保存原始的run方法
    original_run = uvicorn.run
    
    def ssl_run(*args, **kwargs):
        # 如果没有指定SSL，添加SSL配置
        if 'ssl_keyfile' not in kwargs and 'ssl_certfile' not in kwargs:
            kwargs['ssl_keyfile'] = "/etc/letsencrypt/live/su.guiyunai.fun/privkey.pem"
            kwargs['ssl_certfile'] = "/etc/letsencrypt/live/su.guiyunai.fun/fullchain.pem"
            logger.info("🔒 启用HTTPS模式")
        
        return original_run(*args, **kwargs)
    
    # 替换uvicorn.run方法
    uvicorn.run = ssl_run


def main():
    """主函数"""
    logger.info("🚀 启动HTTPS版本的中文语音对话机器人")
    
    # 检查SSL证书
    ssl_context = create_ssl_context()
    if not ssl_context:
        logger.error("无法加载SSL证书，退出")
        sys.exit(1)
    
    # 为Uvicorn添加SSL支持
    patch_uvicorn_for_ssl()
    
    # 设置命令行参数
    sys.argv = [
        "start_https.py",
        "--transport", "webrtc",
        "--host", "0.0.0.0", 
        "--port", "7860"
    ]
    
    logger.info("🔒 使用HTTPS启动WebRTC服务器")
    logger.info("📱 访问地址: https://su.guiyunai.fun:7860")
    
    # 调用Pipecat主函数
    try:
        pipecat_main(voice_bot.run_example, transport_params=voice_bot.transport_params)
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器运行错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
