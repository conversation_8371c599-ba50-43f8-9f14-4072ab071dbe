# Groq AI + Edge TTS 语音机器人

基于Pipecat框架的智能语音机器人，集成Groq AI大语言模型和微软Edge TTS语音合成技术。

## 🌟 特性

- **智能对话**: 使用Groq AI的Llama-3.1-8b-instant模型进行自然语言理解和生成
- **高质量语音**: 集成微软Edge TTS，支持多种中文语音
- **实时交互**: 基于WebSocket的实时语音对话体验
- **Web界面**: 美观的现代化Web界面，支持文本和语音交互
- **免费部署**: 使用免费的API服务，无需额外费用

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Linux/macOS/Windows
- 网络连接（用于API调用）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd pipecat-voice-ai
```

2. **创建虚拟环境**
```bash
python -m venv env
source env/bin/activate  # Linux/macOS
# 或 env\Scripts\activate  # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，填入您的API密钥
```

5. **启动Web演示**
```bash
python web-demo.py
```

6. **访问应用**
打开浏览器访问: http://localhost:8000

## 🔧 配置说明

### 环境变量

在 `.env` 文件中配置以下变量：

```env
# Groq AI API密钥 (必需)
GROQ_API_KEY=your_groq_api_key_here

# Daily.co API密钥 (可选，用于WebRTC功能)
DAILY_API_KEY=your_daily_api_key_here
```

### 获取API密钥

1. **Groq API密钥**
   - 访问 [Groq Console](https://console.groq.com/)
   - 注册账户并创建API密钥
   - 免费额度足够测试使用

2. **Daily.co API密钥** (可选)
   - 访问 [Daily.co](https://www.daily.co/)
   - 注册账户并获取API密钥
   - 用于WebRTC实时语音通话功能

## 📁 项目结构

```
pipecat-voice-ai/
├── web-demo.py              # Web演示应用
├── simple-tts-test.py       # TTS功能测试
├── custom-edge-tts.py       # 自定义Edge TTS服务
├── requirements.txt         # Python依赖
├── .env                     # 环境变量配置
├── static/                  # 静态文件目录
└── README.md               # 项目文档
```

## 🎯 功能演示

### 1. Web界面演示
- 启动 `python web-demo.py`
- 访问 http://localhost:8000
- 在输入框中输入问题
- 系统会生成文本回复和语音回复

### 2. TTS功能测试
```bash
python simple-tts-test.py
```
会生成测试音频文件 `test_output.mp3` 和 `test_output.wav`

### 3. 自定义TTS服务
```bash
python custom-edge-tts.py
```
演示如何集成Edge TTS到Pipecat框架

## 🔊 支持的语音

Edge TTS支持多种中文语音：

- `zh-CN-XiaoxiaoNeural` - 中文女声（默认）
- `zh-CN-YunxiNeural` - 中文男声
- `zh-CN-YunyangNeural` - 中文男声
- `zh-CN-XiaoyiNeural` - 中文女声
- `zh-CN-YunjianNeural` - 中文男声

可在代码中修改 `voice` 参数来切换语音。

## 🛠️ 技术栈

- **后端框架**: FastAPI + Uvicorn
- **AI模型**: Groq AI (Llama-3.1-8b-instant)
- **语音合成**: Microsoft Edge TTS
- **音频处理**: PyDub + FFmpeg
- **实时通信**: WebSocket
- **语音框架**: Pipecat AI

## 📝 API接口

### WebSocket接口

**连接地址**: `ws://localhost:8000/ws`

**消息格式**:

发送消息：
```json
{
    "type": "user_message",
    "content": "用户输入的文本"
}
```

接收消息：
```json
{
    "type": "text_response",
    "content": "AI生成的文本回复"
}
```

```json
{
    "type": "audio_response", 
    "content": "AI生成的文本回复",
    "audio_url": "/static/audio_xxx.mp3"
}
```

## 🚀 部署指南

### 本地部署

1. 按照"快速开始"步骤安装和配置
2. 运行 `python web-demo.py`
3. 访问 http://localhost:8000

### 服务器部署

1. 安装系统依赖：
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y ffmpeg portaudio19-dev

# CentOS/RHEL
sudo yum install -y ffmpeg portaudio-devel
```

2. 配置防火墙开放8000端口

3. 使用进程管理器（如PM2、Supervisor）管理应用

4. 可选：配置Nginx反向代理

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Pipecat AI](https://github.com/pipecat-ai/pipecat) - 优秀的语音AI框架
- [Groq](https://groq.com/) - 高性能AI推理平台
- [Microsoft Edge TTS](https://github.com/rany2/edge-tts) - 免费的语音合成服务
- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的Python Web框架

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交GitHub Issue
- 发送邮件至: [<EMAIL>]

---

**享受与AI的语音对话吧！** 🎉
