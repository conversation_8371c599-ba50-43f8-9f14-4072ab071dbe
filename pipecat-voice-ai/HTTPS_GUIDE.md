# HTTPS版本中文语音对话机器人使用指南

## 🔒 问题解决

### 原始问题
- **HTTP版本**: http://su.guiyunai.fun:7860 显示空白页面
- **JavaScript错误**: `Cannot read properties of undefined (reading 'enumerateDevices')`
- **根本原因**: WebRTC需要HTTPS才能访问麦克风和摄像头设备

### 解决方案
创建了HTTPS版本的语音机器人，使用Let's Encrypt SSL证书。

## 🚀 启动HTTPS版本

### 1. 启动命令
```bash
# 激活虚拟环境
source env/bin/activate

# 启动HTTPS版本
python voice_bot_https.py
```

### 2. 访问地址
- **HTTPS地址**: https://su.guiyunai.fun:7860
- **状态**: ✅ 正常运行，支持WebRTC设备访问

## 🎯 功能验证

### Web界面状态
- ✅ **SSL证书**: Let's Encrypt证书正常加载
- ✅ **页面加载**: CSS和JavaScript文件正常加载
- ✅ **设备访问**: 浏览器可以访问麦克风设备
- ✅ **WebRTC连接**: 支持实时音频传输

### 服务器日志
```
INFO: Uvicorn running on https://0.0.0.0:7860
INFO: *************:59616 - "GET / HTTP/1.1" 307 Temporary Redirect
INFO: *************:59616 - "GET /client/ HTTP/1.1" 200 OK
INFO: *************:59602 - "GET /client/assets/index-CdJgXBqL.css HTTP/1.1" 200 OK
INFO: *************:59616 - "GET /client/assets/index-CJ8kfZwS.js HTTP/1.1" 200 OK
```

## 🔧 技术实现

### SSL配置
```python
uvicorn.run(
    app,
    host="0.0.0.0",
    port=7860,
    ssl_keyfile="/etc/letsencrypt/live/su.guiyunai.fun/privkey.pem",
    ssl_certfile="/etc/letsencrypt/live/su.guiyunai.fun/fullchain.pem",
    log_level="info"
)
```

### WebRTC传输
```python
# 支持HTTPS的WebRTC传输
transport = SmallWebRTCTransport(
    params=TransportParams(
        audio_out_enabled=True,
        audio_in_enabled=True,
        vad_enabled=True,
        vad_analyzer=SileroVADAnalyzer(),
    ),
    webrtc_connection=pipecat_connection
)
```

## 📱 使用步骤

### 1. 访问Web界面
1. 打开浏览器访问: https://su.guiyunai.fun:7860
2. 浏览器会显示安全的HTTPS连接（绿色锁图标）

### 2. 连接语音机器人
1. 点击页面上的"Connect"或"连接"按钮
2. 浏览器会请求麦克风权限，点击"允许"
3. 连接成功后，机器人会播放中文欢迎语音

### 3. 开始语音对话
1. 对着麦克风说话（中文）
2. 语音活动检测(VAD)会自动识别您的语音
3. 系统将语音转换为文字，发送给AI处理
4. AI生成中文回复，转换为语音播放

## ⚠️ 注意事项

### 浏览器兼容性
- ✅ **Chrome**: 完全支持WebRTC和麦克风访问
- ✅ **Firefox**: 支持WebRTC功能
- ✅ **Safari**: 支持WebRTC（可能需要额外权限设置）
- ❌ **旧版浏览器**: 可能不支持WebRTC

### 网络要求
- 需要稳定的互联网连接
- HTTPS连接确保安全传输
- WebRTC需要UDP端口开放

### API状态
- ⚠️ **Groq API**: 当前存在连接问题，可能影响实际对话功能
- ✅ **WebRTC传输**: 正常工作
- ✅ **语音检测**: VAD功能正常

## 🔍 故障排除

### 1. 页面空白
- **原因**: 使用HTTP而非HTTPS
- **解决**: 访问 https://su.guiyunai.fun:7860

### 2. 麦克风权限被拒绝
- **解决**: 在浏览器设置中允许网站访问麦克风
- **Chrome**: 地址栏左侧点击锁图标 → 权限设置

### 3. 连接失败
- **检查**: 确认服务器正在运行
- **检查**: 防火墙是否开放7860端口
- **检查**: SSL证书是否有效

### 4. 语音无响应
- **原因**: Groq API连接问题
- **状态**: 需要修复API连接才能实现完整对话功能

## 📊 当前状态

### ✅ 已解决
- HTTPS访问问题
- WebRTC设备权限问题
- SSL证书配置
- Web界面加载

### ⚠️ 待解决
- Groq API连接问题
- 实际语音对话功能验证

## 🔄 下一步

1. **修复API连接**: 解决Groq API的404错误
2. **功能测试**: 验证完整的语音对话流程
3. **性能优化**: 减少语音延迟，提高响应速度

---

**HTTPS版本状态**: 🟢 正常运行  
**访问地址**: https://su.guiyunai.fun:7860  
**最后更新**: 2025-07-31 17:50
