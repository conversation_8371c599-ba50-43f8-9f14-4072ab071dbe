#!/usr/bin/env python3

"""
Groq AI 语音对话示例 - 按官方文档正确部署

使用官方Pipecat框架 + Groq服务
"""

import argparse
import os

from dotenv import load_dotenv
from loguru import logger

from pipecat.frames.frames import EndFrame, TTSSpeakFrame
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineTask
from pipecat.services.openai.tts import OpenAITTSService  # 使用OpenAI兼容的TTS
from pipecat.transports.base_transport import BaseTransport, TransportParams
from pipecat.transports.network.fastapi_websocket import FastAPIWebsocketParams
from pipecat.transports.services.daily import DailyParams

load_dotenv(override=True)

# 传输参数配置
transport_params = {
    "daily": lambda: DailyParams(audio_out_enabled=True),
    "webrtc": lambda: TransportParams(audio_out_enabled=True),
}


async def run_example(transport: BaseTransport, args: argparse.Namespace, handle_sigint: bool):
    logger.info(f"启动Groq AI语音机器人")

    # 使用OpenAI兼容的TTS服务，指向Groq API
    tts = OpenAITTSService(
        api_key=os.getenv("GROQ_API_KEY"),
        base_url="https://api.groq.com/openai/v1",  # Groq的OpenAI兼容端点
        voice="alloy",  # 使用alloy语音
    )

    task = PipelineTask(Pipeline([tts, transport.output()]))

    # 当客户端连接时播放欢迎消息
    @transport.event_handler("on_client_connected")
    async def on_client_connected(transport, client):
        logger.info(f"客户端已连接: {client}")
        await task.queue_frames([
            TTSSpeakFrame("你好！我是基于Groq AI的语音助手，很高兴为您服务！"), 
            EndFrame()
        ])

    runner = PipelineRunner(handle_sigint=handle_sigint)
    await runner.run(task)


if __name__ == "__main__":
    from pipecat.examples.run import main

    main(run_example, transport_params=transport_params)
