#!/bin/bash

# HTTPS版本的语音机器人启动脚本
# 解决WebRTC需要HTTPS的问题

echo "🚀 启动HTTPS版本的中文语音对话机器人"

# 检查SSL证书
if [ ! -f "/etc/letsencrypt/live/su.guiyunai.fun/fullchain.pem" ]; then
    echo "❌ SSL证书不存在"
    exit 1
fi

echo "✅ SSL证书检查通过"

# 激活虚拟环境
source env/bin/activate

# 设置SSL环境变量
export SSL_CERTFILE="/etc/letsencrypt/live/su.guiyunai.fun/fullchain.pem"
export SSL_KEYFILE="/etc/letsencrypt/live/su.guiyunai.fun/privkey.pem"

echo "🔒 使用HTTPS启动WebRTC服务器"
echo "📱 访问地址: https://su.guiyunai.fun:7860"
echo "⚠️  请确保防火墙开放7860端口"
echo ""

# 启动服务器，使用SSL
python -c "
import os
import sys
import ssl
import uvicorn
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True)

# 导入主程序
sys.path.append('.')
from pipecat.examples.run import main as pipecat_main

# 导入机器人函数
import importlib.util
spec = importlib.util.spec_from_file_location('voice_bot', '01-say-one-thing.py')
voice_bot = importlib.util.module_from_spec(spec)
spec.loader.exec_module(voice_bot)

# 修改系统参数以启用SSL
sys.argv = [
    'start_ssl.py',
    '--transport', 'webrtc',
    '--host', '0.0.0.0',
    '--port', '7860',
    '--ssl-keyfile', '/etc/letsencrypt/live/su.guiyunai.fun/privkey.pem',
    '--ssl-certfile', '/etc/letsencrypt/live/su.guiyunai.fun/fullchain.pem'
]

# 启动服务
try:
    pipecat_main(voice_bot.run_example, transport_params=voice_bot.transport_params)
except KeyboardInterrupt:
    print('\\n服务器已停止')
except Exception as e:
    print(f'服务器运行错误: {e}')
    sys.exit(1)
"
