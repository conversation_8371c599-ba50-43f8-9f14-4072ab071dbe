#
# Copyright (c) 2024–2025, Daily
#
# SPDX-License-Identifier: BSD 2-Clause License
#
# 中文语音对话机器人 - 基于Pipecat框架
# 集成Groq AI + OpenAI TTS/STT + Daily传输
#

import argparse
import os

from dotenv import load_dotenv
from loguru import logger

from pipecat.frames.frames import EndFrame, TTSSpeakFrame
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineTask
from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext
from pipecat.services.openai.llm import OpenAILLMService
from pipecat.services.openai.stt import OpenAISTTService
from pipecat.services.groq.tts import GroqTTSService
from pipecat.transports.base_transport import BaseTransport, TransportParams
from pipecat.transports.network.fastapi_websocket import FastAPIWebsocketParams
from pipecat.transports.services.daily import DailyParams
from pipecat.audio.vad.silero import SileroVADAnalyzer
from pipecat.transcriptions.language import Language

load_dotenv(override=True)


# 传输参数配置 - 支持语音输入和输出
# 使用SileroVAD进行语音活动检测
transport_params = {
    "daily": lambda: DailyParams(
        audio_out_enabled=True,
        audio_in_enabled=True,
        vad_enabled=True,
        vad_analyzer=SileroVADAnalyzer(),
        transcription_enabled=True,
    ),
    "twilio": lambda: FastAPIWebsocketParams(
        audio_out_enabled=True,
        audio_in_enabled=True,
        vad_enabled=True,
        vad_analyzer=SileroVADAnalyzer(),
    ),
    "webrtc": lambda: TransportParams(
        audio_out_enabled=True,
        audio_in_enabled=True,
        vad_enabled=True,
        vad_analyzer=SileroVADAnalyzer(),
    ),
}


async def run_example(transport: BaseTransport, args: argparse.Namespace, handle_sigint: bool):
    logger.info("🤖 启动中文语音对话机器人")

    # 配置语音转文字服务 (STT) - 使用OpenAI Whisper，支持中文
    stt = OpenAISTTService(
        api_key=os.getenv("GROQ_API_KEY"),  # 使用Groq API密钥
        base_url="https://api.groq.com/openai/v1",  # Groq的OpenAI兼容端点
        model="whisper-large-v3",  # Groq的Whisper模型
        language=Language.ZH,  # 中文语言设置
    )

    # 配置大语言模型服务 (LLM) - 使用Groq的Llama模型
    llm = OpenAILLMService(
        api_key=os.getenv("GROQ_API_KEY"),
        base_url="https://api.groq.com/openai/v1",
        model="llama3-8b-8192",  # 使用Groq支持的模型名称
    )

    # 配置文字转语音服务 (TTS) - 使用Groq TTS
    tts = GroqTTSService(
        api_key=os.getenv("GROQ_API_KEY"),
        voice="alloy",  # 使用alloy语音
    )

    # 配置LLM上下文管理器
    context = OpenAILLMContext(
        messages=[
            {
                "role": "system",
                "content": "你是一个友好的中文AI助手。请用简洁、自然的中文回答用户的问题。回答要简短明了，不超过50字。"
            }
        ]
    )
    context_aggregator = llm.create_context_aggregator(context)

    # 创建处理管道：语音输入 -> 文字 -> AI处理 -> 语音输出
    pipeline = Pipeline([
        transport.input(),           # 接收音频输入
        stt,                        # 语音转文字
        context_aggregator.user(),  # 用户上下文聚合
        llm,                        # AI语言模型处理
        tts,                        # 文字转语音
        transport.output(),         # 输出音频
        context_aggregator.assistant(),  # 助手上下文聚合
    ])

    task = PipelineTask(pipeline)

    # 当客户端连接时的欢迎消息
    @transport.event_handler("on_client_connected")
    async def on_client_connected(transport, client):
        logger.info(f"客户端已连接: {client}")
        await task.queue_frames([
            TTSSpeakFrame("你好！我是您的中文语音助手。请开始说话，我会用语音回答您的问题。"),
            EndFrame()
        ])

    @transport.event_handler("on_client_disconnected")
    async def on_client_disconnected(transport, client, reason):
        logger.info(f"客户端已断开连接: {client}, 原因: {reason}")

    runner = PipelineRunner(handle_sigint=handle_sigint)
    await runner.run(task)


if __name__ == "__main__":
    from pipecat.examples.run import main

    main(run_example, transport_params=transport_params)
