# Daily.co API 密钥获取指南

## 🎯 为什么需要Daily API密钥？

Pipecat使用Daily.co作为WebRTC基础设施来提供：
- 实时音视频通信
- 低延迟语音交互
- 跨平台兼容性
- 企业级稳定性

## 💰 费用说明

**Daily.co 免费额度：**
- ✅ 每月 10,000 参与者分钟免费
- ✅ HD视频质量
- ✅ 全球CDN
- ✅ 开发者支持

**实际使用估算：**
- 1对1语音对话，每小时 = 120参与者分钟 (你+AI各60分钟)
- 免费额度可支持约83小时/月的使用
- 对于测试和小规模使用完全免费

## 🚀 获取API密钥步骤

### 1. 注册Daily账户
访问：https://dashboard.daily.co/u/signup

### 2. 验证邮箱
检查邮箱并点击验证链接

### 3. 获取API密钥
1. 登录后访问：https://dashboard.daily.co/developers
2. 点击 "Create API Key"
3. 复制生成的API密钥

### 4. 配置到项目
将API密钥添加到 `.env` 文件：
```bash
DAILY_API_KEY=你的API密钥
```

## 🔧 快速配置命令

```bash
# 编辑环境变量文件
nano /www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server/.env

# 添加以下行（替换为你的实际API密钥）：
DAILY_API_KEY=your_actual_api_key_here

# 重启服务器
cd /www/wwwroot/su.guiyunai.fun/pipecat-voice-ai/production-app/server
./stop_server.sh
./start_server.sh
```

## 🌟 替代方案

如果不想使用Daily.co，可以考虑：

### LiveKit (开源替代)
- 更大的免费额度
- 可自托管
- 需要修改代码适配

### Agora.io
- 类似的免费额度
- 全球覆盖
- 价格稍高

## 📞 获取帮助

如果遇到问题：
1. 查看Daily.co文档：https://docs.daily.co/
2. 联系Daily支持：<EMAIL>
3. 查看Pipecat文档：https://docs.pipecat.ai/

## ⚡ 临时测试方案

如果暂时无法获取API密钥，可以：
1. 使用Daily的演示房间进行测试
2. 或者我们可以实现一个简化版本，先不使用视频功能

---

**建议：先注册Daily免费账户，10,000分钟/月的免费额度对你的项目完全够用！**
