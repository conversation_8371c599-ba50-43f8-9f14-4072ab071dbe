#!/usr/bin/env python3

"""
简单的Groq AI语音机器人 - 按官方文档正确部署

直接使用Pipecat核心功能，不依赖examples模块
"""

import asyncio
import os
import sys

from dotenv import load_dotenv
from loguru import logger

from pipecat.frames.frames import EndFrame, TTSSpeakFrame
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineTask
from pipecat.services.openai.tts import OpenAITTSService
from pipecat.transports.services.daily import DailyTransport, DailyParams

load_dotenv(override=True)


async def main():
    """主函数"""
    logger.info("启动Groq AI语音机器人")
    
    # 检查必要的环境变量
    daily_api_key = os.getenv("DAILY_API_KEY")
    groq_api_key = os.getenv("GROQ_API_KEY")
    
    if not daily_api_key:
        logger.error("缺少DAILY_API_KEY环境变量")
        sys.exit(1)
        
    if not groq_api_key:
        logger.error("缺少GROQ_API_KEY环境变量")
        sys.exit(1)

    # 创建Daily房间
    import aiohttp
    async with aiohttp.ClientSession() as session:
        headers = {
            "Authorization": f"Bearer {daily_api_key}",
            "Content-Type": "application/json"
        }
        
        # 创建房间
        room_data = {
            "properties": {
                "exp": int(asyncio.get_event_loop().time()) + 3600,  # 1小时后过期
                "enable_chat": True,
                "enable_knocking": False,
                "enable_screenshare": False,
                "enable_recording": False
            }
        }
        
        async with session.post(
            "https://api.daily.co/v1/rooms",
            headers=headers,
            json=room_data
        ) as response:
            if response.status != 200:
                logger.error(f"创建Daily房间失败: {response.status}")
                sys.exit(1)
                
            room_info = await response.json()
            room_url = room_info["url"]
            logger.info(f"Daily房间已创建: {room_url}")

        # 创建令牌
        token_data = {
            "properties": {
                "room_name": room_info["name"],
                "is_owner": True,
                "exp": int(asyncio.get_event_loop().time()) + 3600
            }
        }
        
        async with session.post(
            "https://api.daily.co/v1/meeting-tokens",
            headers=headers,
            json=token_data
        ) as response:
            if response.status != 200:
                logger.error(f"创建Daily令牌失败: {response.status}")
                sys.exit(1)
                
            token_info = await response.json()
            token = token_info["token"]
            logger.info(f"Daily令牌已创建")

    # 配置Daily传输
    transport = DailyTransport(
        room_url,
        token,
        "Groq AI Bot",
        DailyParams(
            audio_out_enabled=True,
            audio_in_enabled=False,  # 暂时只输出音频
            vad_enabled=False,
            transcription_enabled=False,
        ),
    )

    # 配置TTS服务 - 使用OpenAI兼容的Groq API
    tts = OpenAITTSService(
        api_key=groq_api_key,
        base_url="https://api.groq.com/openai/v1",
        voice="alloy",
    )

    # 创建管道
    pipeline = Pipeline([tts, transport.output()])
    task = PipelineTask(pipeline)

    # 当客户端连接时播放欢迎消息
    @transport.event_handler("on_client_connected")
    async def on_client_connected(transport, client):
        logger.info(f"客户端已连接: {client}")
        await task.queue_frames([
            TTSSpeakFrame("你好！我是基于Groq AI的语音助手。欢迎使用！"), 
            EndFrame()
        ])

    @transport.event_handler("on_client_disconnected")
    async def on_client_disconnected(transport, client, reason):
        logger.info(f"客户端已断开: {client}, 原因: {reason}")

    # 运行机器人
    runner = PipelineRunner()
    
    logger.info("机器人已启动，等待客户端连接...")
    logger.info(f"请访问: {room_url}")
    
    await runner.run(task)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("机器人已停止")
    except Exception as e:
        logger.error(f"运行错误: {e}")
        sys.exit(1)
