#!/usr/bin/env python3

"""
Groq AI + Edge TTS 语音机器人 Web演示

使用FastAPI创建Web界面，展示语音机器人功能
"""

import asyncio
import io
import os
import uuid
from typing import Dict, Any

import edge_tts
from dotenv import load_dotenv
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from loguru import logger
from openai import AsyncOpenAI

load_dotenv(override=True)

app = FastAPI(title="Groq AI + Edge TTS 语音机器人")

# 配置Groq客户端
groq_client = AsyncOpenAI(
    api_key=os.getenv("GROQ_API_KEY"),
    base_url="https://api.groq.com/openai/v1"
)

# 存储活跃的WebSocket连接
active_connections: Dict[str, WebSocket] = {}


class VoiceBot:
    """语音机器人类"""
    
    def __init__(self):
        self.voice = "zh-CN-XiaoxiaoNeural"  # 中文女声
        
    async def generate_response(self, user_message: str) -> str:
        """使用Groq生成回复"""
        try:
            response = await groq_client.chat.completions.create(
                model="llama-3.1-8b-instant",
                messages=[
                    {
                        "role": "system", 
                        "content": "你是一个友好的AI助手，用中文回答问题。回答要简洁明了，不超过100字。"
                    },
                    {"role": "user", "content": user_message}
                ],
                max_tokens=200,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Groq API错误: {e}")
            return "抱歉，我现在无法处理您的请求。请稍后再试。"
    
    async def text_to_speech(self, text: str) -> bytes:
        """使用Edge TTS生成语音"""
        try:
            communicate = edge_tts.Communicate(text, self.voice)
            audio_data = b""
            
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_data += chunk["data"]
            
            return audio_data
            
        except Exception as e:
            logger.error(f"Edge TTS错误: {e}")
            return b""


# 创建机器人实例
bot = VoiceBot()


@app.get("/", response_class=HTMLResponse)
async def get_demo_page():
    """返回演示页面"""
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Groq AI + Edge TTS 语音机器人</title>
        <style>
            body {
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: white;
            }
            .container {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 30px;
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            }
            h1 {
                text-align: center;
                margin-bottom: 30px;
                font-size: 2.5em;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            .chat-container {
                height: 400px;
                overflow-y: auto;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 20px;
                background: rgba(0, 0, 0, 0.2);
            }
            .message {
                margin-bottom: 15px;
                padding: 10px;
                border-radius: 8px;
                max-width: 80%;
            }
            .user-message {
                background: rgba(100, 200, 255, 0.3);
                margin-left: auto;
                text-align: right;
            }
            .bot-message {
                background: rgba(255, 255, 255, 0.2);
                margin-right: auto;
            }
            .input-container {
                display: flex;
                gap: 10px;
                margin-bottom: 20px;
            }
            input[type="text"] {
                flex: 1;
                padding: 12px;
                border: none;
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.9);
                color: #333;
                font-size: 16px;
            }
            button {
                padding: 12px 20px;
                border: none;
                border-radius: 8px;
                background: #4CAF50;
                color: white;
                cursor: pointer;
                font-size: 16px;
                transition: background 0.3s;
            }
            button:hover {
                background: #45a049;
            }
            button:disabled {
                background: #cccccc;
                cursor: not-allowed;
            }
            .status {
                text-align: center;
                margin-top: 10px;
                font-style: italic;
            }
            .audio-player {
                width: 100%;
                margin-top: 10px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🤖 Groq AI + Edge TTS 语音机器人</h1>
            
            <div class="chat-container" id="chatContainer">
                <div class="message bot-message">
                    <strong>AI助手:</strong> 你好！我是基于Groq AI和Edge TTS的语音助手。请输入您的问题，我会用语音回答您！
                </div>
            </div>
            
            <div class="input-container">
                <input type="text" id="messageInput" placeholder="请输入您的问题..." maxlength="200">
                <button onclick="sendMessage()" id="sendButton">发送</button>
            </div>
            
            <div class="status" id="status">准备就绪</div>
        </div>

        <script>
            const ws = new WebSocket(`ws://${window.location.host}/ws`);
            const chatContainer = document.getElementById('chatContainer');
            const messageInput = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            const status = document.getElementById('status');

            ws.onopen = function(event) {
                status.textContent = '已连接到服务器';
            };

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                
                if (data.type === 'text_response') {
                    addMessage('bot', data.content);
                } else if (data.type === 'audio_response') {
                    addAudioMessage('bot', data.content, data.audio_url);
                } else if (data.type === 'status') {
                    status.textContent = data.content;
                }
            };

            ws.onclose = function(event) {
                status.textContent = '连接已断开';
            };

            function addMessage(sender, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}-message`;
                messageDiv.innerHTML = `<strong>${sender === 'user' ? '您' : 'AI助手'}:</strong> ${content}`;
                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }

            function addAudioMessage(sender, content, audioUrl) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}-message`;
                messageDiv.innerHTML = `
                    <strong>AI助手:</strong> ${content}
                    <audio controls class="audio-player">
                        <source src="${audioUrl}" type="audio/mpeg">
                        您的浏览器不支持音频播放。
                    </audio>
                `;
                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
                
                // 自动播放音频
                const audio = messageDiv.querySelector('audio');
                audio.play().catch(e => console.log('自动播放失败:', e));
            }

            function sendMessage() {
                const message = messageInput.value.trim();
                if (message && ws.readyState === WebSocket.OPEN) {
                    addMessage('user', message);
                    ws.send(JSON.stringify({type: 'user_message', content: message}));
                    messageInput.value = '';
                    sendButton.disabled = true;
                    status.textContent = '正在处理...';
                }
            }

            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !sendButton.disabled) {
                    sendMessage();
                }
            });

            // 重新启用发送按钮
            ws.addEventListener('message', function(event) {
                const data = JSON.parse(event.data);
                if (data.type === 'audio_response') {
                    sendButton.disabled = false;
                    status.textContent = '准备就绪';
                }
            });
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点"""
    await websocket.accept()
    connection_id = str(uuid.uuid4())
    active_connections[connection_id] = websocket
    
    logger.info(f"新的WebSocket连接: {connection_id}")
    
    try:
        while True:
            data = await websocket.receive_json()
            
            if data["type"] == "user_message":
                user_message = data["content"]
                logger.info(f"收到用户消息: {user_message}")
                
                # 生成文本回复
                await websocket.send_json({
                    "type": "status",
                    "content": "正在生成回复..."
                })
                
                bot_response = await bot.generate_response(user_message)
                
                await websocket.send_json({
                    "type": "text_response",
                    "content": bot_response
                })
                
                # 生成语音
                await websocket.send_json({
                    "type": "status",
                    "content": "正在生成语音..."
                })
                
                audio_data = await bot.text_to_speech(bot_response)
                
                if audio_data:
                    # 保存音频文件
                    audio_filename = f"audio_{connection_id}_{uuid.uuid4().hex[:8]}.mp3"
                    audio_path = f"static/{audio_filename}"
                    
                    os.makedirs("static", exist_ok=True)
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)
                    
                    await websocket.send_json({
                        "type": "audio_response",
                        "content": bot_response,
                        "audio_url": f"/static/{audio_filename}"
                    })
                else:
                    await websocket.send_json({
                        "type": "status",
                        "content": "语音生成失败"
                    })
                    
    except WebSocketDisconnect:
        logger.info(f"WebSocket连接断开: {connection_id}")
    finally:
        if connection_id in active_connections:
            del active_connections[connection_id]


# 静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")


if __name__ == "__main__":
    import uvicorn
    
    logger.info("启动Groq AI + Edge TTS 语音机器人Web演示")
    uvicorn.run(app, host="0.0.0.0", port=8000)
