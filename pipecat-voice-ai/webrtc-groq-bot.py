#!/usr/bin/env python3

"""
WebRTC版本的Groq AI语音机器人

使用WebRTC传输，不需要Daily.co账户
"""

import asyncio
import os
import sys

from dotenv import load_dotenv
from loguru import logger

from pipecat.frames.frames import EndFrame, TTSSpeakFrame
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineTask
from pipecat.services.openai.tts import OpenAITTSService
from pipecat.transports.services.webrtc import WebRTCTransport, WebRTCTransportParams

load_dotenv(override=True)


async def main():
    """主函数"""
    logger.info("启动WebRTC版本的Groq AI语音机器人")
    
    # 检查必要的环境变量
    groq_api_key = os.getenv("GROQ_API_KEY")
    
    if not groq_api_key:
        logger.error("缺少GROQ_API_KEY环境变量")
        sys.exit(1)

    # 配置WebRTC传输
    transport = WebRTCTransport(
        params=WebRTCTransportParams(
            audio_out_enabled=True,
            audio_in_enabled=False,  # 暂时只输出音频
            video_out_enabled=False,
            video_in_enabled=False,
        )
    )

    # 配置TTS服务 - 使用OpenAI兼容的Groq API
    tts = OpenAITTSService(
        api_key=groq_api_key,
        base_url="https://api.groq.com/openai/v1",
        voice="alloy",
    )

    # 创建管道
    pipeline = Pipeline([tts, transport.output()])
    task = PipelineTask(pipeline)

    # 当客户端连接时播放欢迎消息
    @transport.event_handler("on_client_connected")
    async def on_client_connected(transport, client):
        logger.info(f"客户端已连接: {client}")
        await task.queue_frames([
            TTSSpeakFrame("你好！我是基于Groq AI的语音助手。欢迎使用WebRTC版本！"), 
            EndFrame()
        ])

    @transport.event_handler("on_client_disconnected")
    async def on_client_disconnected(transport, client, reason):
        logger.info(f"客户端已断开: {client}, 原因: {reason}")

    # 运行机器人
    runner = PipelineRunner()
    
    logger.info("WebRTC机器人已启动，等待客户端连接...")
    logger.info("请在浏览器中访问WebRTC客户端页面")
    
    await runner.run(task)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("机器人已停止")
    except Exception as e:
        logger.error(f"运行错误: {e}")
        sys.exit(1)
