#!/usr/bin/env python3

"""
修复版本的中文语音对话机器人

解决连接问题和API错误
"""

import argparse
import asyncio
import os
import sys
from typing import Dict

import uvicorn
from dotenv import load_dotenv
from fastapi import BackgroundTasks, FastAPI
from fastapi.responses import RedirectResponse
from loguru import logger

from pipecat.frames.frames import EndFrame, TTSSpeakFrame
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineTask
from pipecat.transports.base_transport import TransportParams
from pipecat.transports.network.small_webrtc import SmallWebRTCTransport
from pipecat.transports.network.webrtc_connection import SmallWebRTCConnection
from pipecat.audio.vad.silero import SileroVADAnalyzer

load_dotenv(override=True)


async def run_simple_bot(transport, args, handle_sigint: bool):
    """运行简化版语音机器人 - 只播放欢迎消息"""
    logger.info("🤖 启动简化版语音机器人")

    # 创建简单的管道 - 只有传输，不包含STT/LLM/TTS
    pipeline = Pipeline([
        transport.input(),
        transport.output(),
    ])

    task = PipelineTask(pipeline)

    # 当客户端连接时播放欢迎消息
    @transport.event_handler("on_client_connected")
    async def on_client_connected(transport, client):
        logger.info(f"✅ 客户端已连接: {client}")
        # 直接播放文本消息（不使用TTS）
        await task.queue_frames([
            TTSSpeakFrame("连接成功！语音机器人已准备就绪。"),
            EndFrame()
        ])

    @transport.event_handler("on_client_disconnected")
    async def on_client_disconnected(transport, client):
        logger.info(f"❌ 客户端已断开连接: {client}")

    runner = PipelineRunner(handle_sigint=handle_sigint)
    await runner.run(task)


def create_simple_app():
    """创建简化的FastAPI应用"""
    from pipecat_ai_small_webrtc_prebuilt.frontend import SmallWebRTCPrebuiltUI
    
    app = FastAPI(title="修复版中文语音机器人")
    
    # 存储WebRTC连接
    pcs_map: Dict[str, SmallWebRTCConnection] = {}
    
    # 挂载前端界面
    app.mount("/client", SmallWebRTCPrebuiltUI)
    
    @app.get("/", include_in_schema=False)
    async def root_redirect():
        return RedirectResponse(url="/client/")
    
    @app.post("/api/offer")
    async def offer(request: dict, background_tasks: BackgroundTasks):
        """处理WebRTC offer请求"""
        logger.info(f"📡 收到WebRTC offer请求: {request.get('pc_id', 'unknown')}")
        
        pc_id = request.get("pc_id")
        
        try:
            if pc_id and pc_id in pcs_map:
                # 重用现有连接
                logger.info(f"🔄 重用现有连接: {pc_id}")
                pipecat_connection = pcs_map[pc_id]
                await pipecat_connection.restart(
                    sdp=request["sdp"],
                    type=request["type"],
                    restart_pc=request.get("restart_pc", False),
                )
            else:
                # 创建新连接
                logger.info(f"🆕 创建新的WebRTC连接")
                pipecat_connection = SmallWebRTCConnection()
                await pipecat_connection.initialize(sdp=request["sdp"], type=request["type"])
                
                @pipecat_connection.event_handler("closed")
                async def handle_disconnected(webrtc_connection):
                    logger.info(f"🔌 WebRTC连接已关闭: {webrtc_connection.pc_id}")
                    pcs_map.pop(webrtc_connection.pc_id, None)
                
                # 创建传输参数 - 简化配置
                params = TransportParams(
                    audio_out_enabled=True,
                    audio_in_enabled=True,
                    vad_enabled=True,
                    vad_analyzer=SileroVADAnalyzer(),
                )
                
                # 创建传输并启动机器人
                transport = SmallWebRTCTransport(params=params, webrtc_connection=pipecat_connection)
                
                # 创建模拟的args对象
                class Args:
                    def __init__(self):
                        self.transport = "webrtc"
                        self.host = "0.0.0.0"
                        self.port = 7860
                
                args = Args()
                background_tasks.add_task(run_simple_bot, transport, args, False)
            
            answer = pipecat_connection.get_answer()
            pcs_map[answer["pc_id"]] = pipecat_connection
            
            logger.info(f"✅ WebRTC连接已建立: {answer['pc_id']}")
            return answer
            
        except Exception as e:
            logger.error(f"❌ WebRTC连接失败: {e}")
            raise
    
    return app


def main():
    """主函数"""
    logger.info("🚀 启动修复版中文语音对话机器人")
    
    # 检查SSL证书
    ssl_certfile = "/etc/letsencrypt/live/su.guiyunai.fun/fullchain.pem"
    ssl_keyfile = "/etc/letsencrypt/live/su.guiyunai.fun/privkey.pem"
    
    if not os.path.exists(ssl_certfile) or not os.path.exists(ssl_keyfile):
        logger.error("❌ SSL证书文件不存在")
        logger.error(f"证书文件: {ssl_certfile}")
        logger.error(f"密钥文件: {ssl_keyfile}")
        sys.exit(1)
    
    logger.info("✅ SSL证书检查通过")
    
    # 创建FastAPI应用
    app = create_simple_app()
    
    # 启动HTTPS服务器
    logger.info("🔒 启动HTTPS WebRTC服务器")
    logger.info("📱 访问地址: https://su.guiyunai.fun:7860")
    logger.info("🔧 这是修复版本，专注于解决连接问题")
    
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=7860,
            ssl_keyfile=ssl_keyfile,
            ssl_certfile=ssl_certfile,
            log_level="info"
        )
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器运行错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
