#!/bin/bash

# Groq AI + Edge TTS 语音机器人部署脚本
# 适用于Ubuntu/Debian系统

set -e

echo "🚀 开始部署 Groq AI + Edge TTS 语音机器人..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "检测到root用户，建议使用普通用户部署"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检查系统
check_system() {
    log_info "检查系统环境..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装，请先安装Python3"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    log_info "Python版本: $PYTHON_VERSION"
    
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 未安装，请先安装pip3"
        exit 1
    fi
    
    log_success "系统环境检查通过"
}

# 安装系统依赖
install_system_deps() {
    log_info "安装系统依赖..."
    
    if command -v apt-get &> /dev/null; then
        # Ubuntu/Debian
        sudo apt-get update
        sudo apt-get install -y ffmpeg portaudio19-dev python3-venv
    elif command -v yum &> /dev/null; then
        # CentOS/RHEL
        sudo yum install -y ffmpeg portaudio-devel python3-venv
    elif command -v dnf &> /dev/null; then
        # Fedora
        sudo dnf install -y ffmpeg portaudio-devel python3-venv
    else
        log_warning "无法识别的包管理器，请手动安装 ffmpeg 和 portaudio-devel"
    fi
    
    log_success "系统依赖安装完成"
}

# 创建虚拟环境
create_venv() {
    log_info "创建Python虚拟环境..."
    
    if [ -d "env" ]; then
        log_warning "虚拟环境已存在，是否重新创建？"
        read -p "(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf env
        else
            log_info "使用现有虚拟环境"
            return
        fi
    fi
    
    python3 -m venv env
    log_success "虚拟环境创建完成"
}

# 安装Python依赖
install_python_deps() {
    log_info "安装Python依赖..."
    
    source env/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装依赖
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
    else
        log_info "requirements.txt不存在，安装基础依赖..."
        pip install pipecat-ai[local] edge-tts fastapi uvicorn websockets python-dotenv loguru openai pydub
    fi
    
    log_success "Python依赖安装完成"
}

# 配置环境变量
setup_env() {
    log_info "配置环境变量..."
    
    if [ ! -f ".env" ]; then
        log_info "创建.env配置文件..."
        cat > .env << EOF
# Groq AI API密钥 (必需)
GROQ_API_KEY=your_groq_api_key_here

# Daily.co API密钥 (可选，用于WebRTC功能)
DAILY_API_KEY=your_daily_api_key_here
EOF
        log_warning "请编辑 .env 文件，填入您的API密钥"
        log_info "Groq API密钥获取地址: https://console.groq.com/"
    else
        log_info ".env文件已存在"
    fi
    
    # 创建static目录
    mkdir -p static
    
    log_success "环境配置完成"
}

# 测试安装
test_installation() {
    log_info "测试安装..."
    
    source env/bin/activate
    
    # 测试TTS功能
    log_info "测试Edge TTS功能..."
    if python simple-tts-test.py; then
        log_success "Edge TTS测试通过"
    else
        log_error "Edge TTS测试失败"
        return 1
    fi
    
    # 检查音频文件
    if [ -f "test_output.mp3" ]; then
        log_success "音频文件生成成功: test_output.mp3"
    fi
    
    if [ -f "test_output.wav" ]; then
        log_success "音频文件生成成功: test_output.wav"
    fi
    
    log_success "安装测试通过"
}

# 启动服务
start_service() {
    log_info "启动语音机器人服务..."
    
    source env/bin/activate
    
    # 检查端口是否被占用
    if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null ; then
        log_warning "端口8000已被占用"
        read -p "是否终止现有进程并重启？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            pkill -f "python web-demo.py" || true
            sleep 2
        else
            log_info "请手动处理端口冲突"
            return 1
        fi
    fi
    
    log_info "启动Web服务器..."
    log_info "访问地址: http://localhost:8000"
    log_info "按 Ctrl+C 停止服务"
    
    python web-demo.py
}

# 主函数
main() {
    echo "========================================"
    echo "  Groq AI + Edge TTS 语音机器人部署"
    echo "========================================"
    echo
    
    check_root
    check_system
    install_system_deps
    create_venv
    install_python_deps
    setup_env
    test_installation
    
    echo
    log_success "🎉 部署完成！"
    echo
    echo "下一步："
    echo "1. 编辑 .env 文件，填入您的Groq API密钥"
    echo "2. 运行 './deploy.sh start' 启动服务"
    echo "3. 访问 http://localhost:8000 使用语音机器人"
    echo
}

# 处理命令行参数
case "${1:-}" in
    "start")
        start_service
        ;;
    "test")
        test_installation
        ;;
    *)
        main
        ;;
esac
