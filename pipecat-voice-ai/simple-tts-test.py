#!/usr/bin/env python3

"""
简单的TTS测试 - 生成音频文件

验证Edge TTS是否能正常工作
"""

import asyncio
import io
import os

import edge_tts
from loguru import logger


async def test_edge_tts():
    """测试Edge TTS"""
    logger.info("开始测试Edge TTS")
    
    text = "你好！这是Edge TTS语音合成测试。如果生成了音频文件，说明TTS工作正常！"
    voice = "zh-CN-XiaoxiaoNeural"
    
    try:
        # 使用Edge TTS生成语音
        communicate = edge_tts.Communicate(text, voice)
        audio_data = b""
        
        async for chunk in communicate.stream():
            if chunk["type"] == "audio":
                audio_data += chunk["data"]
        
        if audio_data:
            # 保存为MP3文件
            with open("test_output.mp3", "wb") as f:
                f.write(audio_data)
            
            logger.info(f"音频文件已生成: test_output.mp3 ({len(audio_data)} 字节)")
            
            # 尝试转换为WAV
            try:
                from pydub import AudioSegment
                
                # 从MP3数据创建AudioSegment
                audio = AudioSegment.from_mp3(io.BytesIO(audio_data))
                
                # 转换为16kHz单声道
                audio = audio.set_frame_rate(16000).set_channels(1)
                
                # 保存为WAV
                audio.export("test_output.wav", format="wav")
                
                logger.info("WAV文件已生成: test_output.wav")
                
                # 获取PCM数据
                pcm_data = audio.raw_data
                logger.info(f"PCM数据长度: {len(pcm_data)} 字节")
                
                return True
                
            except Exception as e:
                logger.error(f"音频转换错误: {e}")
                return False
                
        else:
            logger.error("没有生成音频数据")
            return False
            
    except Exception as e:
        logger.error(f"Edge TTS错误: {e}")
        return False


async def main():
    """主函数"""
    success = await test_edge_tts()
    
    if success:
        logger.info("✅ Edge TTS测试成功！")
        logger.info("现在可以集成到Pipecat中了")
    else:
        logger.error("❌ Edge TTS测试失败")


if __name__ == "__main__":
    asyncio.run(main())
