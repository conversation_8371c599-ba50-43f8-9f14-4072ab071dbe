#!/usr/bin/env python3

"""
Edge TTS测试 - 使用免费的微软Edge TTS

验证TTS功能是否正常工作
"""

import asyncio
import os
import sys

from dotenv import load_dotenv
from loguru import logger

from pipecat.frames.frames import EndFrame, TTSSpeakFrame
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineTask
from pipecat.transports.local.audio import LocalAudioTransport, LocalAudioTransportParams

# 导入Edge TTS服务
try:
    from pipecat.services.edge.tts import EdgeTTSService
except ImportError:
    logger.error("Edge TTS服务不可用，请检查pipecat版本")
    sys.exit(1)

load_dotenv(override=True)


async def main():
    """主函数"""
    logger.info("启动Edge TTS测试")

    # 配置本地音频传输
    transport = LocalAudioTransport(
        params=LocalAudioTransportParams(
            audio_out_enabled=True,
            audio_in_enabled=False
        )
    )

    # 配置Edge TTS服务 - 使用中文语音
    tts = EdgeTTSService(
        voice="zh-CN-XiaoxiaoNeural"  # 中文女声
    )

    # 创建管道
    pipeline = Pipeline([tts, transport.output()])
    task = PipelineTask(pipeline)

    # 运行机器人
    runner = PipelineRunner()
    
    logger.info("开始播放测试音频...")
    
    # 直接播放测试消息
    await task.queue_frames([
        TTSSpeakFrame("你好！这是微软Edge TTS语音合成测试。如果你能听到这段中文语音，说明配置成功了！"), 
        EndFrame()
    ])
    
    await runner.run(task)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试已停止")
    except Exception as e:
        logger.error(f"测试错误: {e}")
        sys.exit(1)
